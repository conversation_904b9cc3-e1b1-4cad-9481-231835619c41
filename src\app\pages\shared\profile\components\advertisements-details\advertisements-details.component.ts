import { Component, Input, ChangeDetectorRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormGroup,
  FormsModule,
  FormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { Modal } from 'bootstrap';
import { ProfileService } from '../../services/profile.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

// Define interfaces for the tree structure

@Component({
  selector: 'app-advertisements-details',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule],
  templateUrl: './advertisements-details.component.html',
  styleUrl: './advertisements-details.component.scss',
})
export class AdvertisementsDetailsComponent implements OnInit {
  @Input() user: any = {};
  cities: any[] = [];
  areas: any[] = [];
  selectedCityId: any;
  selectedCityName: string;
  selectedAreaName: string;
  isLoadingCities = false;

  Form: FormGroup;

  // New items for the modal forms
  newLocation: string = '';
  newSpecialization: string = '';

  // References to the modals
  private locationModal: Modal | null = null;
  private specializationModal: Modal | null = null;

  constructor(
    private profileService: ProfileService,
    private propertyService: PropertyService,
    private cd: ChangeDetectorRef,
    private fb: FormBuilder,
    private translate: TranslateService
  ) {
    // Initialize the form
    this.Form = this.fb.group({
      cityId: [''],
      areaId: [''],
    });
  }

  ngOnInit(): void {
    this.updatedScopes = [...(this.user.specializationScopes || [])];
    if (this.user && this.user.locations) {
      this.locations = this.user.locations;
    }
    // Load cities when component initializes
    this.loadCities();
  }

  // Helper functions for translation
  getLocalizedCityName(city: any): string {
    if (!city) return '';

    const currentLang = this.translate.currentLang || 'en';

    if (currentLang === 'ar') {
      // ترجمة أسماء المدن الشائعة
      const cityTranslations: { [key: string]: string } = {
        'Cairo': 'القاهرة',
        'Giza': 'الجيزة',
        'Alexandria': 'الإسكندرية',
        'Luxor': 'الأقصر',
        'Aswan': 'أسوان',
        'Sharm El Sheikh': 'شرم الشيخ',
        'Hurghada': 'الغردقة',
        'Mansoura': 'المنصورة',
        'Tanta': 'طنطا',
        'Ismailia': 'الإسماعيلية',
        'Suez': 'السويس',
        'Port Said': 'بورسعيد',
        'Damietta': 'دمياط',
        'Kafr El Sheikh': 'كفر الشيخ',
        'Beni Suef': 'بني سويف',
        'Minya': 'المنيا',
        'Asyut': 'أسيوط',
        'Sohag': 'سوهاج',
        'Qena': 'قنا',
        'Red Sea': 'البحر الأحمر',
        'New Valley': 'الوادي الجديد',
        'Matrouh': 'مطروح',
        'North Sinai': 'شمال سيناء',
        'South Sinai': 'جنوب سيناء',
        'Fayyum': 'الفيوم',
        'Beheira': 'البحيرة',
        'Dakahlia': 'الدقهلية',
        'Sharqia': 'الشرقية',
        'Monufia': 'المنوفية',
        'Gharbia': 'الغربية',
        'Qalyubia': 'القليوبية',
        '10th of Ramadan': 'العاشر من رمضان',
        'Badr': 'بدر',
        'New Cairo': 'القاهرة الجديدة',
        'Sheikh Zayed': 'الشيخ زايد',
        'New Administrative Capital': 'العاصمة الإدارية الجديدة',
        'Al Azbakeyah': 'الأزبكية'
      };

      const englishName = city.name_en || city.en || '';
      const translatedName = cityTranslations[englishName];

      if (translatedName) {
        return translatedName;
      }

      return city.name_ar || city.ar || englishName;
    } else {
      return city.name_en || city.en || city.name_ar || city.ar || '';
    }
  }

  getLocalizedAreaName(area: any): string {
    if (!area) return '';

    const currentLang = this.translate.currentLang || 'en';

    if (currentLang === 'ar') {
      return area.name_ar || area.ar || area.name_en || area.en || '';
    } else {
      return area.name_en || area.en || area.name_ar || area.ar || '';
    }
  }

  //***************************************************** */
  //***************************************************** */
  // ******************location modal***********************

  // Current selected locations
  locations: any[] = [];

  openLocationModal() {
    this.newLocation = '';
    const modalElement = document.getElementById('addLocationModal');
    if (modalElement) {
      this.locationModal = new Modal(modalElement);
      this.locationModal.show();
    }
  }

  saveLocation() {
    const selectedAreaId = this.Form.value.areaId;
    const selectedArea = this.areas.find((area) => area.id === selectedAreaId);

    const existingAreaIds = this.user.areas.map((area: any) => area.id);

    if (!existingAreaIds.includes(selectedAreaId)) {
      existingAreaIds.push(selectedAreaId);
    }

    this.profileService
      .updateAdvertisements(this.user.id, { areaIds: existingAreaIds })
      .subscribe({
        next: (response) => {
          // console.log(locationData);
          localStorage.setItem('currentUser', JSON.stringify(response.data));
          console.log(response);
          this.showSuccessMessage(this.getTranslatedMessage('Location saved successfully', 'تم حفظ الموقع بنجاح'));

          if (
            !this.user.areas.some((area: any) => area.id === selectedAreaId)
          ) {
            this.user.areas.push({
              id: selectedAreaId,
              name_en: selectedArea
                ? selectedArea.name_en
                : this.selectedAreaName,
            });
          }

          if (this.locationModal) {
            this.locationModal.hide();
          }
          this.cd.detectChanges();
        },
        error: (err) => {
          this.showErrorMessage(err);
        },
      });
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cd.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }
  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;

    // Check if Form is initialized before patching
    if (this.Form) {
      this.Form.patchValue({
        cityId: cityId,
      });
    }

    // Load areas for the selected city
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;

    // Check if Form is initialized before patching
    if (this.Form) {
      this.Form.patchValue({
        areaId: areaId,
      });
    }
  }
  removeLocation(index: number, areaId: number) {
    if (index >= 0 && index < this.user.areas.length) {
      let currentAreaIds = Array.isArray(this.Form.value.areaId)
        ? this.Form.value.areaId
        : [this.Form.value.areaId];

      let updatedAreaIds = currentAreaIds.filter((id: number) => id !== areaId);
      this.Form.patchValue({ areaId: updatedAreaIds });
      this.user.areas.splice(index, 1);
      this.openLocationModal();

    }
  }
  //***************************************************** */
  //***************************************************** */
  // ****************specialization modal***********************

  searchTerm: string = '';
  formChanged: boolean = false;
  originalSpecializations: any[] = [];
  updatedScopes: any[] = [];

  staticScopes = [
    {
      specialization_scope: 'purchase_sell_outside_compound',
      specializations: [
        'purchasing_sell_residential_outside_compound',
        'purchasing_sell_national_housing_projects_outside_compound',
        'purchasing_sell_administrative_commercial_units_outside_compound',
        'purchasing_sell_industrial_and_warehousing_outside_compound',
        'purchasing_sell_lands_and_ready_projects_outside_compound',
        'purchasing_sell_villas_and_buildings_outside_compound'
      ],
      expanded: false,
      selected: false,
    },
    // {
    //   specialization_scope: 'purchase_sell_inside_compound',
    //   specializations: [
    //     'purchasing_sell_residential_inside_compound',
    //     'purchasing_sell_villas_inside_compound',
    //     'purchasing_sell_administrative_commercial_units_inside_compound'
    //   ],
    //   expanded: false,
    //   selected: false,
    // },
    {
      specialization_scope: 'primary_inside_compound',
      specializations: [
        'purchasing_sell_residential_inside_compound',
        'purchasing_sell_villas_inside_compound',
        'purchasing_sell_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'resale_inside_compound',
      specializations: [
        'purchasing_sell_residential_inside_compound',
        'purchasing_sell_villas_inside_compound',
        'purchasing_sell_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'rentals_outside_compound',
      specializations: [
        'rent_residential_inside_compound',
        'rent_hotel_Units_inside_compound',
        'rent_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'rentals_inside_compound',
      specializations: [
        'rent_residential_outside_compound',
        'rent_national_housing_projects_compound',
        'rent_administrative_commercial_units_outside_compound',
        'rent_industrial_and_warehousing_outside_compound',
        'rent_hotel_units_outside_compound'
      ],
      expanded: false,
      selected: false,
    },
  ];

  // Open specialization modal
  openSpecializationModal() {
     this.searchTerm = '';
     const modalElement = document.getElementById('addSpecializationModal');
    if (modalElement) {
      this.specializationModal = new Modal(modalElement);
      this.specializationModal.show();
    }
  }

  hasScope(scopeName: string): boolean {
    return this.updatedScopes.some(
      (item) => item.specialization_scope === scopeName
    );
  }

  hasSpecialization(scopeName: string, specialization: string): boolean {
    if (!this.user || !this.user.specializationScopes) return false;

    return this.user.specializationScopes.some(
      (item: any) =>
        item.specialization_scope === scopeName &&
        item.specialization === specialization
    );
  }

  onScopeChange(scopeName: string, specialization: string, event: Event): void {
    console.log(specialization);
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      const exists = this.updatedScopes.some(
        (item) =>
          item.specialization_scope === scopeName &&
          item.specialization === specialization
      );

      if (!exists) {
        this.updatedScopes.push({
          specialization_scope: scopeName,
          specialization: specialization,
        });
      }
    } else {
      this.updatedScopes = this.updatedScopes.filter(
        (item) =>
          !(
            item.specialization_scope === scopeName &&
            item.specialization === specialization
          )
      );
    }

    this.formChanged = true;
  }

  saveChanges() {
    let payload: any = { specializationScopes: {} };

    console.log('Updated scopes:', this.updatedScopes);

    this.updatedScopes.forEach((scope) => {
      const key = scope.specialization_scope;
      if (!key) return;
      if (!payload.specializationScopes[key]) {
        payload.specializationScopes[key] = [];
      }

      payload.specializationScopes[key].push(scope.specialization);
    });

    console.log('Payload:', payload);

    this.profileService.updateAdvertisements(this.user.id, payload).subscribe({
      next: (res) => {
        this.formChanged = false;
        localStorage.setItem('currentUser', JSON.stringify(res.data));
        console.log('Payload:', payload);
        console.log('Response:', res);

        this.showSuccessMessage(this.getTranslatedMessage('Saved successfully', 'تم الحفظ بنجاح'));
        this.specializationModal?.hide();
      },
      error: (err) => {
        this.showErrorMessage(err);
      },
    });
  }

  private showSuccessMessage(message: string): void {
    const currentLang = this.translate.currentLang || 'en';
    const isArabic = currentLang === 'ar';

    Swal.fire({
      icon: 'success',
      title: isArabic ? 'نجح' : 'Success',
      text: message,
      confirmButtonText: isArabic ? 'موافق' : 'OK',
      customClass: {
        popup: isArabic ? 'rtl-popup' : '',
        title: isArabic ? 'rtl-title' : '',
        htmlContainer: isArabic ? 'rtl-content' : '',
        confirmButton: 'btn btn-success'
      },
      buttonsStyling: false,
      timer: 3000,
      timerProgressBar: true
    });
  }

  private showErrorMessage(error: any): void {
    const currentLang = this.translate.currentLang || 'en';
    const isArabic = currentLang === 'ar';

    Swal.fire({
      icon: 'error',
      title: isArabic ? 'خطأ' : 'Error',
      text: error.error?.message || (isArabic ? 'حدث خطأ غير متوقع' : 'An unexpected error occurred'),
      confirmButtonText: isArabic ? 'موافق' : 'OK',
      customClass: {
        popup: isArabic ? 'rtl-popup' : '',
        title: isArabic ? 'rtl-title' : '',
        htmlContainer: isArabic ? 'rtl-content' : '',
        confirmButton: 'btn btn-danger'
      },
      buttonsStyling: false
    });
  }

  specializationDisplayMap: { [key: string]: { en: string; ar: string } } = {
    'purchasing_sell_residential_outside_compound': {
      en: 'Apartments - duplexes - penthouses - roof - basements - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - روف - بدروم - استوديو'
    },
    'purchasing_sell_national_housing_projects_outside_compound': {
      en: 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
      ar: 'الإسكان القومي - الإسكان المختلط - إسكان الشباب - جنة مصر - دار مصر - سكن مصر'
    },
    'purchasing_sell_administrative_commercial_units_outside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'purchasing_sell_industrial_and_warehousing_outside_compound': {
      en: 'Factories - Warehouses - Industrial Lands - Warehouse Lands',
      ar: 'مصانع - مخازن - أراضي صناعية - أراضي مخازن'
    },
    'purchasing_sell_lands_and_ready_projects_outside_compound': {
      en: 'Administrative & Commercial Lands - Commercial Administrative Malls',
      ar: 'أراضي إدارية وتجارية - مولات إدارية تجارية'
    },
    'purchasing_sell_villas_and_buildings_outside_compound': {
      en: 'Villas - Full Buildings - Residential Lands - Concrete Structure',
      ar: 'فيلات - مباني كاملة - أراضي سكنية - هيكل خرساني'
    },
    'purchasing_sell_residential_inside_compound': {
      en: 'Apartments - duplexes - penthouses - i villa - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - آي فيلا - استوديو'
    },
    'purchasing_sell_villas_inside_compound': {
      en: 'Villas - Standalone - town house - twin house',
      ar: 'فيلات - منفصلة - تاون هاوس - توين هاوس'
    },
    'purchasing_sell_administrative_commercial_units_inside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'rent_residential_inside_compound': {
      en: 'Apartments - duplexes - penthouses - i villa - villas - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - آي فيلا - فيلات - استوديو'
    },
    'rent_hotel_Units_inside_compound': {
      en: 'Hotel Units',
      ar: 'وحدات فندقية'
    },
    'rent_administrative_commercial_units_inside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'rent_residential_outside_compound': {
      en: 'Apartments - duplexes - penthouses - roof - basements - villas - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - روف - بدروم - فيلات - استوديو'
    },
    'rent_national_housing_projects_compound': {
      en: 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
      ar: 'الإسكان القومي - الإسكان المختلط - إسكان الشباب - جنة مصر - دار مصر - سكن مصر'
    },
    'rent_administrative_commercial_units_outside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات - مباني كاملة'
    },
    'rent_industrial_and_warehousing_outside_compound': {
      en: 'Factories - Warehouses',
      ar: 'مصانع - مخازن'
    },
    'rent_hotel_units_outside_compound': {
      en: 'Hotel Units',
      ar: 'وحدات فندقية'
    }
  };

  // Specialization scope translations
  specializationScopeMap: { [key: string]: { en: string; ar: string } } = {
    'purchase_sell_outside_compound': {
      en: 'Purchase/Sell Outside Compound',
      ar: 'شراء/بيع خارج الكمبوند'
    },
    'primary_inside_compound': {
      en: 'Primary Inside Compound',
      ar: 'أولي داخل الكمبوند'
    },
    'resale_inside_compound': {
      en: 'Resale Inside Compound',
      ar: 'إعادة بيع داخل الكمبوند'
    },
    'rentals_outside_compound': {
      en: 'Rentals Outside Compound',
      ar: 'إيجار خارج الكمبوند'
    },
    'rentals_inside_compound': {
      en: 'Rentals Inside Compound',
      ar: 'إيجار داخل الكمبوند'
    }
  };

  // Helper function for quick translation
  getTranslatedMessage(englishText: string, arabicText: string): string {
    const currentLang = this.translate.currentLang || 'en';
    return currentLang === 'ar' ? arabicText : englishText;
  }

  // Get translated specialization scope text
  getSpecializationScopeText(key: string): string {
    const currentLang = this.translate.currentLang || 'en';
    const isArabic = currentLang === 'ar';

    if (this.specializationScopeMap[key]) {
      return isArabic ? this.specializationScopeMap[key].ar : this.specializationScopeMap[key].en;
    }

    return key; // fallback to key if not found
  }

  // Get translated specialization display text
  getSpecializationDisplayText(key: string): string {
    const currentLang = this.translate.currentLang || 'en';
    const isArabic = currentLang === 'ar';

    if (this.specializationDisplayMap[key]) {
      return isArabic ? this.specializationDisplayMap[key].ar : this.specializationDisplayMap[key].en;
    }

    return key; // fallback to key if not found
  }

}

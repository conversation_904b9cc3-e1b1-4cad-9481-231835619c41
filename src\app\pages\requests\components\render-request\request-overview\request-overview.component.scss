.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}

.position-relative {
  position: relative;

  .position-absolute {
    position: absolute;
  }
}

// RTL Support for Request Overview
.rtl-layout {
  direction: rtl;
  text-align: right;

  .rtl-section-header {
    text-align: right;
    font-family: 'Noto Ku<PERSON> Arabic', sans-serif;

    i {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  .rtl-content {
    direction: rtl;
    text-align: right;

    .rtl-field {
      text-align: right;

      .rtl-label {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: right;
      }

      .rtl-value {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: right;
      }
    }
  }

  // Badge styles for RTL
  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
    direction: rtl;
  }

  // Fix icon spacing in Arabic - General
  .d-flex.align-items-center {
    i {
      margin-right: 0 !important;
      margin-left: 0.75rem !important;
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  // Ensure all FontAwesome icons are visible in Arabic
  i.fas, i.far, i.fab {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  // Fix spacing for user info section specifically
  .d-flex.align-items-center.mb-2 {
    i {
      margin-right: 0 !important;
      margin-left: 1rem !important;
      font-size: 1rem !important;
    }

    span {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }
  }

  // Fix spacing in section headers
  h6.text-primary {
    i {
      margin-right: 0 !important;
      margin-left: 0.75rem !important;
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  // Fix spacing in media gallery
  .rounded.p-3 {
    .d-flex.align-items-center {
      span {
        i {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }
    }
  }

  // Fix spacing for user information section
  .d-flex.align-items-center.text-gray-600.mb-2 {
    i {
      margin-right: 0 !important;
      margin-left: 1rem !important;
      font-size: 1rem !important;
    }
  }

  // Fix badge spacing in RTL
  .rtl-badge {
    font-family: 'Markazi Text', sans-serif !important;
    direction: rtl !important;
  }

  // Modal RTL support
  .modal {
    direction: rtl;

    .modal-header {
      text-align: right;

      .modal-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
      }

      .btn-close {
        margin-left: auto;
        margin-right: 0;
      }
    }

    .modal-body {
      text-align: center;
    }

    .modal-footer {
      justify-content: flex-start;

      .btn {
        font-family: 'Hacen Liner Screen St', sans-serif;
      }
    }
  }

  // Media gallery RTL
  .d-flex {
    &.align-items-center {
      flex-direction: row-reverse;

      span {
        text-align: right;
      }
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  h6 {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  span {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}

// ===== COMPREHENSIVE RESPONSIVE DESIGN =====

// Base card styling
.card {
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }

  .card-body {
    padding: 2rem;
  }
}

// Section headers
h6.text-primary {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  background-color: #f8f9fa !important;

  i {
    font-size: 1rem;
    margin-right: 0.5rem;
  }
}

// Content sections
.rounded.p-3 {
  background-color: #ffffff;
  border: 1px solid #e4e6ef;
  border-radius: 0.5rem;
  padding: 1.5rem !important;

  .mb-2 {
    margin-bottom: 1rem !important;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;

    span {
      &.text-gray-600 {
        font-size: 0.875rem;
        font-weight: 500;
        color: #5e6278;
        min-width: 140px;
        margin-bottom: 0.25rem;
      }

      &.text-gray-800 {
        font-size: 0.9rem;
        font-weight: 600;
        color: #181c32;
        flex: 1;
      }
    }

    .badge {
      font-size: 0.8rem;
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
    }
  }
}

// Image styling
img {
  border-radius: 0.375rem;
  transition: transform 0.2s ease;

  &.cursor-pointer:hover {
    transform: scale(1.05);
  }
}

// ===== RESPONSIVE BREAKPOINTS =====

// Large screens (1200px+) - Normal layout
@media screen and (min-width: 1200px) {
  .card .card-body {
    padding: 2.5rem;
  }

  h6.text-primary {
    font-size: 1.1rem;
    padding: 1.25rem 1.75rem;
  }

  .rounded.p-3 {
    padding: 2rem !important;

    .mb-2 span.text-gray-600 {
      min-width: 160px;
      font-size: 0.9rem;
    }

    .mb-2 span.text-gray-800 {
      font-size: 1rem;
    }
  }
}

// Medium-large screens (992px - 1199px)
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .card .card-body {
    padding: 2rem;
  }

  h6.text-primary {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }

  .rounded.p-3 {
    padding: 1.75rem !important;

    .mb-2 span.text-gray-600 {
      min-width: 150px;
      font-size: 0.875rem;
    }

    .mb-2 span.text-gray-800 {
      font-size: 0.95rem;
    }
  }
}

// Medium screens (768px - 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .card .card-body {
    padding: 1.5rem;
  }

  h6.text-primary {
    font-size: 0.95rem;
    padding: 0.875rem 1.25rem;

    i {
      font-size: 0.9rem;
    }
  }

  .rounded.p-3 {
    padding: 1.5rem !important;

    .mb-2 {
      flex-direction: column;
      align-items: flex-start;

      span.text-gray-600 {
        min-width: auto;
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
        width: 100%;
      }

      span.text-gray-800 {
        font-size: 0.9rem;
        width: 100%;
      }

      .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
        margin-top: 0.25rem;
      }
    }
  }

  // Image gallery adjustments
  .d-flex.gap-2 {
    flex-wrap: wrap;
    gap: 0.5rem !important;
  }

  img {
    height: 25px !important;
    width: 40px !important;
  }

  // RTL spacing fixes for medium screens
  .d-flex.align-items-center.mb-2 {
    i {
      margin-right: 0 !important;
      margin-left: 0.75rem !important;
    }
  }
}

// Small-medium screens (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .card .card-body {
    padding: 1.25rem;
  }

  h6.text-primary {
    font-size: 0.9rem;
    padding: 0.75rem 1rem;

    i {
      font-size: 0.85rem;
      margin-right: 0.4rem;
    }
  }

  .rounded.p-3 {
    padding: 1.25rem !important;

    .mb-2 {
      flex-direction: column;
      margin-bottom: 0.875rem !important;

      span.text-gray-600 {
        font-size: 0.75rem;
        margin-bottom: 0.4rem;
        font-weight: 600;
      }

      span.text-gray-800 {
        font-size: 0.85rem;
      }

      .badge {
        font-size: 0.7rem;
        padding: 0.35rem 0.5rem;
      }
    }
  }

  // Image adjustments
  img {
    height: 22px !important;
    width: 35px !important;
  }

  .d-flex.gap-2 {
    gap: 0.4rem !important;
  }
}

// Small screens (425px - 575px)
@media screen and (min-width: 425px) and (max-width: 575px) {
  .card .card-body {
    padding: 1rem;
  }

  h6.text-primary {
    font-size: 0.85rem;
    padding: 0.6rem 0.875rem;

    i {
      font-size: 0.8rem;
      margin-right: 0.3rem;
    }
  }

  .rounded.p-3 {
    padding: 1rem !important;

    .mb-2 {
      flex-direction: column;
      margin-bottom: 0.75rem !important;

      span.text-gray-600 {
        font-size: 0.7rem;
        margin-bottom: 0.3rem;
        font-weight: 600;
      }

      span.text-gray-800 {
        font-size: 0.8rem;
      }

      .badge {
        font-size: 0.65rem;
        padding: 0.3rem 0.45rem;
      }
    }
  }

  // Image adjustments
  img {
    height: 20px !important;
    width: 32px !important;
  }

  .d-flex.gap-2 {
    gap: 0.3rem !important;
  }
}

// Extra small screens (≤424px)
@media screen and (max-width: 424px) {
  .card .card-body {
    padding: 0.875rem;
  }

  h6.text-primary {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;

    i {
      font-size: 0.75rem;
      margin-right: 0.25rem;
    }
  }

  .rounded.p-3 {
    padding: 0.875rem !important;

    .mb-2 {
      flex-direction: column;
      margin-bottom: 0.6rem !important;

      span.text-gray-600 {
        font-size: 0.65rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
      }

      span.text-gray-800 {
        font-size: 0.75rem;
      }

      .badge {
        font-size: 0.6rem;
        padding: 0.25rem 0.4rem;
      }
    }
  }

  // Image adjustments
  img {
    height: 18px !important;
    width: 28px !important;
  }

  .d-flex.gap-2 {
    gap: 0.25rem !important;
    flex-wrap: wrap;
  }

  // Stack images vertically on very small screens
  .d-flex.align-items-center {
    flex-direction: column !important;
    align-items: flex-start !important;

    span {
      margin-bottom: 0.5rem !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
    }
  }
}

// ===== RTL RESPONSIVE ENHANCEMENTS =====

:host-context(html[lang="ar"]) {
  // RTL responsive adjustments for medium screens
  @media screen and (min-width: 768px) and (max-width: 991px) {
    h6.text-primary {
      text-align: right;
      font-family: 'Noto Kufi Arabic', sans-serif;

      i {
        margin-right: 0;
        margin-left: 0.4rem;
      }
    }

    .rounded.p-3 .mb-2 {
      span.text-gray-600 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }

      span.text-gray-800 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }

      .badge {
        font-family: 'Markazi Text', sans-serif;
      }
    }
  }

  // RTL responsive adjustments for small-medium screens
  @media screen and (min-width: 576px) and (max-width: 767px) {
    h6.text-primary {
      text-align: right;
      font-family: 'Noto Kufi Arabic', sans-serif;

      i {
        margin-right: 0;
        margin-left: 0.4rem;
      }
    }

    .rounded.p-3 .mb-2 {
      span.text-gray-600 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }

      span.text-gray-800 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }
    }
  }

  // RTL responsive adjustments for small screens
  @media screen and (min-width: 425px) and (max-width: 575px) {
    h6.text-primary {
      text-align: right;
      font-family: 'Noto Kufi Arabic', sans-serif;

      i {
        margin-right: 0;
        margin-left: 0.3rem;
      }
    }

    .rounded.p-3 .mb-2 {
      span.text-gray-600 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }

      span.text-gray-800 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }
    }
  }

  // RTL responsive adjustments for extra small screens
  @media screen and (max-width: 424px) {
    h6.text-primary {
      text-align: right;
      font-family: 'Noto Kufi Arabic', sans-serif;

      i {
        margin-right: 0;
        margin-left: 0.25rem;
      }
    }

    .rounded.p-3 .mb-2 {
      span.text-gray-600 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }

      span.text-gray-800 {
        text-align: right;
        font-family: 'Markazi Text', sans-serif;
      }
    }

    .d-flex.align-items-center {
      span {
        text-align: right !important;
      }
    }
  }
}

// ===== MODAL RESPONSIVE DESIGN =====

.modal {
  .modal-dialog {
    @media screen and (max-width: 576px) {
      margin: 1rem 0.5rem;
      max-width: calc(100% - 1rem);
    }

    .modal-content {
      border-radius: 0.75rem;

      .modal-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e4e6ef;

        .modal-title {
          font-size: 1rem;
          font-weight: 600;
        }

        @media screen and (max-width: 576px) {
          padding: 0.75rem 1rem;

          .modal-title {
            font-size: 0.9rem;
          }
        }
      }

      .modal-body {
        padding: 1.5rem;

        img {
          max-width: 100%;
          height: auto;
          border-radius: 0.5rem;
        }

        video {
          max-width: 100%;
          height: auto;
          border-radius: 0.5rem;
        }

        @media screen and (max-width: 576px) {
          padding: 1rem;

          img, video {
            max-height: 200px;
          }
        }
      }

      .modal-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid #e4e6ef;

        @media screen and (max-width: 576px) {
          padding: 0.75rem 1rem;
        }
      }
    }
  }
}

// RTL Modal support
:host-context(html[lang="ar"]) {
  .modal {
    .modal-content {
      .modal-header {
        text-align: right;

        .modal-title {
          font-family: 'Noto Kufi Arabic', sans-serif;
        }

        .btn-close {
          margin-left: auto;
          margin-right: 0;
        }
      }

      .modal-body {
        text-align: center;
      }

      .modal-footer {
        justify-content: flex-start;

        .btn {
          font-family: 'Markazi Text', sans-serif;
        }
      }
    }
  }
}

import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { RequestService } from '../../services/request.service';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-render-request',
  templateUrl: './render-request.component.html',
  styleUrls: ['./render-request.component.scss'],
})
export class RenderRequestComponent implements OnInit, OnDestroy {

  userId :number;
  brokerId :number ;
  request: any = null;
  requestId: string | null = null;
  canReply: boolean = true;
  private routeSub: Subscription | null = null;
  user: any;


  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute,
    private router: Router,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.userId = this.user?.id;
    this.brokerId = this.user?.brokerId;
    this.routeSub = this.route.paramMap.subscribe((params: ParamMap) => {
      this.requestId = params.get('id');
      console.log('RenderRequestComponent - Request ID:', this.requestId);
      if (this.requestId) {
        this.requestService.setRequestId(this.requestId);
        this.requestService.clearRequest(); // Clear previous request data
        this.request = null; // Reset local request
        this.cd.markForCheck();
        this.getRequest();
      } else {
        console.error('RenderRequestComponent - No request ID found in route');
        Swal.fire('Invalid request ID.', '', 'error');
      }
    });
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

  getRequest() {
    if (this.requestId) {
      this.requestService.getRequestById(this.requestId).subscribe({
        next: (response: any) => {
          this.request = response.data;
          console.log('RenderRequestComponent - Request Data:', this.request);
          console.log('RenderRequestComponent - Request canReply:', this.canReply);
          this.requestService.setRequest(this.request);
          this.checkReplyAvailability();
          this.cd.markForCheck();
        },
        error: (error: any) => {
          console.error('RenderRequestComponent - Error fetching request:', error);
          this.cd.markForCheck();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  checkReplyAvailability(): boolean {
    this.canReply = this.request?.user?.id !== this.userId;
    console.log('RenderRequestComponent - canReply:', this.canReply);
    this.cd.markForCheck();
    return this.canReply;
  }

  updateRequestStatus(requestId: number, userId: number, status: string) {
    const payload = {
      userId,
      status,
    };

    this.requestService.updateRequestStatus(requestId, payload).subscribe({
      next: (response) => {
        console.log('Status updated successfully:', response);
        this.request.status = status;
        this.cd.markForCheck();
        Swal.fire('Request status updated successfully!', '', 'success');
      },
      error: (error) => {
        console.error('Error updating status:', error);
      },
    });
  }

  archiveRequest(requestId: number, brokerId: number) {
    this.requestService.archiveRequest(requestId, brokerId).subscribe({
      next: (response) => {
        console.log('Request archived successfully:', response);
        this.cd.markForCheck();

        Swal.fire({
          title: 'Request archived successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/requests/received']);
          }
        });
      },
      error: (error) => {
        console.error('Error archiving request:', error);
        Swal.fire('Failed to archive request', '', 'error');
      },
    });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'LOADING': 'جاري التحميل...',
        'N_A': 'غير متوفر',
        'START_PROCESSING': 'بدء المعالجة',
        'ARCHIVE': 'أرشفة',
        'REPLIES': 'الردود',
        'OVERVIEW': 'نظرة عامة',
        'UNITS_RECOMMENDATION': 'توصيات الوحدات',
        'HISTORY': 'السجل',
        'NEW': 'جديد',
        'IN_PROCESSING': 'قيد المعالجة',
        'FINISHED': 'مكتمل',
        'CLIENT_NAME': 'اسم العميل',
        'REQUEST_DATE': 'تاريخ الطلب',
        'PHONE_NUMBER': 'رقم الهاتف'
      },
      'en': {
        'LOADING': 'Loading...',
        'N_A': 'N/A',
        'START_PROCESSING': 'Start Processing',
        'ARCHIVE': 'Archive',
        'REPLIES': 'Replies',
        'OVERVIEW': 'Overview',
        'UNITS_RECOMMENDATION': 'Units Recommendation',
        'HISTORY': 'History',
        'NEW': 'New',
        'IN_PROCESSING': 'In Processing',
        'FINISHED': 'Finished',
        'CLIENT_NAME': 'Client Name',
        'REQUEST_DATE': 'Request Date',
        'PHONE_NUMBER': 'Phone Number'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Helper method to get translated status
  getTranslatedStatus(status: string): string {
    if (!status) return this.getTranslatedText('N_A');

    const statusKey = status.toUpperCase().replace(' ', '_');
    return this.getTranslatedText(statusKey);
  }

  // Helper method to get translated title
  getTranslatedTitle(title: string): string {
    if (!title) return this.getTranslatedText('LOADING');

    // Common title translations
    const titleTranslations: any = {
      'ar': {
        'Buy Commercial Units': 'شراء وحدات تجارية',
        'Sell Commercial Units': 'بيع وحدات تجارية',
        'Rent Commercial Units': 'تأجير وحدات تجارية',
        'Buy Residential Units': 'شراء وحدات سكنية',
        'Sell Residential Units': 'بيع وحدات سكنية',
        'Rent Residential Units': 'تأجير وحدات سكنية',
        'Buy Land': 'شراء أرض',
        'Sell Land': 'بيع أرض',
        'Rent Land': 'تأجير أرض',
        'Buy Villa': 'شراء فيلا',
        'Sell Villa': 'بيع فيلا',
        'Rent Villa': 'تأجير فيلا',
        'Buy Apartment': 'شراء شقة',
        'Sell Apartment': 'بيع شقة',
        'Rent Apartment': 'تأجير شقة',
        'Purchasing Penthouses': 'شراء بنتهاوس',
        'Selling Penthouses': 'بيع بنتهاوس',
        'Renting Penthouses': 'تأجير بنتهاوس',
        'Buy Penthouse': 'شراء بنتهاوس',
        'Sell Penthouse': 'بيع بنتهاوس',
        'Rent Penthouse': 'تأجير بنتهاوس',
        'Purchasing Duplex': 'شراء دوبلكس',
        'Selling Duplex': 'بيع دوبلكس',
        'Renting Duplex': 'تأجير دوبلكس',
        'Purchasing Studio': 'شراء استوديو',
        'Selling Studio': 'بيع استوديو',
        'Renting Studio': 'تأجير استوديو',
        'Purchasing Office': 'شراء مكتب',
        'Selling Office': 'بيع مكتب',
        'Renting Office': 'تأجير مكتب',
        'Property Request': 'طلب عقار',
        'Real Estate Request': 'طلب عقاري',
        'Investment Opportunity': 'فرصة استثمارية'
      },
      'en': {
        'Buy Commercial Units': 'Buy Commercial Units',
        'Sell Commercial Units': 'Sell Commercial Units',
        'Rent Commercial Units': 'Rent Commercial Units',
        'Buy Residential Units': 'Buy Residential Units',
        'Sell Residential Units': 'Sell Residential Units',
        'Rent Residential Units': 'Rent Residential Units',
        'Buy Land': 'Buy Land',
        'Sell Land': 'Sell Land',
        'Rent Land': 'Rent Land',
        'Buy Villa': 'Buy Villa',
        'Sell Villa': 'Sell Villa',
        'Rent Villa': 'Rent Villa',
        'Buy Apartment': 'Buy Apartment',
        'Sell Apartment': 'Sell Apartment',
        'Rent Apartment': 'Rent Apartment',
        'Purchasing Penthouses': 'Purchasing Penthouses',
        'Selling Penthouses': 'Selling Penthouses',
        'Renting Penthouses': 'Renting Penthouses',
        'Buy Penthouse': 'Buy Penthouse',
        'Sell Penthouse': 'Sell Penthouse',
        'Rent Penthouse': 'Rent Penthouse',
        'Purchasing Duplex': 'Purchasing Duplex',
        'Selling Duplex': 'Selling Duplex',
        'Renting Duplex': 'Renting Duplex',
        'Purchasing Studio': 'Purchasing Studio',
        'Selling Studio': 'Selling Studio',
        'Renting Studio': 'Renting Studio',
        'Purchasing Office': 'Purchasing Office',
        'Selling Office': 'Selling Office',
        'Renting Office': 'Renting Office',
        'Property Request': 'Property Request',
        'Real Estate Request': 'Real Estate Request',
        'Investment Opportunity': 'Investment Opportunity'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();

    // Try exact match first
    if (titleTranslations[currentLang]?.[title]) {
      return titleTranslations[currentLang][title];
    }

    // Try partial matches for dynamic titles
    const lowerTitle = title.toLowerCase();

    if (currentLang === 'ar') {
      if (lowerTitle.includes('buy') && lowerTitle.includes('commercial')) {
        return 'شراء وحدات تجارية';
      } else if (lowerTitle.includes('sell') && lowerTitle.includes('commercial')) {
        return 'بيع وحدات تجارية';
      } else if (lowerTitle.includes('rent') && lowerTitle.includes('commercial')) {
        return 'تأجير وحدات تجارية';
      } else if (lowerTitle.includes('buy') && lowerTitle.includes('residential')) {
        return 'شراء وحدات سكنية';
      } else if (lowerTitle.includes('sell') && lowerTitle.includes('residential')) {
        return 'بيع وحدات سكنية';
      } else if (lowerTitle.includes('rent') && lowerTitle.includes('residential')) {
        return 'تأجير وحدات سكنية';
      } else if (lowerTitle.includes('buy')) {
        return 'طلب شراء';
      } else if (lowerTitle.includes('sell')) {
        return 'طلب بيع';
      } else if (lowerTitle.includes('rent')) {
        return 'طلب تأجير';
      } else if (lowerTitle.includes('property') || lowerTitle.includes('real estate')) {
        return 'طلب عقاري';
      }
    }

    // Return original title if no translation found
    return title;
  }
}

// Shared RTL styles for user account types components

// General header improvements (applies to both LTR and RTL)
.card-header {
  .d-flex.align-items-center {
    gap: 10px !important;

    .header-icon,
    .symbol {
      flex-shrink: 0;
    }

    .header-content {
      flex-grow: 1;
    }
  }
}

// SweetAlert RTL Support
.swal2-rtl {
  direction: rtl;
  text-align: right;

  .swal2-title {
    font-family: 'Noto <PERSON> Arabic', sans-serif;
    font-size: 1.5rem;
    line-height: 1.4;
    text-align: right;
  }

  .swal2-content {
    font-family: 'Hacen Liner Screen', sans-serif;
    text-align: right;
    line-height: 1.6;
  }

  .swal2-input,
  .swal2-textarea {
    font-family: 'Hacen Liner Screen', sans-serif;
    text-align: right;
    direction: rtl;
  }

  .swal2-actions {
    flex-direction: row-reverse;

    .swal2-confirm,
    .swal2-cancel {
      font-family: 'Hacen Liner Screen', sans-serif;
      margin-left: 0.5rem;
      margin-right: 0;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.swal2-title-rtl {
  font-family: 'Noto <PERSON>', sans-serif !important;
  font-size: 1.5rem !important;
  line-height: 1.4 !important;
  text-align: right !important;
}

.swal2-html-rtl {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  text-align: right !important;
  direction: rtl !important;
}

.swal2-input-rtl {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  text-align: right !important;
  direction: rtl !important;
}

// Global RTL improvements for forms
[dir="rtl"] {
  .form-label {
    text-align: right;
    font-family: 'Hacen Liner Screen', sans-serif;

    &.required::after {
      content: " *";
      color: #dc3545;
      margin-right: 0.25rem;
      margin-left: 0;
    }
  }

  .form-control,
  .form-select {
    text-align: right;
    direction: rtl;
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen', sans-serif;

    i {
      margin-left: 0.5rem;
      margin-right: 0;
    }
  }

  // Card improvements
  .card-header {
    .card-title {
      text-align: right;

      h3, h4, h5 {
        font-family: 'Noto Kufi Arabic', sans-serif;
        text-align: right;
        line-height: 1.4;
      }

      span, p {
        font-family: 'Hacen Liner Screen', sans-serif;
        text-align: right;
        line-height: 1.6;
      }
    }

    // Add gap to all header flex containers
    .d-flex.align-items-center {
      gap: 10px !important;
    }

    .card-toolbar {
      flex-direction: row-reverse;

      .btn {
        margin-left: 0.5rem;
        margin-right: 0;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  // Table improvements
  .table {
    thead th {
      text-align: right;
      font-family: 'Noto Kufi Arabic', sans-serif;
    }

    tbody td {
      text-align: right;
      font-family: 'Hacen Liner Screen', sans-serif;
    }
  }

  // Modal improvements
  .modal-header {
    text-align: right;

    .modal-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      text-align: right;
    }
  }

  .modal-body {
    text-align: right;
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  .modal-footer {
    flex-direction: row-reverse;

    .btn {
      margin-left: 0.5rem;
      margin-right: 0;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  // Dropdown improvements
  .dropdown-menu {
    text-align: right;

    .dropdown-item {
      text-align: right;
      font-family: 'Hacen Liner Screen', sans-serif;

      i {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }

  // Badge improvements
  .badge {
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  // Search input improvements
  .position-relative {
    .position-absolute {
      &.end-0 {
        right: 0 !important;
        left: auto !important;
      }

      &.me-3 {
        margin-right: 1rem !important;
        margin-left: 0 !important;
      }
    }
  }

  .form-control {
    &.pe-10 {
      padding-right: 2.5rem;
      padding-left: 0.75rem;
    }
  }
}

// Arabic font classes
.arabic-title {
  font-family: 'Noto Kufi Arabic', sans-serif !important;
  font-weight: 700;
  line-height: 1.4;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  line-height: 1.6;
}

.arabic-input {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  text-align: right;
  direction: rtl;
}

// Header specific improvements for Arabic
[dir="rtl"] {
  .card-header {
    .d-flex.align-items-center {
      gap: 10px !important;

      &.flex-row-reverse {
        .symbol {
          margin-left: 0 !important;
          margin-right: 0 !important;
        }

        div {
          text-align: right !important;

          h3 {
            font-family: 'Noto Kufi Arabic', sans-serif !important;
            font-size: 1.8rem !important;
            font-weight: 700 !important;
            line-height: 1.3 !important;
            margin-bottom: 0.5rem !important;
            color: #2d3748 !important;
          }

          p, span {
            font-family: 'Hacen Liner Screen', sans-serif !important;
            font-size: 1.1rem !important;
            line-height: 1.5 !important;
            color: #718096 !important;
          }
        }
      }
    }
  }

  // Page title improvements
  .page-title {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 2rem !important;
    font-weight: 700 !important;
    line-height: 1.3 !important;
    text-align: right !important;
    margin-bottom: 0.5rem !important;
  }

  .page-subtitle {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-size: 1.2rem !important;
    line-height: 1.5 !important;
    text-align: right !important;
    color: #718096 !important;
  }

  // Icon improvements
  .fas, .far, .fab {
    &.text-primary,
    &.text-success,
    &.text-warning,
    &.text-danger {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }
  }
}

<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-5" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'"
  [class.received-requests-page]="router.url.includes('/received')">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <!-- Large and Medium screens layout (Tablet and Desktop) -->
        <div class="d-none d-md-flex justify-content-between align-items-center mb-2"
          [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
          <!-- Title on the left/right -->
          <div class="d-flex my-6">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1"
              [class.rtl-title]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('REQUESTS') }}
            </h1>
          </div>

          <!-- Search in the center -->
          <div class="d-flex justify-content-center flex-grow-1 my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off"
              (input)="onSearchChanged($event)" [class.rtl-search]="translationService.getCurrentLanguage() === 'ar'">

              <input type="text" class="form-control form-control-flush bg-light border rounded-pill ps-3"
                [class.rtl-input]="translationService.getCurrentLanguage() === 'ar'" name="search" value=""
                placeholder="{{ getTranslatedText('SEARCH') }}" data-kt-search-element="input" />
            </form>
          </div>

          <!-- Filter button at the end -->
          <div class="d-flex flex-column my-4">
            <app-filter-drop-down (filtersApplied)="onFiltersChanged($event)">
            </app-filter-drop-down>
          </div>
        </div>

        <!-- Small screens layout (Mobile only) -->
        <div class="d-flex d-md-none justify-content-between align-items-start flex-wrap mb-2"
          [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
          <div class="d-flex my-6">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1"
              [class.rtl-title]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('REQUESTS') }}
            </h1>
          </div>

          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off"
              (input)="onSearchChanged($event)" [class.rtl-search]="translationService.getCurrentLanguage() === 'ar'">

              <input type="text" class="form-control form-control-flush bg-light border rounded-pill ps-3"
                [class.rtl-input]="translationService.getCurrentLanguage() === 'ar'" name="search" value=""
                placeholder="{{ getTranslatedText('SEARCH') }}" data-kt-search-element="input" />
            </form>
          </div>

          <div class="d-flex flex-column my-4">
            <app-filter-drop-down (filtersApplied)="onFiltersChanged($event)">
            </app-filter-drop-down>
          </div>
        </div>
      </div>
    </div>

    <div class="d-flex h-50px mb-2" [class.rtl-tabs]="translationService.getCurrentLanguage() === 'ar'"
      *ngIf="user?.role !== 'client'">
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap"
        [class.rtl-nav]="translationService.getCurrentLanguage() === 'ar'">
        <li class="nav-item" *ngIf="user?.role === 'broker'">
          <a class="nav-link pt-0 pb-0 btn btn-active-dark-blue btn-light-primary"
            [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="/requests/received"
            routerLinkActive="active">
            {{ getTranslatedText('RECEIVED_REQUESTS') }}
            <span class="badge badge-light-danger" [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-2]="translationService.getCurrentLanguage() === 'ar'" *ngIf="newRequestsCount > 0">{{
              newRequestsCount }}</span>
          </a>
        </li>
        <li class="nav-item" *ngIf="user?.role !== 'client'">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="/requests/sent"
            routerLinkActive="active">
            {{ getTranslatedText('SENT_REQUESTS') }}
          </a>
        </li>
      </ul>
    </div>

    <div class="card-body pb-0" [class.pt-3]="user?.role !== 'client'" [class.pt-0]="user?.role === 'client'">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
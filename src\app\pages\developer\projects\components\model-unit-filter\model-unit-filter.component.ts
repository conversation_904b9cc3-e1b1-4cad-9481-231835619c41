import { ChangeDetectorRef, Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-model-unit-filter',
  templateUrl: './model-unit-filter.component.html',
  styleUrl: './model-unit-filter.component.scss'
})
export class ModelUnitFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType: '',
    compoundType: '',
    unitAreaFrom:'',
    unitAreaTo:'',
    area:'',
    view:'',
    priceFrom:'',
    priceTo:'',
  };

  constructor(
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.loadUnitTypes();
    this.loadAreas();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

 status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  views: { key: string; value: string }[] = [
    { key: 'WATER VIEW', value: 'water_view' },
    { key: 'GARDENS AND LANDSCAPE', value: 'gardens_and_landscape' },
    { key: 'STREET', value: 'street' },
    { key: 'ENTERTAINMENT AREA', value: 'entertainment_area' },
    { key: 'GARDEN', value: 'garden' },
    { key: 'MAIN STREET', value: 'main_street' },
    { key: 'SQUARE', value: 'square' },
    { key: 'SIDE STREET', value: 'side_street' },
    { key: 'REAR VIEW', value: 'rear_view' },
  ];

  // toggleDropdown() {
  //   this.isOpen = !this.isOpen;
  // }

  apply() {
    // this.isOpen = false;
    this.filtersApplied.emit(this.filter);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // loadCities(): void {
  //   this.propertyService.getCities().subscribe({
  //     next: (response) => {
  //       if (response && response.data) {
  //         this.cities = response.data;
  //       } else {
  //         console.warn('No cities data in response');
  //         this.cities = [];
  //       }
  //     },
  //     error: (err) => {
  //       console.error('Error loading cities:', err);
  //     },
  //     complete: () => {
  //       this.cdr.detectChanges();
  //     },
  //   });
  // }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'AREA': 'المنطقة',
        'UNIT_AREA': 'مساحة الوحدة',
        'VIEW': 'الإطلالة',
        'PRICE': 'السعر',
        'UNIT_TYPE': 'نوع الوحدة',
        'SELECT': 'اختر',
        'ENTER_UNIT_AREA': 'أدخل مساحة الوحدة',
        'ENTER_PRICE': 'أدخل السعر',
        'APPLY': 'تطبيق',
        'WATER_VIEW': 'إطلالة مائية',
        'GARDENS_AND_LANDSCAPE': 'حدائق ومناظر طبيعية',
        'STREET': 'شارع',
        'ENTERTAINMENT_AREA': 'منطقة ترفيهية',
        'GARDEN': 'حديقة',
        'MAIN_STREET': 'شارع رئيسي',
        'SQUARE': 'ميدان',
        'SIDE_STREET': 'شارع جانبي',
        'REAR_VIEW': 'إطلالة خلفية',
        'FROM' : 'من',
        'TO': 'الي',
        'OUTSIDE_COMPOUND': 'داخل كمبوند',
        'INSIDE_COMPOUND': 'خارج كمبوند',
        'VILLAGE': 'قريه سياحيه',
      },
      'en': {
        'AREA': 'Area',
        'UNIT_AREA': 'Unit Area',
        'VIEW': 'View',
        'PRICE': 'Price',
        'UNIT_TYPE': 'Unit Type',
        'SELECT': 'Select',
        'ENTER_UNIT_AREA': 'Enter unit area',
        'ENTER_PRICE': 'Enter price',
        'APPLY': 'Apply',
        'WATER_VIEW': 'Water View',
        'GARDENS_AND_LANDSCAPE': 'Gardens and Landscape',
        'STREET': 'Street',
        'ENTERTAINMENT_AREA': 'Entertainment Area',
        'GARDEN': 'Garden',
        'MAIN_STREET': 'Main Street',
        'SQUARE': 'Square',
        'SIDE_STREET': 'Side Street',
        'REAR_VIEW': 'Rear View',
        'FROM' : 'From',
        'TO': 'To',
        'OUTSIDE_COMPOUND': 'Outside Compound',
        'INSIDE_COMPOUND': 'Inside Compound',
        'VILLAGE': 'Village',
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Get translated view name
  getViewName(view: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    if (currentLang === 'ar') {
      return this.getTranslatedText(view.key.replace(/\s+/g, '_'));
    }
    return view.key;
  }

  // Get area name based on language
  getAreaName(area: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (area.name_ar || area.name_en) : area.name_en;
  }

}

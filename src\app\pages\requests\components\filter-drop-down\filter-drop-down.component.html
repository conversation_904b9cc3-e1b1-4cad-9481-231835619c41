<div class="dropdown" [class.show]="isOpen" [class.rtl-dropdown]="translationService.getCurrentLanguage() === 'ar'"
  (clickOutside)="closeDropdown()">
  <button class="btn btn-sm btn-light-dark-blue cursor-pointer dropdown-toggle"
    [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
    [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
    [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'" (click)="toggleDropdown($event)"
    aria-expanded="isOpen">
    <i class="fa-solid fa-filter fs-4"></i>
    {{ selectedMainOption || getTranslatedText('FILTERS') }}
  </button>

  <div class="dropdown-menu p-0" [class.dropdown-menu-left]="translationService.getCurrentLanguage() !== 'ar'"
    [class.show]="isOpen" [class.rtl-menu]="translationService.getCurrentLanguage() === 'ar'">
    <div class="card shadow-sm" style="min-width: 350px;"
      [class.rtl-card]="translationService.getCurrentLanguage() === 'ar'">


      <div class="card-body pt-0" [class.rtl-body]="translationService.getCurrentLanguage() === 'ar'">
        <form [formGroup]="form" (ngSubmit)="onSubmit()"
          [class.rtl-form]="translationService.getCurrentLanguage() === 'ar'">
          <!-- Specialization dropdown -->
          <div class="mb-5" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
            <label for="specializationScope" class="form-label fs-6 fw-semibold text-gray-700"
              [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{
              getTranslatedText('SPECIALIZATION_SCOPE') }}</label>
            <select id="specializationScope" formControlName="specializationScope" class="form-select form-select-solid"
              [class.rtl-select]="translationService.getCurrentLanguage() === 'ar'" data-control="select2"
              data-hide-search="true">
              <option value="">{{ getTranslatedText('SELECT') }}</option>
              <option *ngFor="let option of options.specializationScope" [value]="option">
                {{ getTranslatedOption(option, 'specializationScope') }}
              </option>
            </select>
          </div>

          <!-- Status dropdown -->
          <div class="mb-5" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
            <label for="status" class="form-label fs-6 fw-semibold text-gray-700"
              [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('STATUS')
              }}</label>
            <select id="status" formControlName="status" class="form-select form-select-solid"
              [class.rtl-select]="translationService.getCurrentLanguage() === 'ar'" data-control="select2"
              data-hide-search="true">
              <option value="">{{ getTranslatedText('SELECT') }}</option>
              <option *ngFor="let option of options.status" [value]="option">
                {{ getTranslatedOption(option, 'status') }}
              </option>
            </select>
          </div>

          <!-- Type dropdown -->
          <div class="mb-7" [class.rtl-field]="translationService.getCurrentLanguage() === 'ar'">
            <label for="type" class="form-label fs-6 fw-semibold text-gray-700"
              [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('TYPE')
              }}</label>
            <select id="type" formControlName="type" class="form-select form-select-solid"
              [class.rtl-select]="translationService.getCurrentLanguage() === 'ar'" data-control="select2"
              data-hide-search="true">
              <option value="">{{ getTranslatedText('SELECT') }}</option>
              <option *ngFor="let option of options.type" [value]="option">
                {{ getTranslatedOption(option, 'type') }}
              </option>
            </select>
          </div>

          <!-- Form actions -->
          <div class="d-flex gap-3" [class.justify-content-end]="translationService.getCurrentLanguage() !== 'ar'"
            [class.justify-content-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-actions]="translationService.getCurrentLanguage() === 'ar'">
            <button type="button" class="btn btn-light"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'" (click)="closeDropdown()">
              {{ getTranslatedText('RESET') }}
            </button>
            <button type="submit" class="btn btn-light-dark-blue"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
              <span class="indicator-label">{{ getTranslatedText('APPLY') }}</span>
              <span class="indicator-progress">
                {{ getTranslatedText('LOADING') }}...
                <span class="spinner-border spinner-border-sm align-middle"
                  [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.me-2]="translationService.getCurrentLanguage() === 'ar'"></span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
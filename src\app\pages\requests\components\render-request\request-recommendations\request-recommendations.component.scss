// ===== RTL SUPPORT FOR REQUEST RECOMMENDATIONS =====

// Arabic font support
:host-context(html[lang="ar"]) {
  // Table styling
  .table {
    direction: rtl;
    text-align: right;

    th {
      font-family: 'Noto Kufi Arabic', sans-serif;
      text-align: right !important;
      font-size: 0.9rem;
      font-weight: 600;
    }

    td {
      font-family: 'Markazi Text', sans-serif;
      text-align: right !important;
      font-size: 0.95rem;
    }

    .rtl-table-header {
      direction: rtl;
    }
  }

  // Buttons
  .btn {
    font-family: 'Markazi Text', sans-serif;
    font-weight: 600;

    &.rtl-button {
      direction: rtl;
      text-align: center;

      i {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }
    }
  }

  // Badges
  .badge {
    font-family: 'Markazi Text', sans-serif;
    direction: rtl;

    &.rtl-badge {
      text-align: center;
    }
  }

  // Text elements
  .rtl-text {
    font-family: 'Markazi Text', sans-serif;
    text-align: right;
  }

  // Modal styling
  .modal {
    .rtl-modal-header {
      direction: rtl;
      text-align: right;

      .rtl-modal-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        text-align: right;
      }

      .rtl-close-btn {
        margin-left: auto;
        margin-right: 0;
      }
    }

    .rtl-modal-body {
      direction: rtl;
      text-align: center;

      .rtl-text {
        font-family: 'Markazi Text', sans-serif;
      }

      .rtl-alert {
        font-family: 'Markazi Text', sans-serif;
        text-align: center;
      }
    }
  }

  // Alert styling for no units
  .rtl-alert-container {
    direction: rtl;
    flex-direction: row-reverse;

    .rtl-alert-content {
      text-align: right;

      .rtl-alert-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        text-align: right;
        font-size: 1rem;
      }

      .rtl-alert-message {
        font-family: 'Markazi Text', sans-serif;
        text-align: right;
        font-size: 0.9rem;
      }
    }
  }

  // Pagination
  .pagination {
    direction: rtl;

    .page-link {
      font-family: 'Markazi Text', sans-serif;
    }
  }

  // Form controls
  .form-check-input {
    margin-right: 0;
    margin-left: 0.25rem;
  }
}

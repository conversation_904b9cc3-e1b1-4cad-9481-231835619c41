import { Injectable } from '@angular/core';
import { PROPERTY_TYPES, COMPOUND_TYPES } from '../interfaces/property-types.interface';

@Injectable({
  providedIn: 'root'
})
export class PropertyTranslationService {

  constructor() { }

  /**
   * ترجمة نوع العقار حسب اللغة
   * @param type نوع العقار من الـ API
   * @param language اللغة المطلوبة ('en' أو 'ar')
   * @param maxLength الحد الأقصى لطول النص (افتراضي: 20)
   * @returns النص المترجم
   */
  translatePropertyType(type: string, language: 'en' | 'ar', maxLength: number = 20): string {
    if (!type) return '';

    // Try with original case first
    let translation = PROPERTY_TYPES[type.trim()];

    // If not found, try with lowercase
    if (!translation) {
      const normalizedType = type.trim().toLowerCase();
      translation = PROPERTY_TYPES[normalizedType];
    }

    // If still not found, try with uppercase
    if (!translation) {
      const upperType = type.trim().toUpperCase();
      translation = PROPERTY_TYPES[upperType];
    }

    if (translation) {
      return translation[language].slice(0, maxLength);
    }

    // إذا لم توجد ترجمة، إرجاع النص الأصلي
    return type.slice(0, maxLength);
  }

  /**
   * الحصول على جميع أنواع العقارات المتاحة
   * @returns مصفوفة بجميع أنواع العقارات
   */
  getAllPropertyTypes(): string[] {
    return Object.keys(PROPERTY_TYPES);
  }

  /**
   * التحقق من وجود نوع عقار معين
   * @param type نوع العقار
   * @returns true إذا كان النوع موجود
   */
  isPropertyTypeExists(type: string): boolean {
    const normalizedType = type.trim().toLowerCase();
    return PROPERTY_TYPES.hasOwnProperty(normalizedType);
  }

  /**
   * الحصول على ترجمة نوع العقار بكلا اللغتين
   * @param type نوع العقار
   * @returns كائن يحتوي على الترجمة بالإنجليزية والعربية
   */
  getPropertyTypeTranslations(type: string): { en: string; ar: string } | null {
    if (!type) return null;

    const normalizedType = type.trim().toLowerCase();
    return PROPERTY_TYPES[normalizedType] || null;
  }

  /**
   * ترجمة نوع الكمبوند حسب اللغة
   * @param type نوع الكمبوند من الـ API
   * @param language اللغة المطلوبة ('en' أو 'ar')
   * @param maxLength الحد الأقصى لطول النص (افتراضي: 20)
   * @returns النص المترجم
   */
  translateCompoundType(type: string, language: 'en' | 'ar', maxLength: number = 20): string {
    if (!type) return '';

    // Try with original case first
    let translation = COMPOUND_TYPES[type.trim()];

    // If not found, try with lowercase
    if (!translation) {
      const normalizedType = type.trim().toLowerCase();
      translation = COMPOUND_TYPES[normalizedType];
    }

    // If still not found, try with uppercase
    if (!translation) {
      const upperType = type.trim().toUpperCase();
      translation = COMPOUND_TYPES[upperType];
    }

    if (translation) {
      return translation[language].slice(0, maxLength);
    }

    // إذا لم توجد ترجمة، إرجاع النص الأصلي
    return type.slice(0, maxLength);
  }

  /**
   * الحصول على جميع أنواع الكمبوند المتاحة
   * @returns مصفوفة بجميع أنواع الكمبوند
   */
  getAllCompoundTypes(): string[] {
    return Object.keys(COMPOUND_TYPES);
  }

  /**
   * التحقق من وجود نوع كمبوند معين
   * @param type نوع الكمبوند
   * @returns true إذا كان النوع موجود
   */
  isCompoundTypeExists(type: string): boolean {
    const normalizedType = type.trim().toLowerCase();
    return COMPOUND_TYPES.hasOwnProperty(normalizedType);
  }

  /**
   * الحصول على ترجمة نوع الكمبوند بكلا اللغتين
   * @param type نوع الكمبوند
   * @returns كائن يحتوي على الترجمة بالإنجليزية والعربية
   */
  getCompoundTypeTranslations(type: string): { en: string; ar: string } | null {
    if (!type) return null;

    const normalizedType = type.trim().toLowerCase();
    return COMPOUND_TYPES[normalizedType] || null;
  }
}

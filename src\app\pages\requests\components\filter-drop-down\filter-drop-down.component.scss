// RTL Support for Filter Dropdown
.rtl-dropdown {
  direction: rtl;
  text-align: right;

  .rtl-button {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: center;
  }

  .rtl-menu {
    direction: rtl;
    text-align: right;

    .rtl-card {
      direction: rtl;
      text-align: right;

      .rtl-header {
        text-align: right;

        .rtl-title {
          font-family: 'Noto Kufi Arabic', sans-serif;
          font-weight: bold;
          text-align: right;
        }
      }

      .rtl-body {
        direction: rtl;
        text-align: right;

        .rtl-form {
          direction: rtl;

          .rtl-field {
            text-align: right;

            .rtl-label {
              font-family: 'Hacen Liner Screen St', sans-serif;
              text-align: right;
              display: block;
              margin-bottom: 0.5rem;
            }

            .rtl-select {
              direction: rtl;
              text-align: right;
              font-family: 'Hacen Liner Screen St', sans-serif;

              option {
                direction: rtl;
                text-align: right;
                font-family: 'Hacen Liner Screen St', sans-serif;
              }
            }
          }

          .rtl-actions {
            flex-direction: row-reverse;

            .rtl-button {
              font-family: 'Hacen Liner Screen St', sans-serif;
            }
          }
        }
      }
    }
  }
}

.dropdown-menu-left {
  right: auto !important;
  left: 0 !important;
  transform: translateX(-100%) !important;
}

.dropdown-menu-right {
  left: auto !important;
  right: 0 !important;
  transform: translateX(100%) !important;
}

// Arabic font support
:host-context(html[lang="ar"]) {
  .dropdown {
    direction: rtl;

    .btn {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .card-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
    }

    .form-label {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .form-select {
      font-family: 'Hacen Liner Screen St', sans-serif;
      // direction: rtl;
      text-align: right;

      option {
        font-family: 'Hacen Liner Screen St', sans-serif;
        // direction: rtl;
        text-align: right;
      }
    }
  }
}
// Compact dropdown for all screen sizes
.dropdown {
  .dropdown-menu {
    .card {
      min-width: 280px !important;
      max-width: 320px !important;

      .card-body {
        padding: 1rem !important;

        .mb-5 {
          margin-bottom: 0.75rem !important;
        }

        .mb-7 {
          margin-bottom: 1rem !important;
        }

        .form-label {
          font-size: 0.8rem !important;
          margin-bottom: 0.4rem !important;
        }

        .form-select {
          // padding: 0.5rem 0.75rem !important;
          font-size: 0.85rem !important;
          min-height: 2.2rem !important;
        }

        .d-flex.gap-3 {
          gap: 0.5rem !important;
          margin-top: 0.75rem !important;

          .btn {
            padding: 0.5rem 1rem !important;
            font-size: 0.8rem !important;
          }
        }
      }
    }
  }
}

// Large Desktop (1200px and above)
@media screen and (min-width: 1200px) {

  .dropdown {
    .dropdown-menu {
      .card {
        min-width: 300px !important;
        max-width: 350px !important;

        .card-body {
          padding: 1.125rem !important;

          .form-label {
            font-size: 0.85rem !important;
            margin-bottom: 0.5rem !important;
          }

          .form-select {
            // padding: 0.6rem 0.875rem !important;
            font-size: 0.9rem !important;
            min-height: 2.4rem !important;
          }

          .d-flex.gap-3 {
            .btn {
              padding: 0.6rem 1.2rem !important;
              font-size: 0.85rem !important;
            }
          }
        }
      }
    }
  }
}

// Desktop and Large Tablet (1024px to 1199px)
@media screen and (min-width: 1024px) and (max-width: 1199px) {
  .dropdown {
    .btn {
      font-size: 0.9rem !important;
    }

    .dropdown-menu {
      .card {
        min-width: 270px !important;
        max-width: 300px !important;

        .card-body {
          padding: 1rem !important;

          .form-label {
            font-size: 0.8rem !important;
            margin-bottom: 0.4rem !important;
          }

          .form-select {
            // padding: 0.5rem 0.75rem !important;
            font-size: 0.85rem !important;
            min-height: 2.2rem !important;
          }

          .d-flex.gap-3 {
            .btn {
              padding: 0.5rem 1rem !important;
              font-size: 0.8rem !important;
            }
          }
        }
      }
    }
  }
}

// Tablet (768px to 1023px)
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .dropdown {
    .dropdown-menu {
      .card {
        min-width: 250px !important;
        max-width: 280px !important;

        .card-body {
          padding: 0.875rem !important;

          .form-label {
            font-size: 0.75rem !important;
            margin-bottom: 0.35rem !important;
          }

          .form-select {
            // padding: 0.45rem 0.65rem !important;
            font-size: 0.8rem !important;
            min-height: 2rem !important;
          }

          .d-flex.gap-3 {
            .btn {
              padding: 0.45rem 0.85rem !important;
              font-size: 0.75rem !important;
            }
          }
        }
      }
    }
  }
}

// Small Tablet and Mobile (575px and below)
@media screen and (max-width: 575px) {
  .dropdown {
    .dropdown-menu {
      .card {
        min-width: 175px !important;
        max-width: calc(100vw - 2rem) !important;

        .card-body {
          padding: 0.75rem !important;

          .mb-5 {
            margin-bottom: 0.5rem !important;
          }

          .mb-7 {
            margin-bottom: 0.75rem !important;
          }

          .form-label {
            font-size: 0.7rem !important;
            margin-bottom: 0.25rem !important;
          }

          .form-select {
            // padding: 0.4rem 0.6rem !important;
            font-size: 0.75rem !important;
            min-height: 1.8rem !important;
          }

          .d-flex.gap-3 {
            gap: 0.4rem !important;
            flex-direction: column !important;

            .btn {
              padding: 0.4rem 0.75rem !important;
              font-size: 0.7rem !important;
              width: 100% !important;
            }
          }
        }
      }
    }
  }
}


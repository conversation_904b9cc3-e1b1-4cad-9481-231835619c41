import { environment } from './../../../../../../environments/environment.prod';
import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { AccountTypeMapper } from 'src/app/pages/broker/account-type-mapper';
import { ProfileService } from '../../services/profile.service';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { BaseLoading } from '../../../base-loading/base-loading';
import { ArabicNumbersPipe } from 'src/app/shared/pipes/arabic-numbers.pipe';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-profile-header',
  standalone: true,
  imports: [CommonModule, SharedModule, TranslateModule, ArabicNumbersPipe],
  templateUrl: './profile-header.component.html',
  styleUrl: './profile-header.component.scss',
})
export class ProfileHeaderComponent extends BaseLoading {
  @Input() user: any = {};

  constructor(
    private cd: ChangeDetectorRef,
    private ProfileService: ProfileService,
    private translate: TranslateService,
    private translationService: TranslationService
  ) {
    super();
  }

  capitalizeWords(text: string | null): string {
    if (!text) return '';
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  }

  getAccountTypeBadge(type: string): string {
    return AccountTypeMapper.getAccountTypeBadge(type);
  }

  getTranslatedAccountType(accountType: string): string {
    if (!accountType) return '';

    const currentLang = this.translationService.getSelectedLanguage();
    const upperType = accountType.toUpperCase();

    if (currentLang === 'ar') {
      return this.translate.instant(`ACCOUNT_TYPES.${upperType}`);
    }

    return this.capitalizeWords(accountType);
  }

  getTranslatedRole(role: string): string {
    if (!role) return '';

    const currentLang = this.translationService.getSelectedLanguage();
    const upperRole = role.toUpperCase();

    if (currentLang === 'ar') {
      return this.translate.instant(`USER_ROLES.${upperRole}`);
    }

    return this.capitalizeWords(role);
  }

  onProfileImageChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const image = input.files[0];
      console.log('Selected image:', image);

      this.startLoading();

      const formData = new FormData();
      formData.append('image', image);

      this.ProfileService.updateProfileImage(this.user.id, formData).subscribe({
        next:async (response: any) => {
          localStorage.setItem('currentUser', JSON.stringify(response.data));
          this.stopLoading();
          this.cd.detectChanges();
          await Swal.fire(
            this.translate.instant('PROFILE.PROFILE_IMAGE_UPDATED_SUCCESS'),
            '',
            'success'
          );
          window.location.reload();
        },
        error: (error: any) => {
          this.stopLoading();
          console.error('Error updating profile image:', error);
          Swal.fire(
            this.translate.instant('PROFILE.PROFILE_IMAGE_UPDATE_FAILED'),
            '',
            'error'
          );
        },
      });
    }
  }
}

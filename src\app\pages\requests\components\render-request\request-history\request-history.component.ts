import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RequestService } from '../../../services/request.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-request-history',
  templateUrl: './request-history.component.html',
  styleUrls: ['./request-history.component.scss']
})

export class RequestHistoryComponent implements OnInit {
  id: string | null = null;
  requestId: number | null = null;
  requestHistory: any[] = [];
  isLoading = false;
  requestUserId :any;

  constructor(
    private route: ActivatedRoute,
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.id = this.route.parent?.snapshot.paramMap.get('id') ?? null;
    this.requestUserId = this.route.snapshot.queryParamMap.get('requestUserId');
    this.requestId = this.id ? Number(this.id) : null;

    if (this.requestId !== null) {
      this.loadRequestHistory();
    } else {
      console.error('No valid request ID found in URL');
      this.isLoading = false;
    }
  }

  loadRequestHistory(): void {
    this.isLoading = true;

    if (this.requestId !== null) {
      this.requestService.getRequestHistory(this.requestId).subscribe({
        next: (response: any) => {
          this.requestHistory = response?.data || [];
          this.isLoading = false;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading request history:', error);
          this.isLoading = false;
          this.cd.detectChanges();
        }
      });
    }
  }

  checkAssign(status: string) {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    if (user?.id != this.requestUserId && user?.role === 'broker' && status == 'Assign') {
      return false;
    } return true;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'CREATE': 'إنشاء',
        'ASSIGN': 'تعيين',
        'UPDATE_STATUS': 'تحديث الحالة',
        'REPLY': 'رد',
        'HISTORY': 'السجل',
        'NO_HISTORY': 'لا يوجد سجل متاح',
        'LOADING': 'جاري التحميل...',
        'ERROR_LOADING': 'خطأ في تحميل السجل'
      },
      'en': {
        'CREATE': 'Create',
        'ASSIGN': 'Assign',
        'UPDATE_STATUS': 'Update Status',
        'REPLY': 'Reply',
        'HISTORY': 'History',
        'NO_HISTORY': 'No history available',
        'LOADING': 'Loading...',
        'ERROR_LOADING': 'Error loading history'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Helper method to get translated status
  getTranslatedStatus(status: string): string {
    const statusKey = status.toUpperCase().replace('_', '_');
    return this.getTranslatedText(statusKey);
  }

  // Helper method to get translated description
  getTranslatedDescription(description: string): string {
    if (!description) return '';

    const descriptionTranslations: any = {
      'ar': {
        'Create New Request': 'إنشاء طلب جديد',
        'Update Request Status': 'تحديث حالة الطلب',
        'Assign Request to Broker': 'تعيين الطلب للوسيط',
        'Reply to Request': 'الرد على الطلب',
        'Archive Request': 'أرشفة الطلب',
        'Finish Request': 'إنهاء الطلب',
        'Start Processing': 'بدء المعالجة',
        'Request Created': 'تم إنشاء الطلب',
        'Status Updated': 'تم تحديث الحالة',
        'Broker Assigned': 'تم تعيين الوسيط',
        'Reply Added': 'تم إضافة رد',
        'Request Archived': 'تم أرشفة الطلب',
        'Request Finished': 'تم إنهاء الطلب',
        'By': 'بواسطة',
        'broker': 'وسيط',
        'client': 'عميل',
        'admin': 'مدير',
        'user': 'مستخدم'
      },
      'en': {
        'Create New Request': 'Create New Request',
        'Update Request Status': 'Update Request Status',
        'Assign Request to Broker': 'Assign Request to Broker',
        'Reply to Request': 'Reply to Request',
        'Archive Request': 'Archive Request',
        'Finish Request': 'Finish Request',
        'Start Processing': 'Start Processing',
        'Request Created': 'Request Created',
        'Status Updated': 'Status Updated',
        'Broker Assigned': 'Broker Assigned',
        'Reply Added': 'Reply Added',
        'Request Archived': 'Request Archived',
        'Request Finished': 'Request Finished',
        'By': 'By',
        'broker': 'broker',
        'client': 'client',
        'admin': 'admin',
        'user': 'user'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();

    // Try to find exact match first
    if (descriptionTranslations[currentLang]?.[description]) {
      return descriptionTranslations[currentLang][description];
    }

    // Handle complex descriptions with patterns like "Action By User (role)"
    if (currentLang === 'ar') {
      let translatedDescription = description;

      // Replace common patterns
      for (const key in descriptionTranslations[currentLang]) {
        if (translatedDescription.includes(key)) {
          translatedDescription = translatedDescription.replace(key, descriptionTranslations[currentLang][key]);
        }
      }

      // Handle "By" pattern
      translatedDescription = translatedDescription.replace(/\bBy\b/g, 'بواسطة');

      // Handle role patterns in parentheses
      translatedDescription = translatedDescription.replace(/\(broker\)/g, '(وسيط)');
      translatedDescription = translatedDescription.replace(/\(client\)/g, '(عميل)');
      translatedDescription = translatedDescription.replace(/\(admin\)/g, '(مدير)');
      translatedDescription = translatedDescription.replace(/\(user\)/g, '(مستخدم)');

      return translatedDescription;
    }

    // If no translation found, return original
    return description;
  }
}

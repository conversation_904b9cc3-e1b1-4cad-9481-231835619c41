<!-- <div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div> -->

<div class="card mb-5 mb-xl-10 developers-page" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap"
          [class.flex-row-reverse]="translationService.isRTL()">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">
              {{ getTranslatedText('ACCOUNTS') }}
            </h1>
          </div>

          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <i class="fas fa-search fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y"
                [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"
                [class.d-none]="translationService.isRTL()"></i>
              <input type="text" name="searchInput" [(ngModel)]="searchInput" (ngModelChange)="onSearch($event)"
                class="form-control form-control-flush bg-light border rounded-pill"
                [class.ps-10]="!translationService.isRTL()" [class.pe-3]="translationService.isRTL()"
                [placeholder]="getTranslatedText('SEARCH_USERS')"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'" data-kt-search-element="input" />
            </form>
          </div>

          <div class="d-flex my-4" [class.flex-row-reverse]="translationService.isRTL()">
           <a class="btn btn-sm btn-dark-blue cursor-pointer"
              (click)="openModal()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fa-solid fa-plus" [class.me-2]="!translationService.isRTL()" [class.ms-2]="translationService.isRTL()"></i>
              {{ getTranslatedText('ADD_DEVELOPER') }}
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- <router-outlet></router-outlet> -->

    <div class="table-responsive mb-5" [class.rtl-table]="translationService.isRTL()">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <!-- Checkbox Column - Arabic: First, English: First -->
            <th class="w-25px rounded-start" [class.ps-4]="!translationService.isRTL()"
              [class.pe-4]="translationService.isRTL()" [class.rounded-start]="!translationService.isRTL()"
              [class.rounded-end]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>

            <!-- Developer Name Column - Arabic: Second, English: Second -->
            <th class="min-w-150px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('USER_NAME') }}
            </th>

            <!-- Email Column - Arabic: Third, English: Third -->
            <th class="min-w-140px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('EMAIL') }}
            </th>

            <!-- Phone Column - Arabic: Fourth, English: Fourth -->
            <th class="min-w-120px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('PHONE') }}
            </th>

            <!-- Projects Link Column - Arabic: Sixth, English: Sixth -->
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('ROLE') }}
            </th>

            <!-- Status Column - Arabic: Seventh, English: Seventh -->
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('ACTIVE') }}
            </th>

            <!-- Actions Column - Arabic: Last, English: Last -->
            <!-- <th class="min-w-100px rounded-end" [class.text-end]="!translationService.isRTL()"
              [class.text-start]="translationService.isRTL()" [class.pe-4]="!translationService.isRTL()"
              [class.ps-4]="translationService.isRTL()" [class.rounded-end]="!translationService.isRTL()"
              [class.rounded-start]="translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ getTranslatedText('ACTIONS') }}
            </th> -->
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <!-- Checkbox Column -->
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>

            <!-- Developer Name Column (Image + Name) -->
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-40px symbol-circle me-3">
                  <img *ngIf="row.image" [src]="row.image" alt="" class="w-10 h-10"
                    style="object-fit: cover; border-radius: 50%" />
                  <div *ngIf="!row.image" class="symbol-label bg-light-primary text-primary fw-bold fs-6">
                    {{ row?.fullName?.charAt(0) }}
                  </div>
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ row.fullName }}
                  </a>
                </div>
              </div>
            </td>

            <!-- Email Column -->
            <td>
              <div class="d-flex flex-column">
                <span class="text-gray-800 fw-semibold fs-7 mb-1">{{ row.email }}</span>
                <span class="text-muted fw-semibold fs-7 d-lg-none">{{ row.phone }}</span>
              </div>
            </td>

            <!-- Phone Column -->
            <td class="d-none d-lg-table-cell">
              <span class="text-gray-800 fw-semibold fs-6">{{ row.phone }}</span>
            </td>

            <!-- Role -->
            <td class="d-none d-xl-table-cell">
              <span class="badge badge-light-primary fs-7 fw-bold">{{ row.role }}</span>
            </td>

            <!-- Status -->
            <td class="d-none d-xl-table-cell">
              <span class="badge fs-7 fw-bold" [ngClass]="getStatusBadgeClass(row.isActive)">
                {{ getTranslatedStatus(row.isActive) }}
              </span>
            </td>

            <!-- Actions Column -->
            <!-- <td [class.text-end]="!translationService.isRTL()" [class.text-start]="translationService.isRTL()"
              [class.pe-4]="!translationService.isRTL()" [class.ps-4]="translationService.isRTL()">
              <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: row.developerId }"
                class="btn btn-sm btn-icon btn-light-primary" data-bs-toggle="tooltip"
                [title]="getTranslatedText('VIEW_DEVELOPER')">
                <i class="fas fa-eye fs-6"></i>
              </a>
            </td> -->
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div *ngIf="!loading && rows.length > 0" class="d-flex justify-content-center mt-5 mb-5">
        <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
          (pageChange)="onPageChange($event)"></app-pagination>
      </div>
    </div>
  </div>
</div>

<!-- <router-outlet></router-outlet> -->

<!-- ADD modal -->
<div *ngIf="isModalVisible" class="modal-backdrop">
  <div class="modal-content-custom">
    <div class="card mt-5">
      <div class="card-header border-0 pt-5">
        <div class="d-flex justify-content-between align-items-start w-100">
          <!-- <h3 class="card-title">{{ getTranslatedText('ADD_DEVELOPER') }}</h3> -->
          <button class="btn btn-sm btn-light-danger" (click)="closeModal()">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="card-body py-1">
        <form [formGroup]="accountForm" (ngSubmit)="submitForm()">
          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label required">{{ getTranslatedText('USER_NAME') }}</label>
              <input formControlName="name" class="form-control" />
            </div>
            <div class="col-md-6">
              <label class="form-label">{{ getTranslatedText('EMAIL') }}</label>
              <input formControlName="email" type="email" class="form-control" />
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label required">{{ getTranslatedText('PHONE') }}</label>
              <input formControlName="phone" class="form-control" />
            </div>
            <div class="col-md-6">
              <label class="form-label required">{{ getTranslatedText('ROLE') }}</label>
              <select formControlName="role" class="form-select">
                <option value="">{{ getTranslatedText('SELECT') }}</option>
                <option *ngFor="let role of roles" [value]="role?.name">
                  {{ role?.name | titlecase }}
                </option>
              </select>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label required">{{ getTranslatedText('PASSWORD') }}</label>
              <input formControlName="password" type="password" class="form-control" />
            </div>
            <div class="col-md-6">
              <label class="form-label d-block">{{ getTranslatedText('GENDER') }}</label>

              <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" formControlName="gender" [value]="'male'" id="genderMale"/>
                <label class="form-check-label" for="genderMale">
                  {{ getTranslatedText('MALE') }}
                </label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" formControlName="gender" [value]="'female'" id="genderFemale"/>
                <label class="form-check-label" for="genderFemale">
                  {{ getTranslatedText('FEMALE') }}
                </label>
              </div>
            </div>
          </div>

          <div class="text-end">
            <button type="submit" class="btn btn-dark-blue">
              {{ getTranslatedText('ADD_DEVELOPER') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class HomeService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}`;

   getFeaturedProperties(limit: number = 4, offset: number = 0, filters: any ={}): Observable<any> {
    let params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());

    Object.keys(filters).forEach((key) => {
      const value = filters[key];
      if (value !== null && value !== undefined && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<any>(`${this.apiUrl}/unit/advertisement-shuffle`, { params });
   }

  getExploreLocations(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/unit/unit-available-statistics` );
  }

  makeVisit(): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/visits/log-visit`,{} );
  }

  getAreas(
    cityId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    return this.http.get(`${environment.apiUrl}/location/area`, { params });
  }

  getUnitTypes(): Observable<any> {
    return this.http.get(`${environment.apiUrl}/unit/unit-types`, {});
  }

}

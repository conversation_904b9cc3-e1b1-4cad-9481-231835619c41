import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { UnitsService } from '../../../services/units.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { event } from 'jquery';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-units',
  templateUrl: './units.component.html',
  styleUrl: './units.component.scss',
})

export class UnitsComponent extends BaseGridComponent implements OnInit {
  user: any;
  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;
  selectedUnitIds: number[] = [];

  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitsService,
    private activatedRoute: ActivatedRoute,
    public translationService: TranslationService
  ) {
    super(cd);
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();

    // Get user from localStorage
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params['modelCode']) {
        this.page.filters = { modelCode: params['modelCode'] };
      }
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout);

    this.searchTimeout = setTimeout(() => {
      this.page.filters = { ...this.page.filters, unitType: value.trim() };
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  toggleAll(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    const checked = checkbox.checked;

    if (checked) {
      // Select all row IDs
      this.selectedUnitIds = this.rows.map((row: any) => row.id);
    } else {
      this.selectedUnitIds = [];
    }
  }

  // Check if all are selected
  isAllSelected(): boolean {
    return this.rows.length > 0 && this.selectedUnitIds.length === this.rows.length;
  }

  toggleUnitSelection(unitId: number, $event: any) {
    const checkbox = $event.target as HTMLInputElement;
    const checked = checkbox.checked;
    if (checked) {
      this.selectedUnitIds.push(unitId);
    } else {
      this.selectedUnitIds = this.selectedUnitIds.filter(id => id !== unitId);
    }
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = { ...this.page.filters, ...filters };

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any): Promise<void> {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (pagedData: any) => {
        console.log(pagedData.data);
        this.rows = Array.isArray(pagedData.data) ? pagedData.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = pagedData.count;
        this.page.count = Math.ceil(pagedData.count / this.page.size);

        // Update empty card visibility
        this.updateEmptyCardVisibility();

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  updateEmptyCardVisibility() {
    // Show empty card when there are no units
    this.showEmptyCard = this.rows.length === 0;
  }

  selectedUnitPlanImage: string;
  showUnitPlanModal(diagramUrl: string) {
    this.selectedUnitPlanImage = diagramUrl;
    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  uploadModel() {
    console.log('Upload model clicked');
  }

  makeSelectedAvailable() {
    if (this.selectedUnitIds.length === 0) {
      Swal.fire('Please select at least one unit.', '', 'warning');
      return;
    }
    console.log(this.selectedUnitIds);
    this.unitService.makeSelectedAvailable(this.selectedUnitIds).subscribe(
      async (response: any) => {
        this.cd.detectChanges();
        await Swal.fire('Units updated successfully!', '', 'success');
        this.selectedUnitIds = [];
        this.reloadTable(this.page);
      },
      (error: any) => {
        Swal.fire('Error making units available.', '', 'error');
        console.error(error);
      }
    );
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'MODEL_CODE': 'كود النموذج',
        'UNIT': 'الوحدة',
        'UNITS': 'الوحدات',
        'FILTER': 'تصفية',
        'SEARCH_UNITS': 'ابحث بنوع الوحدات..',
        'BUILDING_NUMBER': 'رقم المبنى',
        'APARTMENT_NUMBER': 'رقم الوحدة',
        'VIEW': 'الاطلالة',
        'VIEW_IMAGE': 'عرض الصورة',
        'VIEW_DETAILS': 'عرض التفاصيل',
        'FLOOR': 'الدور',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'FINISHING_TYPE': 'حالة التشطيب',
        'STATUS': 'الحالة',
        'DIAGRAM': 'المخطط',
        'LOCATION_IN_MASTER_PLAN': 'ماستر بلان',
        'OTHER_ACCESSORIES': 'كماليات أخرى',
        'ACTIONS': 'الإجراءات',
        'VIEW_DIAGRAM': 'عرض المخطط',
        'VIEW_LOCATION': 'عرض الموقع',
        'APARTMENTS': 'شقة',
        'DUPLEXES': 'دوبلكس',
        'STUDIOS': 'استوديو',
        'PENTHOUSES': 'بنت هاوس',
        'ROOFS': 'روف',
        'VILLAS': 'فيلا',
        'I_VILLA': 'اى فيلا',
        'TWIN_HOUSES': 'توين هاوس',
        'TOWN_HOUSES': 'تاون هاوس',
        'ADMINISTRATIVE_UNITS': 'وحدات إدارية',
        'MEDICAL_CLINICS': 'عيادات طبية',
        'PHARMACIES': 'صيدليات',
        'COMMERCIAL_STORES': 'محلات تجارية',
        'WAREHOUSES': 'مخازن',
        'FACTORY_LANDS': 'أراضي مصانع',
        'WAREHOUSES_LAND': 'أراضي مخازن',
        'STANDALONE_VILLAS': 'فيلا مستقلة',
        'COMMERCIAL_ADMINISTRATIVE_BUILDINGS': 'مبنى ادارى تجارى',
        'COMMERCIAL_ADMINISTRATIVE_LANDS': 'ارض ادارى تجارى',
        'RESIDENTIAL_BUILDINGS': 'مباني سكنية',
        'RESIDENTIAL_LANDS': 'أراضي سكنية',
        'CHALETS': 'شاليه',
        'HOTELS': 'فندق',
        'FACTORIES': 'مصانع',
        'BASEMENTS': 'بيزمنت',
        'FULL_BUILDINGS': 'عمارة كاملة',
        'COMMERCIAL_UNITS': 'وحدات تجارية',
        'SHOPS': 'محلات',
        'MIXED_HOUSINGS': 'إسكان مختلط',
        'COOPERATIVES': 'تعاونيات',
        'YOUTH_UNITS': 'وحدات الشباب',
        'GANAT_MISR': 'جنات مصر',
        'DAR_MISR': 'دار مصر',
        'SAKAN_MISR': 'سكن مصر',
        'INDUSTRIAL_LANDS': 'أراضي صناعية',
        'CABIN': 'كابين',
        'VACATION_VILLA': 'فيلا مصيفية',
        'RESIDENTIAL_VILLA_LANDS': 'اراضى فيلا سكنية',
        'RESIDENTIAL_BUILDINGS_LANDS': 'أراضي مباني سكنية',
        'ADMINSTRATIVE_LANDS': 'اراضى ادارية',
        'COMMERCIAL_LANDS': 'ارارضى تجارية',
        'MEDICAL_LANDS': 'أراضي طبية',
        'MIXED_LANDS': 'أراضي مختلطة',
        'WATER_VIEW': 'فيو مائى',
        'GARDENS_AND_LANDSCAPE': 'حدائق ولاندسكيب',
        'STREET': 'شارع',
        'ENTERTAINMENT_AREA': 'منطقة ترفيهية',
        'GARDEN': 'حديقة',
        'MAIN_STREET': 'شارع رئيسي',
        'SQUARE': 'ميدان',
        'SIDE_STREET': 'جانبى',
        'REAR_VIEW': 'خلفى',
        'ALL_OF_THE_ABOVE': 'كل ما سبق',
        'GROUND': 'ارضى',
        'LAST_FLOOR': 'الدور الأخير',
        'REPEATED': 'متكرر',
        'ALL_THE_ABOVE_ARE_SUITABLE': 'جميع ما سبق مناسب',
        'ON_BRICK': 'على الطوب',
        'SEMI_FINISHED': 'نصف تشطيب',
        'FULL_FINISHED': 'تشطيب كامل',
        'COMPANY_FINISHED': 'تشطيب شركة',
        'SUPER_LUX': 'سوبر لوكس',
        'ULTRA_SUPER_LUX': 'ألترا سوبر لوكس',
        'STANDARD': 'عادي',
        'NEW': 'جديد',
        'AVAILABLE': 'متاح',
        'RESERVED': 'محجوز',
        'SOLD': 'تم البيع',
        'GARAGE': 'جراج',
        'CLUBHOUSE': 'كلاب هاوس',
        'CLUB': 'نادي',
        'STORAGE': 'مخزن',
        'ELEVATOR': 'اسانسير',
        'SWIMMING_POOL': 'حمام سباحة',
        'LAND_SHARE': 'حصة أرض',
        'NOT_AVAILABLE': 'غير متاح',
        'MAKE_SELECTED_AVAILABLE' : 'جعل الوحدات المختارة متاحة',
        'TO_VIEW_UNITS_DETAILS' : 'عرض تفاصيل الوحدات...'
      },
      'en': {
        'MODEL_CODE': 'Model Code',
        'UNIT': 'Unit',
        'UNITS': 'Units',
        'FILTER': 'Filter',
        'SEARCH_UNITS': 'Search By Unit Type..',
        'BUILDING_NUMBER': 'Building Number',
        'APARTMENT_NUMBER': 'Apartment Number',
        'VIEW': 'View',
        'VIEW_IMAGE': 'View Image',
        'VIEW_DETAILS': 'View Details',
        'FLOOR': 'Floor',
        'DELIVERY_DATE': 'Delivery Date',
        'FINISHING_TYPE': 'Finishing Type',
        'STATUS': 'Status',
        'DIAGRAM': 'Diagram',
        'LOCATION_IN_MASTER_PLAN': 'Location In Master Plan',
        'OTHER_ACCESSORIES': 'Other Accessories',
        'ACTIONS': 'Actions',
        'VIEW_DIAGRAM': 'View Diagram',
        'VIEW_LOCATION': 'View Location',
        'APARTMENTS': 'Apartments',
        'DUPLEXES': 'Duplexes',
        'STUDIOS': 'Studios',
        'PENTHOUSES': 'Penthouses',
        'ROOFS': 'Roofs',
        'VILLAS': 'Villas',
        'I_VILLA': 'Independent Villa',
        'TWIN_HOUSES': 'Twin Houses',
        'TOWN_HOUSES': 'Town Houses',
        'ADMINISTRATIVE_UNITS': 'Administrative Units',
        'MEDICAL_CLINICS': 'Medical Clinics',
        'PHARMACIES': 'Pharmacies',
        'COMMERCIAL_STORES': 'Commercial Stores',
        'WAREHOUSES': 'Warehouses',
        'FACTORY_LANDS': 'Factory Lands',
        'WAREHOUSES_LAND': 'Warehouses Land',
        'STANDALONE_VILLAS': 'Standalone Villas',
        'COMMERCIAL_ADMINISTRATIVE_BUILDINGS': 'Commercial Administrative Buildings',
        'COMMERCIAL_ADMINISTRATIVE_LANDS': 'Commercial Administrative Lands',
        'RESIDENTIAL_BUILDINGS': 'Residential Buildings',
        'RESIDENTIAL_LANDS': 'Residential Lands',
        'CHALETS': 'Chalets',
        'HOTELS': 'Hotels',
        'FACTORIES': 'Factories',
        'BASEMENTS': 'Basements',
        'FULL_BUILDINGS': 'Full Buildings',
        'COMMERCIAL_UNITS': 'Commercial Units',
        'SHOPS': 'Shops',
        'MIXED_HOUSINGS': 'Mixed Housings',
        'COOPERATIVES': 'Cooperatives',
        'YOUTH_UNITS': 'Youth Units',
        'GANAT_MISR': 'Ganat Misr',
        'DAR_MISR': 'Dar Misr',
        'SAKAN_MISR': 'Sakan Misr',
        'INDUSTRIAL_LANDS': 'Industrial Lands',
        'CABIN': 'Cabin',
        'VACATION_VILLA': 'Vacation Villa',
        'RESIDENTIAL_VILLA_LANDS': 'Residential Villa Lands',
        'RESIDENTIAL_BUILDINGS_LANDS': 'Residential Buildings Lands',
        'ADMINSTRATIVE_LANDS': 'Administrative Lands',
        'COMMERCIAL_LANDS': 'Commercial Lands',
        'MEDICAL_LANDS': 'Medical Lands',
        'MIXED_LANDS': 'Mixed Lands',
        'WATER_VIEW': 'Water View',
        'GARDENS_AND_LANDSCAPE': 'Gardens and Landscape',
        'STREET': 'Street',
        'ENTERTAINMENT_AREA': 'Entertainment Area',
        'GARDEN': 'Garden',
        'MAIN_STREET': 'Main Street',
        'SQUARE': 'Square',
        'SIDE_STREET': 'Side Street',
        'REAR_VIEW': 'Rear View',
        'ALL_OF_THE_ABOVE': 'All of the Above',
        'GROUND': 'Ground Floor',
        'LAST_FLOOR': 'Last Floor',
        'REPEATED': 'Repeated',
        'ALL_THE_ABOVE_ARE_SUITABLE': 'All The Above Are Suitable',
        'ON_BRICK': 'On Brick',
        'SEMI_FINISHED': 'Semi Finished',
        'FULL_FINISHED': 'Full Finished',
        'COMPANY_FINISHED': 'Company Finished',
        'SUPER_LUX': 'Super Lux',
        'ULTRA_SUPER_LUX': 'Ultra Super Lux',
        'STANDARD': 'Standard',
        'NEW': 'New',
        'AVAILABLE': 'Available',
        'RESERVED': 'Reserved',
        'SOLD': 'Sold',
        'GARAGE': 'Garage',
        'CLUBHOUSE': 'Clubhouse',
        'CLUB': 'Club',
        'STORAGE': 'Storage',
        'ELEVATOR': 'Elevator',
        'SWIMMING_POOL': 'Swimming Pool',
        'LAND_SHARE': 'Land Share',
        'NOT_AVAILABLE': 'Not Available',
        'MAKE_SELECTED_AVAILABLE' : 'Make Selected Available',
        'TO_VIEW_UNITS_DETAILS' : 'View Units Details...'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Sorting properties
  orderBy: string = '';
  orderDir: string = 'asc';

  sortData(column: string) {
    if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

    this.rows.sort((a, b) => {
      const valA = a[column];
      const valB = b[column];

      if (this.orderDir === 'asc') {
        return valA > valB ? 1 : -1;
      } else {
        return valA < valB ? 1 : -1;
      }
    });
  }

  getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }

  getTranslatedUnitType(unitType: string): string {
    if (!unitType) return 'N/A';

    switch (unitType.toLowerCase()) {
      case 'apartments':
        return this.getTranslatedText('APARTMENTS');
      case 'duplexes':
        return this.getTranslatedText('DUPLEXES');
      case 'studios':
        return this.getTranslatedText('STUDIOS');
      case 'penthouses':
        return this.getTranslatedText('PENTHOUSES');
      case 'roofs':
        return this.getTranslatedText('ROOFS');
      case 'villas':
        return this.getTranslatedText('VILLAS');
      case 'i_villa':
        return this.getTranslatedText('I_VILLA');
      case 'twin_houses':
        return this.getTranslatedText('TWIN_HOUSES');
      case 'town_houses':
        return this.getTranslatedText('TOWN_HOUSES');
      case 'administrative_units':
        return this.getTranslatedText('ADMINISTRATIVE_UNITS');
      case 'medical_clinics':
        return this.getTranslatedText('MEDICAL_CLINICS');
      case 'pharmacies':
        return this.getTranslatedText('PHARMACIES');
      case 'commercial_stores':
        return this.getTranslatedText('COMMERCIAL_STORES');
      case 'warehouses':
        return this.getTranslatedText('WAREHOUSES');
      case 'factory_lands':
        return this.getTranslatedText('FACTORY_LANDS');
      case 'warehouses_land':
        return this.getTranslatedText('WAREHOUSES_LAND');
      case 'standalone_villas':
        return this.getTranslatedText('STANDALONE_VILLAS');
      case 'commercial_administrative_buildings':
        return this.getTranslatedText('COMMERCIAL_ADMINISTRATIVE_BUILDINGS');
      case 'commercial_administrative_lands':
        return this.getTranslatedText('COMMERCIAL_ADMINISTRATIVE_LANDS');
      case 'residential_buildings':
        return this.getTranslatedText('RESIDENTIAL_BUILDINGS');
      case 'residential_lands':
        return this.getTranslatedText('RESIDENTIAL_LANDS');
      case 'chalets':
        return this.getTranslatedText('CHALETS');
      case 'hotels':
        return this.getTranslatedText('HOTELS');
      case 'factories':
        return this.getTranslatedText('FACTORIES');
      case 'basements':
        return this.getTranslatedText('BASEMENTS');
      case 'full_buildings':
        return this.getTranslatedText('FULL_BUILDINGS');
      case 'commercial_units':
        return this.getTranslatedText('COMMERCIAL_UNITS');
      case 'shops':
        return this.getTranslatedText('SHOPS');
      case 'mixed_housings':
        return this.getTranslatedText('MIXED_HOUSINGS');
      case 'cooperatives':
        return this.getTranslatedText('COOPERATIVES');
      case 'youth_units':
        return this.getTranslatedText('YOUTH_UNITS');
      case 'ganat_misr':
        return this.getTranslatedText('GANAT_MISR');
      case 'dar_misr':
        return this.getTranslatedText('DAR_MISR');
      case 'sakan_misr':
        return this.getTranslatedText('SAKAN_MISR');
      case 'industrial_lands':
        return this.getTranslatedText('INDUSTRIAL_LANDS');
      case 'cabin':
        return this.getTranslatedText('CABIN');
      case 'vacation_villa':
        return this.getTranslatedText('VACATION_VILLA');
      case 'residential_villa_lands':
        return this.getTranslatedText('RESIDENTIAL_VILLA_LANDS');
      case 'residential_buildings_lands':
        return this.getTranslatedText('RESIDENTIAL_BUILDINGS_LANDS');
      case 'adminstrative_lands':
        return this.getTranslatedText('ADMINSTRATIVE_LANDS');
      case 'commercial_lands':
        return this.getTranslatedText('COMMERCIAL_LANDS');
      case 'medical_lands':
        return this.getTranslatedText('MEDICAL_LANDS');
      case 'mixed_lands':
        return this.getTranslatedText('MIXED_LANDS');
      default:
        return unitType;
    }
  }

  getTranslatedViewType(viewType: string): string {
    if (!viewType) return 'N/A';

    switch (viewType.toLowerCase()) {
      case 'water_view':
        return this.getTranslatedText('WATER_VIEW');
      case 'gardens_and_landscape':
        return this.getTranslatedText('GARDENS_AND_LANDSCAPE');
      case 'street':
        return this.getTranslatedText('STREET');
      case 'entertainment_area':
        return this.getTranslatedText('ENTERTAINMENT_AREA');
      case 'garden':
        return this.getTranslatedText('GARDEN');
      case 'main_street':
        return this.getTranslatedText('MAIN_STREET');
      case 'square':
        return this.getTranslatedText('SQUARE');
      case 'side_street':
        return this.getTranslatedText('SIDE_STREET');
      case 'rear_view':
        return this.getTranslatedText('REAR_VIEW');
      case 'all_of_the_above':
        return this.getTranslatedText('ALL_OF_THE_ABOVE');
      default:
        return viewType;
    }
  }

  getTranslatedFloorType(floorType: string): string {
    if (!floorType) return 'N/A';

    switch (floorType.toLowerCase()) {
      case 'ground':
        return this.getTranslatedText('GROUND');
      case 'last_floor':
        return this.getTranslatedText('LAST_FLOOR');
      case 'repeated':
        return this.getTranslatedText('REPEATED');
      case 'all_the_above_are_suitable':
        return this.getTranslatedText('ALL_THE_ABOVE_ARE_SUITABLE');
      default:
        return floorType;
    }
  }

  getTranslatedFinishingType(finishingType: string): string {
    if (!finishingType) return 'N/A';

    switch (finishingType.toLowerCase()) {
      case 'on_brick':
        return this.getTranslatedText('ON_BRICK');
      case 'semi_finished':
        return this.getTranslatedText('SEMI_FINISHED');
      case 'full_finished':
        return this.getTranslatedText('FULL_FINISHED');
      case 'company_finished':
        return this.getTranslatedText('COMPANY_FINISHED');
      case 'super_lux':
        return this.getTranslatedText('SUPER_LUX');
      case 'ultra_super_lux':
        return this.getTranslatedText('ULTRA_SUPER_LUX');
      case 'standard':
        return this.getTranslatedText('STANDARD');
      case 'all_of_the_above':
        return this.getTranslatedText('ALL_OF_THE_ABOVE');
      default:
        return finishingType;
    }
  }

  getTranslatedStatus(status: string): string {
    if (!status) return 'N/A';

    switch (status.toLowerCase()) {
      case 'new':
        return this.getTranslatedText('NEW');
      case 'available':
        return this.getTranslatedText('AVAILABLE');
      case 'reserved':
        return this.getTranslatedText('RESERVED');
      case 'sold':
        return this.getTranslatedText('SOLD');
      default:
        return status;
    }
  }

  getTranslatedOtherAccessories(accessories: string | string[]): string {
    if (!accessories) return 'N/A';

    let accessoryArray: string[] = [];

    if (Array.isArray(accessories)) {
      accessoryArray = accessories;
    } else if (typeof accessories === 'string') {
      const cleanedAccessories = accessories.replace(/[\[\]"']/g, '');
      accessoryArray = cleanedAccessories.split(/[,;|]/).filter(item => item.trim() !== '');
    } else {
      return 'N/A';
    }
    const translatedList = accessoryArray.map(accessory => {
      const trimmedAccessory = accessory.trim().toLowerCase();
      switch (trimmedAccessory) {
        case 'garage':
          return this.getTranslatedText('GARAGE');
        case 'clubhouse':
          return this.getTranslatedText('CLUBHOUSE');
        case 'club':
          return this.getTranslatedText('CLUB');
        case 'storage':
          return this.getTranslatedText('STORAGE');
        case 'elevator':
          return this.getTranslatedText('ELEVATOR');
        case 'swimming_pool':
          return this.getTranslatedText('SWIMMING_POOL');
        case 'land_share':
          return this.getTranslatedText('LAND_SHARE');
        case 'all_the_above_are_suitable':
        case 'all_the_above':
          return this.getTranslatedText('ALL_THE_ABOVE_ARE_SUITABLE');
        default:
          return accessory.trim();
      }
    }).filter(item => item && item.trim() !== '');
    return translatedList.length > 0 ? translatedList.join(', ') : 'N/A';
  }
}

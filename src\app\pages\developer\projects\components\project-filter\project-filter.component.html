<div class="filter-dropdown" [class.rtl-filter]="translationService.getCurrentLanguage() === 'ar'">
  <div class="mb-2">
    <label class="form-label"
      [style.font-family]="translationService.getCurrentLanguage() === 'ar' ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
      {{ getTranslatedText('MANAGEMENT_TEAM') }}:
    </label>
    <input type="text" class="form-control form-control-sm" [(ngModel)]="filter.managementTeam"
      [placeholder]="getTranslatedText('ENTER_MANAGEMENT_TEAM')"
      [style.font-family]="translationService.getCurrentLanguage() === 'ar' ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
      [style.text-align]="translationService.getCurrentLanguage() === 'ar' ? 'right' : 'left'"
      [style.direction]="translationService.getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr'" />
  </div>

  <div class="mb-2">
    <label class="form-label"
      [style.font-family]="translationService.getCurrentLanguage() === 'ar' ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
      {{ getTranslatedText('PROJECT_EXECUTER') }}:
    </label>
    <input type="text" class="form-control form-control-sm" [(ngModel)]="filter.projectExecuter"
      [placeholder]="getTranslatedText('ENTER_PROJECT_EXECUTER')"
      [style.font-family]="translationService.getCurrentLanguage() === 'ar' ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
      [style.text-align]="translationService.getCurrentLanguage() === 'ar' ? 'right' : 'left'"
      [style.direction]="translationService.getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr'" />
  </div>

  <button class="btn btn-sm btn-primary w-100" (click)="apply()"
    [style.font-family]="translationService.getCurrentLanguage() === 'ar' ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
    {{ getTranslatedText('APPLY') }}
  </button>
</div>
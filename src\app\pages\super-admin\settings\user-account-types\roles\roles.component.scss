// Roles component styling - Enhanced design

// Enhanced card styling
.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e1e3ea;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

// Custom Roles Header
.custom-roles-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e1e3ea;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 90px;

  .header-content-wrapper {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
  }

  .header-icon-section {
    .custom-icon-container {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);

      i {
        font-size: 1.8rem;
        color: white;
      }
    }
  }

  .header-text-section {
    flex: 1;

    .header-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: #2d3748;
      margin: 0 0 5px 0;
      line-height: 1.3;
    }

    .header-subtitle {
      font-size: 1rem;
      color: #718096;
      font-weight: 500;
    }
  }

  // Arabic layout
  &.arabic-header {
    direction: rtl;

    .header-content-wrapper {
      justify-content: flex-start;
      text-align: right;

      .header-text-section {
        flex: none;
      }
    }

    .header-text-section {
      text-align: right;

      .header-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 2rem;
        margin-bottom: 8px;
      }

      .header-subtitle {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
      }
    }

    .card-toolbar {
      direction: ltr;

      .btn {
        font-family: 'Hacen Liner Screen', sans-serif;
      }
    }
  }
}

// Custom Table Header
.custom-table-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 20px 25px;

  .table-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .table-header-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  // Arabic layout
  &.arabic-table-header {
    direction: rtl;
    text-align: right;

    .table-header-content {
      justify-content: flex-start;
    }

    .table-header-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      font-size: 1.6rem;
      text-align: right;
    }
  }
}

// Custom Empty State
.custom-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .empty-state-icon {
    margin-bottom: 2rem;

    .symbol-100px {
      width: 100px;
      height: 100px;

      .symbol-label {
        border-radius: 50%;

        i {
          font-size: 2.5rem;
        }
      }
    }
  }

  .empty-state-content {
    max-width: 400px;

    .empty-state-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #6c757d;
      margin-bottom: 0.75rem;
      line-height: 1.4;
    }

    .empty-state-subtitle {
      font-size: 1rem;
      color: #a1a5b7;
      line-height: 1.5;
    }
  }

  // Arabic layout
  &.arabic-empty-state {
    text-align: center;
    padding: 5rem 2rem !important;

    .empty-state-content {
      .empty-state-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: none;
      }

      .empty-state-subtitle {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
        color: #8a8a8a;
        line-height: 1.6;
        font-weight: 500;
      }
    }
  }
}

// Arabic specific adjustments
html[lang="ar"] {
  .custom-roles-header {
    .header-content-wrapper {
      justify-content: center !important;

      .header-text-section {
        text-align: center !important;

        .header-title {
          text-align: center !important;
        }

        .header-subtitle {
          text-align: center !important;
        }
      }
    }
  }

  // Search input without icon in Arabic
  .position-relative {
    .form-control {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }
  }

  // Enhanced Arabic Empty State
  .custom-empty-state.arabic-empty-state {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 12px !important;
    margin: 2rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;

    .empty-state-content {
      .empty-state-title {
        font-size: 1.8rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
      }

      .empty-state-subtitle {
        font-size: 1.2rem !important;
        color: #6c757d !important;
        text-align: center !important;
        max-width: 300px !important;
        margin: 0 auto !important;
      }
    }
  }
}

// Specific layout classes for better control
.role-row-layout {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;

  .role-icon {
    flex-shrink: 0 !important;
  }

  .role-details {
    flex: 1 !important;
  }

  &.arabic-layout {
    direction: ltr !important; // Keep LTR for proper icon-text order
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 1rem !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
    gap: 1rem !important;

    .role-icon {
      display: flex !important;
      order: 1 !important;

      .symbol-label {
        background: #fff3cd !important;
        border-radius: 8px !important;

        i {
          color: #856404 !important;
          font-size: 1.2rem !important;
        }
      }
    }

    .role-details {
      direction: rtl !important;
      text-align: right !important;
      flex: 1 !important;
      order: 2 !important;

      .fw-bold {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        font-size: 1.1rem !important;
        font-weight: 700 !important;
        color: #2d3748 !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.4 !important;
        text-align: right !important;
        display: block !important;
        width: 100% !important;
      }

      .text-muted {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 0.9rem !important;
        font-weight: 400 !important;
        color: #9e9e9e !important;
        line-height: 1.5 !important;
        text-align: right !important;
        display: block !important;
        width: 100% !important;
      }
    }
  }
}

// Table improvements
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// Symbol styling
.symbol {
  &.symbol-50px {
    width: 50px;
    height: 50px;

    .symbol-label {
      border-radius: 8px;

      i {
        font-size: 1.2rem;
      }
    }
  }
}

// Search input styling
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

// Arabic specific fixes for roles
html[lang="ar"] {
  .role-date-column {
    direction: rtl !important;
    text-align: right !important;
    padding: 1.5rem 1rem !important;

    .d-flex.flex-column {
      align-items: flex-end !important;
      text-align: right !important;
      background: rgba(108, 117, 125, 0.1) !important;
      padding: 0.75rem !important;
      border-radius: 6px !important;

      span {
        text-align: right !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;

        &:first-child {
          font-size: 1rem !important;
          font-weight: 600 !important;
          color: #495057 !important;
          margin-bottom: 0.25rem !important;
        }

        &:last-child {
          font-size: 0.85rem !important;
          color: #6c757d !important;
        }
      }
    }
  }

  // Enhanced table styling for Arabic
  .table {
    tbody {
      tr {
        border-bottom: 2px solid #f1f3f4 !important;

        &:hover {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        td {
          border-bottom: none !important;
          vertical-align: middle !important;
        }
      }
    }
  }
}

// RTL and Arabic Language Support
[dir="rtl"] {
  .card-header {
    .card-title {
      text-align: right;

      h3 {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.75rem;
        font-weight: 700;
        line-height: 1.4;
        margin-bottom: 0.25rem;
      }

      span {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
        line-height: 1.5;
      }
    }

    .card-toolbar {
      flex-direction: row-reverse;

      .btn {
        margin-left: 0.5rem;
        margin-right: 0;
        font-family: 'Hacen Liner Screen', sans-serif;

        &:first-child {
          margin-left: 0;
        }

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  // Table headers alignment
  .table thead th {
    text-align: right;
    font-family: 'Noto Kufi Arabic', sans-serif;
    font-size: 0.95rem;
    font-weight: 600;
  }

  .table tbody td {
    text-align: right;
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  // Search input RTL
  .form-control {
    text-align: right;
    direction: rtl;
    font-family: 'Hacen Liner Screen', sans-serif;

    &.pe-10 {
      padding-right: 2.5rem;
      padding-left: 0.75rem;
    }
  }

  // Modal RTL support
  .permissions-modal-content {
    .modal-header {
      text-align: right;

      h5 {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.4rem;
        line-height: 1.4;
      }
    }

    .modal-body {
      .form-check {
        text-align: right;

        .form-check-label {
          font-family: 'Hacen Liner Screen', sans-serif;
          margin-right: 0.5rem;
          margin-left: 0;
        }
      }
    }

    .modal-footer {
      flex-direction: row-reverse;

      .btn {
        font-family: 'Hacen Liner Screen', sans-serif;
        margin-left: 0.5rem;
        margin-right: 0;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  // Header icon spacing for RTL
  .card-header .d-flex.align-items-center {
    .fas {
      margin-left: 1rem;
      margin-right: 0;
    }
  }
} using Metronic design
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 1.5rem;

  .card-title {
    margin-bottom: 0;

    h3 {
      color: #2d3748;
      font-weight: 700;
    }
  }

  .card-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.card-body {
  padding: 1.5rem;
}

// Table styling
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// Role symbol styling
.symbol {
  &.symbol-45px {
    width: 45px;
    height: 45px;

    .symbol-label {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Badge styling
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;

  &.badge-light-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  &.badge-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  &.badge-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }

  &.badge-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  &.badge-light-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
  }

  &.badge-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  &.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #5e6278;

    &:hover, &.btn-active-light-primary:hover {
      background-color: #e9ecef;
      border-color: #e9ecef;
      color: #0d6efd;
    }
  }

  &.btn-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;

    &:hover {
      background-color: rgba(13, 110, 253, 0.2);
      border-color: rgba(13, 110, 253, 0.2);
    }
  }
}

// Dropdown styling
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  min-width: 160px;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #e1e3ea;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: none;

  &.show {
    display: block !important;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;

    &:hover, &:focus {
      background-color: #f8f9fa;
      color: #1e2125;
    }

    &.text-danger {
      color: #dc3545;

      &:hover, &:focus {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
      }
    }

    i {
      width: 16px;
      text-align: center;
    }
  }

  .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e1e3ea;
  }
}

// Form controls
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.ps-10 {
    padding-left: 2.5rem;
  }
}

.form-select {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Utility classes
.text-warning {
  color: #ffc107 !important;
}

.fs-4 {
  font-size: 1.25rem !important;
}

// Simple Permissions Modal Styling
.permissions-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  padding: 1rem;
}

.permissions-modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e1e3ea;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .modal-title {
      color: #2d3748;
      font-weight: 700;
      margin: 0;
    }

    .btn-close {
      background: none;
      border: none;
      font-size: 1.2rem;
      color: #6c757d;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #e9ecef;
        color: #495057;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }

  .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e1e3ea;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    flex-shrink: 0;
  }
}

// Permission Cards
.permission-card {
  border: 1px solid #e1e3ea;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
  }

  .form-check {
    margin-bottom: 0;

    .form-check-input {
      width: 1.25rem;
      height: 1.25rem;
      margin-top: 0.125rem;
      border: 2px solid #e1e3ea;
      border-radius: 4px;

      &:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
      }

      &:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }
    }

    .form-check-label {
      cursor: pointer;
      margin-left: 0.5rem;
    }
  }
}

// Select All Checkbox
#selectAll {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e1e3ea;
  border-radius: 4px;

  &:checked {
    background-color: #198754;
    border-color: #198754;
  }

  &:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .permissions-modal-overlay {
    padding: 0.5rem;
  }

  .permissions-modal-content {
    max-width: 100%;
    margin: 0;

    .modal-header {
      padding: 1rem;

      .modal-title {
        font-size: 1.1rem;
      }
    }

    .modal-body {
      padding: 1rem;

      .col-md-6 {
        width: 100%;
      }
    }

    .modal-footer {
      padding: 1rem;
      flex-direction: column;

      .btn {
        width: 100%;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

<div class="filter-dropdown">
  <div class="mb-3">
    <label class="form-label d-block text-center mb-2">{{ getTranslatedText('COMPOUND_TYPE') }}:</label>
    <div class="d-flex gap-2 flex-wrap justify-content-center" [class.flex-row-reverse]="translationService.isRTL()">
      <!-- Inside Compound -->
      <div class="form-check form-check-inline">
        <input type="radio" value="inside_compound" id="inside_compound" class="form-check-input"
          [(ngModel)]="filter.compoundType" name="compoundType" />
        <label class="form-check-label badge badge-light-primary px-3 py-2 cursor-pointer"
          [class.me-2]="!translationService.isRTL()" [class.ms-2]="translationService.isRTL()" for="inside_compound">
          {{ getTranslatedText('INSIDE_COMPOUND') }}
        </label>
      </div>

      <!-- Village -->
      <div class="form-check form-check-inline">
        <input type="radio" value="village" id="village" class="form-check-input" [(ngModel)]="filter.compoundType"
          name="compoundType" />
        <label class="form-check-label badge badge-light-success px-3 py-2 cursor-pointer"
          [class.me-2]="!translationService.isRTL()" [class.ms-2]="translationService.isRTL()" for="village">
          {{ getTranslatedText('VILLAGE') }}
        </label>
      </div>

      <!-- Outside Compound -->
      <div class="form-check form-check-inline">
        <input type="radio" value="outside_compound" id="outside_compound" class="form-check-input"
          [(ngModel)]="filter.compoundType" name="compoundType" />
        <label class="form-check-label badge badge-light-warning px-3 py-2 cursor-pointer"
          [class.me-2]="!translationService.isRTL()" [class.ms-2]="translationService.isRTL()" for="outside_compound">
          {{ getTranslatedText('OUTSIDE_COMPOUND') }}
        </label>
      </div>
    </div>
  </div>

  <!-- Separator Line -->
  <hr class="my-3" />

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('AREA') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.area">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let area of areas" [value]="area.id">
        {{ translationService.getCurrentLanguage() === 'ar' ? (area.name_ar || area.name_en) : area.name_en }}
      </option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('VIEW') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.view">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let view of views" [value]="view.value">{{ view.key }}</option>
    </select>
  </div>

  <div class="mb-2 text-center">
    <label class="form-label d-block text-center">{{ getTranslatedText('UNIT_TYPE') }}:</label>
    <select class="form-control form-control-sm mx-auto" [(ngModel)]="filter.unitType" style="max-width: 280px;">
      <option value="">{{ getTranslatedText('SELECT_UNIT_TYPE') }}</option>
      <option *ngFor="let type of unitTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_AREA') }}:</label>
    <div class="d-flex gap-2">
      <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('FROM')"
        [(ngModel)]="filter.unitAreaFrom" />
      <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('TO')"
        [(ngModel)]="filter.unitAreaTo" />
    </div>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('PRICE') }}:</label>
    <!-- <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_PRICE')"
      [(ngModel)]="filter.price" /> -->
    <div class="d-flex gap-2">
      <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('FROM')"
        [(ngModel)]="filter.priceFrom" />
      <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('TO')"
        [(ngModel)]="filter.priceTo" />
    </div>
  </div>

  <!-- <div class="mb-2">
    <label class="form-label">{{ 'DATA_PROPERTIES.UNIT_FILTER.FINISHING_STATUS' | translate }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">{{ 'DATA_PROPERTIES.UNIT_FILTER.SELECT_FINISHING' | translate }}</option>
      <option *ngFor="let type of finishingTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div> -->

  <!-- <div class="mb-2">
    <label class="form-label">{{ 'DATA_PROPERTIES.UNIT_FILTER.STATUS' | translate }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.status">
      <option value="">{{ 'DATA_PROPERTIES.UNIT_FILTER.SELECT_STATUS' | translate }}</option>
      <option *ngFor="let state of status" [value]="state.value">{{ state.key }}</option>
    </select>
  </div> -->



  <div class="d-flex gap-2">
    <button class="btn btn-sm btn-primary flex-fill" (click)="apply()">{{ getTranslatedText('APPLY') }}</button>
    <button class="btn btn-sm btn-secondary flex-fill" (click)="reset()">{{ getTranslatedText('RESET') }}</button>
  </div>
</div>
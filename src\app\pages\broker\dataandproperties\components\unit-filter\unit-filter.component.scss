.filter-dropdown {
  min-width: 280px;

  .form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .form-control {
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
  }

  .btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;

    &.btn-primary {
      background-color: #667eea;
      border-color: #667eea;

      &:hover {
        background-color: #5a6fd8;
        border-color: #5a6fd8;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;

      &:hover {
        background-color: #5a6268;
        border-color: #545b62;
      }
    }
  }
}

// RTL Support for Unit Filter
:host-context(html[lang="ar"]) {
  .filter-dropdown {
    .form-label {
      text-align: right;
      font-family: 'Noto <PERSON>', sans-serif;
    }

    .form-control {
      text-align: right;
      direction: rtl;

      &::placeholder {
        text-align: right;
        direction: rtl;
      }
    }

    select.form-control {
      text-align: right;
      direction: rtl;

      option {
        text-align: right;
        direction: rtl;
      }
    }

    .d-flex {
      flex-direction: row-reverse;

      .btn {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-weight: 600;
      }
    }
  }
}

// تحسينات عامة للمكون
.filter-dropdown {
  .mb-2 {
    margin-bottom: 1rem !important;
  }

  .form-control-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
  }
}

// تحسينات للشاشات الصغيرة
@media (max-width: 768px) {
  .filter-dropdown {
    min-width: 250px;

    .form-label {
      font-size: 0.8rem;
    }

    .form-control-sm {
      font-size: 0.8rem;
      padding: 0.3rem 0.6rem;
    }

    .btn-sm {
      font-size: 0.8rem;
      padding: 0.3rem 0.6rem;
    }
  }
}

// تحسينات للشاشات الصغيرة جداً (320px)
@media (max-width: 576px) {
  .filter-dropdown {
    min-width: 100% !important;
    width: 100% !important;

    .mb-2 {
      text-align: center !important;
      margin-bottom: 1rem !important;

      .form-label {
        text-align: center !important;
        display: block !important;
        margin-bottom: 0.5rem !important;
        font-size: 0.85rem !important;
      }

      .form-control {
        margin: 0 auto !important;
        max-width: 100% !important;
        width: 100% !important;
        text-align: center !important;
      }
    }
  }
}

// شاشات 320px
@media (max-width: 320px) {
  .filter-dropdown {
    .mb-2 {
      .form-control {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
      }

      .form-label {
        font-size: 0.8rem !important;
      }
    }
  }
}

// تحسينات للـ Radio Buttons والـ Badges
.filter-dropdown {
  .form-check {
    margin-bottom: 0.5rem;

    .form-check-input {
      margin-top: 0.125rem;
      margin-right: 0.5rem;

      &:checked {
        background-color: #667eea;
        border-color: #667eea;
      }
    }

    .form-check-label {
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      border: 1px solid transparent;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      &.badge {
        font-size: 0.8rem;
        font-weight: 500;
        border-radius: 0.375rem;

        &.badge-light-primary {
          background-color: rgba(102, 126, 234, 0.1);
          color: #667eea;
          border-color: rgba(102, 126, 234, 0.2);
        }

        &.badge-light-success {
          background-color: rgba(40, 167, 69, 0.1);
          color: #28a745;
          border-color: rgba(40, 167, 69, 0.2);
        }

        &.badge-light-warning {
          background-color: rgba(255, 193, 7, 0.1);
          color: #ffc107;
          border-color: rgba(255, 193, 7, 0.2);
        }
      }
    }
  }

  // عندما يكون الـ radio button محدد
  .form-check-input:checked + .form-check-label.badge {
    &.badge-light-primary {
      background-color: #667eea;
      color: white;
      border-color: #667eea;
    }

    &.badge-light-success {
      background-color: #28a745;
      color: white;
      border-color: #28a745;
    }

    &.badge-light-warning {
      background-color: #ffc107;
      color: white;
      border-color: #ffc107;
    }
  }
}

// تحسينات responsive للـ radio buttons
@media (max-width: 576px) {
  .filter-dropdown {
    .d-flex.gap-2.flex-wrap.justify-content-center {
      flex-direction: column !important;
      align-items: center !important;

      .form-check {
        margin-bottom: 0.75rem !important;

        .form-check-label.badge {
          font-size: 0.85rem !important;
          padding: 0.5rem 1rem !important;
          min-width: 120px !important;
          text-align: center !important;
        }
      }
    }
  }
}

@media (max-width: 320px) {
  .filter-dropdown {
    .form-check {
      .form-check-label.badge {
        font-size: 0.75rem !important;
        padding: 0.4rem 0.8rem !important;
        min-width: 100px !important;
      }
    }
  }
}

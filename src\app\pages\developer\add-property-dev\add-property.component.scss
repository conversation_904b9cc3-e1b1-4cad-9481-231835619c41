.card {
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e6ef;

  .card-body {
    padding: 1.5rem;
  }

  &.cursor-pointer {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }
}

.text-dark-blue {
  color: #0d6efd; /* More vibrant blue color */
}

// Upload card styling
.upload-card-container {
  .card {
    transition: all 0.2s ease;
    border-radius: 25px;
    border: 1px solid #e4e6ef;

    label {
      cursor: pointer;
      font-size: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 0;

      .upload-icon {
        width: 32px;
        height: 32px;
        background-color: #0d6efd;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }

      .upload-text {
        color: #0d6efd;
        font-weight: bold;
      }
    }

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);
    }
  }
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.btn-blue-custom {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #0056b3;
  }
  &:disabled {
    background-color: #6c757d;
    color: #ffffff;
  }
}

.btn-green-custom {
  background-color: #ffffff;
  color: #000000;
  border: 2px solid #28a745;
  &:hover {
    background-color: #28a745;
    color: #ffffff;
    border-color: #28a745;
  }
  &:focus {
    background-color: #ffffff;
    color: #000000;
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Step indicator styling
.step-indicator-text {
  font-size: 1rem;
  font-weight: 600;
}

.step-separator {
  font-size: 0.9rem;
  margin: 0 0.5rem;
}

.step-total {
  font-size: 1rem;
  font-weight: 500;
}

// Force center alignment for step indicator with high specificity
.card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2 {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
}

// Additional specificity for RTL layout
html[dir="rtl"] .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2,
html[lang="ar"] .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2,
.rtl-layout .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2 {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
}

// Maximum specificity override for step indicator
html[dir="rtl"] .card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2,
html[lang="ar"] .card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2,
.card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2.rtl-layout {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
  width: 100% !important;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

// RTL Support for Add Property
.rtl-layout {
  direction: rtl;
  text-align: right;

  h2, h3, h4, h5 {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-weight: bold;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }

  .dropdown-item {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }

  .upload-text {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  // Reverse margins and paddings
  .me-2, .me-3 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }

  .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }

  // Dropdown positioning
  .dropdown-menu {
    right: 0 !important;
    left: auto !important;
  }

  // Button alignment
  .d-flex.justify-content-between {
    // flex-direction: row-reverse;
  }

  // Form inputs
  .text-start {
    text-align: right !important;
  }

  // Progress bar
  .progress {
    direction: ltr;
  }

  // Upload cards
  .upload-card-container {
    .card {
      .card-body {
        text-align: center;
      }
    }
  }

  // Project units section RTL support
  .d-flex.mb-3.align-items-center {
    flex-direction: row-reverse;

    .btn {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .flex-grow-1 {
      .form-control {
        text-align: right !important;
      }
    }
  }

  // Step indicator styling for Arabic
  .step-indicator-text {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
  }

  .step-separator {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1rem !important;
    margin: 0 0.75rem !important;
  }

  .step-total {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
  }

  // Center back to previous step link
  .text-primary.cursor-pointer.mb-2.text-center {
    text-align: center !important;
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-weight: 500;
  }
}

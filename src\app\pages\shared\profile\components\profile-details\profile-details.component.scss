.image-input {
  position: relative;
  display: inline-block;
  border-radius: 0.475rem;
  background-repeat: no-repeat;
  background-size: cover;

  .image-input-wrapper {
    width: 120px;
    height: 120px;
    border-radius: 0.475rem;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  &.image-input-empty .image-input-wrapper {
    background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="%23cccccc" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"%3E%3C/path%3E%3C/svg%3E');
    background-color: #f8f9fa;
  }

  [data-kt-image-input-action] {
    cursor: pointer;
    position: absolute;
    transform: translate(-50%, -50%);

    &[data-kt-image-input-action="change"] {
      left: 100%;
      top: 0;
    }

    &[data-kt-image-input-action="remove"] {
      left: 100%;
      top: 100%;
    }

    input {
      width: 0 !important;
      height: 0 !important;
      overflow: hidden;
      opacity: 0;
    }
  }
}

.custom-width {
  width: 40% !important;
}

// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;

    .card-title {
      width: 100%;
      text-align: right;
      justify-content: flex-end;
      display: flex;

      h3 {
        text-align: right;
        margin-left: auto;
        margin-right: 0;
      }
    }
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .form-control {
    text-align: right;
  }

  .required {
    margin-left: 0;
    margin-right: 2px;
  }

  .card-footer {
    text-align: left;
    justify-content: flex-start !important;

    .btn {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  .invalid-feedback {
    text-align: right;
  }
}

// Mobile Responsive Design
@media screen and (max-width: 768px) {
  .custom-width {
    width: 100% !important;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  .row.mb-6 {
    margin-bottom: 1.5rem !important;
  }

  .col-lg-2 {
    margin-bottom: 0.5rem;
  }

  .form-control {
    font-size: 1.5rem !important;
    padding: 0.75rem 1rem !important;
    min-height: 48px !important;
  }
}

// Extra Small Mobile Screens (320px and below)
@media screen and (max-width: 320px) {
  .custom-width {
    width: 100% !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .card-footer {
    padding: 1rem !important;
  }

  .form-control {
    font-size: 1.3rem !important;
    padding: 1rem 1.25rem !important;
    min-height: 52px !important;
    border-radius: 8px !important;
  }

  .form-control-lg {
    font-size: 1.2rem !important;
    padding: 1rem 1.25rem !important;
    min-height: 52px !important;
  }

  .col-form-label {
    font-size: 0.95rem !important;
    margin-bottom: 0.75rem !important;
  }

  .btn {
    font-size: 1rem !important;
    padding: 0.875rem 1.5rem !important;
    min-height: 48px !important;
    width: 100% !important;
  }

  .row.mb-6 {
    margin-bottom: 1.25rem !important;
  }

  // RTL Support for small screens
  :host-context(.rtl) {
    .form-control {
      text-align: right !important;
      font-size: 1.1rem !important;
    }

    .col-form-label {
      text-align: right !important;
    }
  }
}

// Center button for tablets and mobile (768px and below)
@media screen and (max-width: 768px) {
  .card-footer {
    justify-content: center !important;
    text-align: center !important;
    padding: 1.5rem !important;

    .btn {
      width: 100% !important;
      max-width: 280px !important;
      margin: 0 auto !important;
      padding: 0.875rem 1.5rem !important;
      font-size: 1rem !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
      align-items: center !important;
    }
  }

  // Specific targeting for the save button
  .card-footer.d-flex.justify-content-end {
    justify-content: center !important;
    align-items: center !important;

    .btn.btn-dark-blue {
      width: 100% !important;
      max-width: 280px !important;
      margin: 0 auto !important;
      text-align: center !important;
    }
  }

  // RTL support
  :host-context(html[dir="rtl"]),
  :host-context(html[lang="ar"]) {
    .card-footer {
      direction: rtl !important;
      text-align: center !important;

      .btn {
        direction: rtl !important;
        text-align: center !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
      }
    }
  }
}

// Center buttons for small screens (375px and below)
@media screen and (max-width: 375px) {
  // Target the specific div with ms-auto class
  #kt_signin_email_button.ms-auto {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;

    .btn.btn-dark-blue {
      width: 100% !important;
      max-width: 280px !important;
      margin: 0 auto !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
      align-items: center !important;
    }
  }

  // Override ms-auto class specifically
  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;

    &:has(.btn.btn-dark-blue) {
      margin-left: 0 !important;
      margin-right: 0 !important;
      width: 100% !important;
      display: flex !important;
      justify-content: center !important;
    }
  }
}

// Center buttons for very small screens (339px and below)
@media screen and (max-width: 339px) {
  // Target all buttons
  .btn.btn-dark-blue {
    width: 100% !important;
    margin: 0 auto 0.75rem auto !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    align-items: center !important;
    white-space: normal !important;
    line-height: 1.3 !important;
  }

  // Target buttons with px-9 class
  .btn.btn-dark-blue.px-9 {
    width: 100% !important;
    margin: 0 auto 0.75rem auto !important;
    padding: 0.75rem 1rem !important;
    text-align: center !important;
  }

  // Target any container holding these buttons
  .d-flex {
    &:has(.btn.btn-dark-blue) {
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      width: 100% !important;
      gap: 0.75rem !important;
    }
  }

  // If buttons are in a card or container
  .card-body,
  .card-footer {
    .btn.btn-dark-blue {
      width: 100% !important;
      margin: 0 auto 0.75rem auto !important;
      text-align: center !important;
    }
  }

  // RTL support for very small screens
  :host-context(html[dir="rtl"]),
  :host-context(html[lang="ar"]) {
    .btn.btn-dark-blue {
      direction: rtl !important;
      text-align: center !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      width: 100% !important;
      margin: 0 auto 0.75rem auto !important;
    }
  }
}

// Force center with highest specificity for 375px
@media screen and (max-width: 375px) {
  // Use ::ng-deep to override Angular encapsulation
  :host ::ng-deep {
    #kt_signin_email_button.ms-auto {
      margin-left: 0 !important;
      margin-right: 0 !important;
      width: 100% !important;
      display: flex !important;
      justify-content: center !important;

      .btn.btn-dark-blue.px-9 {
        width: 100% !important;
        max-width: 280px !important;
        margin: 0 auto !important;
        text-align: center !important;
        justify-content: center !important;
        display: flex !important;
      }
    }

    .ms-auto {
      &:has(.btn) {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
      }
    }
  }
}

:host-context(html[dir=rtl]),
:host-context(html[lang="ar"]){
    .save-button{
      direction : ltr !important;
    }
}
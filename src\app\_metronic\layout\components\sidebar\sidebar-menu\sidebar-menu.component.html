<div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true" data-kt-scroll-activate="true"
  data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
  data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
  <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu"
    data-kt-menu="true" data-kt-menu-expand="false">
    <!-- admin -->
    <!-- Dashboard -->
    <div class="menu-item" *ngIf="user?.role === 'admin'">
      <a class="menu-link without-sub" routerLink="/super-admin/dashboard" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_66_13748" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
              height="24">
              <rect width="24" height="24" fill="#0D47A1" />
            </mask>
            <g mask="url(#mask0_66_13748)">
              <path stroke="#0D47A1" stroke-width="1"
                d="M4.40723 15.5H9.84766C9.97964 15.5001 10.0645 15.5391 10.1377 15.6123C10.2093 15.6839 10.2499 15.7699 10.25 15.9082V19.5918C10.2499 19.6944 10.2273 19.7683 10.1855 19.8291L10.1367 19.8867C10.0617 19.961 9.97477 20 9.84277 20H4.40234C4.30348 20 4.23083 19.9783 4.16992 19.9365L4.1123 19.8877C4.0407 19.8161 4.00009 19.73 4 19.5918V15.9082C4.00007 15.7712 4.04084 15.6852 4.11328 15.6133C4.18833 15.539 4.27521 15.5 4.40723 15.5ZM14.1572 12H19.5977C19.6965 12.0001 19.7692 12.0217 19.8301 12.0635L19.8877 12.1123C19.9618 12.1864 20 12.2713 20 12.4004V19.6006C19.9999 19.6962 19.9786 19.7673 19.9365 19.8281L19.8867 19.8867C19.8117 19.961 19.7248 20 19.5928 20H14.1523C14.0535 20 13.9808 19.9783 13.9199 19.9365L13.8623 19.8877C13.7882 19.8136 13.75 19.7288 13.75 19.5996V12.3994C13.7501 12.3038 13.7714 12.2327 13.8135 12.1719L13.8633 12.1133C13.9383 12.039 14.0252 12 14.1572 12ZM4.40723 4H9.84766C9.94652 4.00006 10.0192 4.02169 10.0801 4.06348L10.1377 4.1123C10.2117 4.18639 10.25 4.27126 10.25 4.40039V11.6006C10.2499 11.6962 10.2286 11.7673 10.1865 11.8281L10.1367 11.8867C10.0617 11.961 9.97477 12 9.84277 12H4.40234C4.3035 12 4.23083 11.9783 4.16992 11.9365L4.1123 11.8877C4.03823 11.8136 4 11.7287 4 11.5996V4.39941L4.00684 4.31152C4.01628 4.25758 4.03538 4.21246 4.06348 4.17188L4.11328 4.11328C4.18833 4.03897 4.27522 4 4.40723 4ZM14.1572 4H19.5977C19.6965 4.00006 19.7692 4.02171 19.8301 4.06348L19.8877 4.1123C19.9593 4.18391 19.9999 4.26999 20 4.4082V8.0918C19.9999 8.22881 19.9592 8.31485 19.8867 8.38672C19.8117 8.46103 19.7248 8.5 19.5928 8.5H14.1523C14.0203 8.49993 13.9355 8.46085 13.8623 8.3877C13.7907 8.3161 13.7501 8.23005 13.75 8.0918V4.4082C13.7501 4.30557 13.7727 4.2317 13.8145 4.1709L13.8633 4.11328C13.9383 4.03898 14.0252 4 14.1572 4Z" />
            </g>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.DASHBOARD"></span>
      </a>
    </div>
    <!-- project -->
    <div class="menu-item " *ngIf="user?.role === 'admin'">
      <a class="menu-link  without-sub" routerLink="/developer/projects" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
            <path stroke="#0D47A1" stroke-width="1.5" fill="none" d="M8 13h8M8 17h8M8 9h8" />
          </svg>
        </span>
        <span class="menu-title" translate="MENU.ALL_PROJECTS"></span>
      </a>

    </div>
    <!-- developer -->
    <div class="menu-item" *ngIf="user?.role === 'admin'">
      <a class="menu-link without-sub" routerLink="/super-admin/all-developers" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h-2.5l-2.5 3-2.5-3H10v6h10z" />
          </svg>
        </span>
        <span class="menu-title" translate="MENU.ALL_DEVELOPERS"></span>
      </a>
    </div>
    <!-- broker -->
    <div class="menu-item" *ngIf="user?.role === 'admin'">
      <a class="menu-link without-sub" routerLink="/super-admin/all-brokers" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4h2v-7.5c0-1.1-.9-2-2-2s-2 .9-2 2V18H4zm8.5-13c.83 0 1.5-.67 1.5-1.5S13.33 2 12.5 2 11 2.67 11 3.5 11.67 5 12.5 5zm1.5 1h-3C9.57 6 8.5 7.07 8.5 8.5V15h2v7h3v-7h2V8.5C15.5 7.07 14.43 6 13 6z" />
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M18.5 10.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S17 8.17 17 9s.67 1.5 1.5 1.5zm1.09 1h-2.18C16.53 11.5 16 12.08 16 12.77V16h1.5v6h3v-6H22v-3.23c0-.69-.53-1.27-1.41-1.27z" />
          </svg>
        </span>
        <span class="menu-title" translate="MENU.ALL_BROKERS"></span>
      </a>
    </div>
    <!-- Users -->
    <div class="menu-item" *ngIf="user?.role === 'admin'">
      <a class="menu-link without-sub" routerLink="/super-admin/all-users" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M16 13c.7 0 1.37-.1 2-.29V16c0 .55-.45 1-1 1s-1-.45-1-1v-3zm-2.5-4c1.38 0 2.5-1.12 2.5-2.5S14.88 4 13.5 4 11 5.12 11 6.5 12.12 9 13.5 9zM7.5 9C8.88 9 10 7.88 10 6.5S8.88 4 7.5 4 5 5.12 5 6.5 6.12 9 7.5 9zm6 1.5c-1.83 0-5.5.92-5.5 2.75V16c0 .55.45 1 1 1h9c.55 0 1-.45 1-1v-2.75c0-1.83-3.67-2.75-5.5-2.75zM7.5 10.5C5.67 10.5 2 11.42 2 13.25V16c0 .55.45 1 1 1h4.5v-2.75c0-.71.11-1.39.3-2.01-.49-.11-1.02-.19-1.55-.19-.09 0-.18.01-.25.01z" />
            <path stroke="#0D47A1" stroke-width="1.5" fill="none"
              d="M18 14v-3c-.88 0-1.73-.09-2.53-.26-.8.17-1.65.26-2.53.26-1.83 0-5.5-.92-5.5-2.75V11c0-.55.45-1 1-1h9c.55 0 1 .45 1 1v3z" />
          </svg>
        </span>
        <span class="menu-title" translate="MENU.ALL_USERS"></span>
      </a>
    </div>




    <!-- ********************************************************** -->
    <!-- broker -->
    <!-- Dashboard -->
    <div class=" menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/dashboard" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_66_13748" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
              height="24">
              <rect width="24" height="24" fill="#0D47A1" />
            </mask>
            <g mask="url(#mask0_66_13748)">
              <path stroke="#0D47A1" stroke-width="1"
                d="M4.40723 15.5H9.84766C9.97964 15.5001 10.0645 15.5391 10.1377 15.6123C10.2093 15.6839 10.2499 15.7699 10.25 15.9082V19.5918C10.2499 19.6944 10.2273 19.7683 10.1855 19.8291L10.1367 19.8867C10.0617 19.961 9.97477 20 9.84277 20H4.40234C4.30348 20 4.23083 19.9783 4.16992 19.9365L4.1123 19.8877C4.0407 19.8161 4.00009 19.73 4 19.5918V15.9082C4.00007 15.7712 4.04084 15.6852 4.11328 15.6133C4.18833 15.539 4.27521 15.5 4.40723 15.5ZM14.1572 12H19.5977C19.6965 12.0001 19.7692 12.0217 19.8301 12.0635L19.8877 12.1123C19.9618 12.1864 20 12.2713 20 12.4004V19.6006C19.9999 19.6962 19.9786 19.7673 19.9365 19.8281L19.8867 19.8867C19.8117 19.961 19.7248 20 19.5928 20H14.1523C14.0535 20 13.9808 19.9783 13.9199 19.9365L13.8623 19.8877C13.7882 19.8136 13.75 19.7288 13.75 19.5996V12.3994C13.7501 12.3038 13.7714 12.2327 13.8135 12.1719L13.8633 12.1133C13.9383 12.039 14.0252 12 14.1572 12ZM4.40723 4H9.84766C9.94652 4.00006 10.0192 4.02169 10.0801 4.06348L10.1377 4.1123C10.2117 4.18639 10.25 4.27126 10.25 4.40039V11.6006C10.2499 11.6962 10.2286 11.7673 10.1865 11.8281L10.1367 11.8867C10.0617 11.961 9.97477 12 9.84277 12H4.40234C4.3035 12 4.23083 11.9783 4.16992 11.9365L4.1123 11.8877C4.03823 11.8136 4 11.7287 4 11.5996V4.39941L4.00684 4.31152C4.01628 4.25758 4.03538 4.21246 4.06348 4.17188L4.11328 4.11328C4.18833 4.03897 4.27522 4 4.40723 4ZM14.1572 4H19.5977C19.6965 4.00006 19.7692 4.02171 19.8301 4.06348L19.8877 4.1123C19.9593 4.18391 19.9999 4.26999 20 4.4082V8.0918C19.9999 8.22881 19.9592 8.31485 19.8867 8.38672C19.8117 8.46103 19.7248 8.5 19.5928 8.5H14.1523C14.0203 8.49993 13.9355 8.46085 13.8623 8.3877C13.7907 8.3161 13.7501 8.23005 13.75 8.0918V4.4082C13.7501 4.30557 13.7727 4.2317 13.8145 4.1709L13.8633 4.11328C13.9383 4.03898 14.0252 4 14.1572 4Z" />
            </g>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.DASHBOARD"></span>
      </a>
    </div>

    <!-- Requests -->
    <div class="menu-item" *ngIf="user?.role === 'client' || user?.role === 'broker' ">
      <a class="menu-link without-sub" routerLink="/requests" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_24_2533)">
              <path stroke="#0D47A1" stroke-width="1"
                d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
              <path stroke="#0D47A1" stroke-width="1"
                d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
              <path stroke="#0D47A1" stroke-width="1"
                d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
            </g>
            <defs>
              <clipPath id="clip0_24_2533">
                <rect width="19" height="19" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.REQUESTS"></span>
      </a>
    </div>

    <!-- Maps -->
    <div class="menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/Maps" routerLinkActive="active">
        <span class="menu-icon">
          <i class="fa-regular fa-map fs-3 text-dark-blue"></i>
        </span>
        <span class="menu-title" translate="MENU.MAPS"></span>
      </a>
    </div>

    <!-- Advertisements -->
    <div class="menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/Adds" routerLinkActive="active">
        <span class="menu-icon">
          <app-keenicon name="social-media" class="fs-1 fw-semibold text-dark-blue" type="outline"></app-keenicon>
        </span>
        <span class="menu-title" translate="MENU.ADVERTISEMENTS"></span>
      </a>
    </div>

    <!-- Data & Properties -->
    <div class="menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/dataandproperties" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_448_13442" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="20"
              height="20">
              <path d="M19.5 19.5V0.5H0.5V19.5H19.5Z" fill="white" stroke="white" />
            </mask>
            <g mask="url(#mask0_448_13442)">
              <path
                d="M11.3698 19.259H2.64697V4.72903C2.64697 4.32169 2.90709 3.95989 3.29318 3.83013L11.3698 1.11528V19.259Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M16.2795 17.4364V19.259H11.3701V9.41489H16.7869C17.1459 9.41489 17.4365 9.70552 17.4365 10.0645V15.2754"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M11.3701 19.259H19.2667C19.51 19.259 19.7072 19.0618 19.7072 18.8186V17.4365H11.3701V19.259Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M17.9105 15.2046H18.0759C18.9767 15.2046 19.707 15.9349 19.707 16.8357V17.436H16.2793V16.8357C16.2793 15.9349 17.0096 15.2046 17.9105 15.2046Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M0.733438 19.259H2.64711V17.4365H0.292969V18.8186C0.292969 19.0618 0.490156 19.259 0.733438 19.259Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M2.4168 15.2046H2.64715V17.436H0.292969V17.3284C0.292969 16.1554 1.24387 15.2046 2.4168 15.2046Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M14.4034 9.41431H11.3701V1.11485L14.1142 3.76731C14.299 3.94599 14.4034 4.19208 14.4034 4.44919V8.03329"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M4.78076 3.32971V1.3512C4.78076 1.01487 5.05342 0.742212 5.39014 0.742212H5.45186C5.78818 0.742212 6.06084 1.01487 6.06084 1.3512V2.89924"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M11.3698 3.32935L4.78076 5.54419" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path d="M11.3698 5.43787L4.78076 7.65271" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path d="M11.3698 7.54651L4.78076 9.76135" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path d="M11.3698 9.65552L4.78076 11.8704" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path d="M5.8685 13.6125L4.78076 13.9781" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path d="M11.37 11.7633L7.18066 13.1715" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
              <path
                d="M8.25287 19.259H4.78076V16.7702C4.78076 16.5772 4.90928 16.4079 5.09514 16.356L7.82197 15.5947C8.03846 15.5343 8.25287 15.697 8.25287 15.9218V19.259Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M15.8193 11.8313H12.9462C12.8352 11.8313 12.7451 11.7413 12.7451 11.6302V10.925C12.7451 10.8139 12.8352 10.7239 12.9462 10.7239H15.8193C15.9304 10.7239 16.0204 10.8139 16.0204 10.925V11.6302C16.0204 11.7413 15.9304 11.8313 15.8193 11.8313Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M15.8193 14.0582H12.9462C12.8352 14.0582 12.7451 13.9682 12.7451 13.8571V13.1519C12.7451 13.0408 12.8352 12.9508 12.9462 12.9508H15.8193C15.9304 12.9508 16.0204 13.0408 16.0204 13.1519V13.8571C16.0204 13.9682 15.9304 14.0582 15.8193 14.0582Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path
                d="M13.8965 16.3091H12.9462C12.8352 16.3091 12.7451 16.219 12.7451 16.108V15.4028C12.7451 15.2917 12.8352 15.2017 12.9462 15.2017H13.8965C14.0076 15.2017 14.0977 15.2917 14.0977 15.4028V16.108C14.0977 16.219 14.0076 16.3091 13.8965 16.3091Z"
                stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M6.51709 15.9591V19.259" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10"
                stroke-linecap="round" stroke-linejoin="round" />
            </g>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.DATA_PROPERTIES"></span>
      </a>
    </div>

    <!-- Developers -->
    <div class="menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/developers" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_136_62239)">
              <path stroke="#0D47A1" fill="#0D47A1" stroke-width="0.5"
                d="M19.4998 18.9999H18.9998V11H17.9998V10H16.9998V11H15.9998V13H14.9999V11V9.00003H13.9999V11H12.9999V8.00004H11.9999V11V13H10.9999V11H9.9999V9.00003H8.99991V11H7.99992V18.9999H5.99994V8.00004H6.99993C7.55227 8.00004 7.99992 7.55239 7.99992 7.00005V6.00006H16.9998V9.00003H17.9998V6.00006H18.9998C19.4635 6.00006 19.8665 5.68108 19.9731 5.22972C20.0798 4.7784 19.8618 4.31305 19.4471 4.10575L17.4471 3.10576C17.3082 3.03642 17.1551 3.00009 16.9998 3.00009H6.10928L4.89429 0.552773C4.7223 0.208792 4.3723 0.00012207 4.00031 0.00012207C3.92399 0.00012207 3.84699 0.00879386 3.77031 0.027114C3.31895 0.133441 2.99997 0.536445 2.99997 1.00011V3.00009H0.99999C0.447652 3.00009 0 3.44774 0 4.00008V5.00007C0 5.55241 0.447652 6.00006 0.99999 6.00006H2.99997V7.00005V18.9999H0.499995C0.22367 18.9999 0 19.2236 0 19.4999C0 19.7763 0.22367 19.9999 0.499995 19.9999H19.4998C19.7761 19.9999 19.9998 19.7763 19.9998 19.4999C19.9998 19.2236 19.7761 18.9999 19.4998 18.9999ZM2.99997 5.00007H0.99999V4.00008H2.99997V5.00007ZM16.9998 4.00008L18.9998 5.00007H7.99992C7.99992 4.73472 7.89457 4.48043 7.70692 4.29309L7.41426 4.00008H16.9998ZM3.99996 1.00011L4.99995 3.00009H3.99996V1.00011ZM3.99996 4.00008H5.99994L6.99993 5.00007V7.00005H3.99996V4.00008ZM4.99995 18.9999H3.99996V8.00004H4.99995V18.9999ZM17.9998 18.9999H8.99991V12H9.9999V14H12.9999V12H13.9999V14H16.9998V12H17.9998V18.9999Z" />
            </g>
            <defs>
              <clipPath id="clip0_136_62239">
                <rect width="20" height="20" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.DEVELOPERS"></span>
      </a>
    </div>

    <!-- Subscription -->
    <div class="menu-item" *ngIf="user?.role === 'broker'">
      <a class="menu-link without-sub" routerLink="/broker/subscriptions" routerLinkActive="active">
        <span class="menu-icon">
          <!-- <img class="mx-auto h-20px w-20px" src="../../../../../../assets/media/broker/subscription2.png" alt="" /> -->
          <svg width="32" height="34" viewBox="0 0 28 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_143_62822)">
              <mask id="path-2-outside-1_143_62822" maskUnits="userSpaceOnUse" x="2.99939" y="2.99951" width="25"
                height="27" fill="black">
                <rect fill="white" x="2.99939" y="2.99951" width="27" height="29" />
                <path
                  d="M19.9266 16.9783C19.923 17.0088 19.9139 17.0392 19.8994 17.0661C19.883 17.1341 19.8758 17.2111 19.8758 17.2952C19.8758 17.3042 19.8776 17.3113 19.8776 17.3203V22.8973C19.9484 23.0727 20.0373 23.2034 20.1426 23.2858C20.2406 23.3628 20.3604 23.4004 20.5075 23.3932C20.5184 23.3914 20.5274 23.3914 20.5383 23.3914H22.1593C22.1629 23.3914 22.1684 23.3914 22.172 23.3914C22.4243 23.4039 22.6077 23.3359 22.7293 23.198C22.8672 23.0423 22.9471 22.7881 22.9743 22.4497V22.4461L23.4681 17.4134C23.4681 17.4062 23.4681 17.3973 23.4699 17.3901C23.5044 17.1036 23.4409 16.9175 23.2975 16.81C23.1341 16.6847 22.86 16.6328 22.5078 16.6346C22.4988 16.6346 22.4915 16.6364 22.4824 16.6364H20.6563V16.6346C20.3768 16.6346 20.1735 16.6901 20.0482 16.8011C19.9956 16.8494 19.9556 16.9067 19.9266 16.9783ZM13.0179 8.83037H15.483C15.5792 8.83037 15.6572 8.90557 15.6572 8.99867V10.9C15.6572 10.9931 15.5792 11.0683 15.483 11.0683H13.0179C12.9217 11.0683 12.8436 10.9931 12.8436 10.9V8.99867C12.8436 8.90557 12.9217 8.83037 13.0179 8.83037ZM15.3087 10.1176H14.3938V10.7317H15.3087V10.1176ZM14.0526 10.1176H13.1922V10.7317H14.0526V10.1176ZM13.1922 9.78106H14.0526V9.16696H13.1922V9.78106ZM14.3938 9.78106H15.3087V9.16696H14.3938V9.78106ZM8.29473 15.3634V10.9305C8.01519 11.0361 7.75561 11.0379 7.53779 10.9663C7.36897 10.9108 7.22557 10.8087 7.12029 10.678C7.01501 10.5473 6.94784 10.388 6.92788 10.2143C6.89702 9.94398 6.9787 9.63425 7.21468 9.34779C7.22557 9.33347 7.24009 9.31914 7.25461 9.3084L14.0617 4.06442C14.1488 3.98386 14.2867 3.97669 14.3829 4.05189L21.2045 9.28154C21.2154 9.28871 21.2245 9.29766 21.2336 9.3084C21.5494 9.6432 21.6275 10.0156 21.5512 10.3307C21.5131 10.4847 21.4387 10.6261 21.3352 10.7407C21.2317 10.8553 21.0992 10.9448 20.9486 10.9985C20.709 11.0844 20.4258 11.0809 20.139 10.9466V15.1593L19.6398 14.7601V10.6548C19.6398 10.6386 19.6416 10.6225 19.6452 10.6082L14.274 6.49216L8.78665 10.61C8.7921 10.6297 8.79573 10.6512 8.79573 10.6727V15.2309L8.29473 15.3634ZM7.5868 9.67363C7.45429 9.84372 7.40709 10.0138 7.42343 10.1588C7.4325 10.2412 7.46336 10.3146 7.51056 10.3737C7.55594 10.431 7.61947 10.4757 7.69571 10.5008C7.82641 10.5438 7.9934 10.533 8.18582 10.4435L14.1234 5.98727C14.196 5.93356 14.2867 5.92282 14.3648 5.95326C14.4156 5.97295 14.4664 6.01771 14.51 6.04994L20.1989 10.4095C20.2007 10.4113 20.2043 10.4131 20.2061 10.4148C20.4167 10.5652 20.62 10.5885 20.7761 10.533C20.8487 10.5079 20.9123 10.465 20.9613 10.4095C21.0103 10.354 21.0466 10.2877 21.0647 10.2143C21.1047 10.0514 21.0575 9.85267 20.8796 9.65573L14.2305 4.55856L7.5868 9.67363ZM12.6349 12.6635H15.6246C15.7589 12.6635 15.8678 12.7745 15.8678 12.9106V13.9598L15.3831 13.7933V13.1577H12.8763V13.7933L12.3917 13.9598V12.9106C12.3917 12.7745 12.5006 12.6635 12.6349 12.6635ZM18.1404 23.1497C18.1005 23.1139 18.0733 23.0656 18.0624 23.0119L15.4576 18.3873C15.1689 17.9111 14.9075 17.8986 14.6552 18.0472C14.4773 18.1528 14.2886 18.3229 14.0998 18.493C13.9854 18.595 13.8729 18.6989 13.7749 18.7758L13.3283 19.1411L13.3247 19.1429C12.9998 19.4025 12.6313 19.5403 12.2827 19.569C12.054 19.5869 11.8308 19.5582 11.6311 19.4848C11.4242 19.4078 11.2426 19.2843 11.1047 19.116C10.9558 18.9352 10.8596 18.7042 10.836 18.4303H10.8378C10.8324 18.3659 10.8524 18.2978 10.8977 18.2441L12.3209 16.5737C12.0214 16.5701 11.7563 16.6095 11.5077 16.6865C11.1283 16.8029 10.7743 17.007 10.3859 17.2666C10.3423 17.3006 10.286 17.3221 10.2243 17.3221H8.58153V20.3764L8.58879 22.3297H9.54178C9.63254 22.3297 9.71967 22.3763 9.76868 22.4586L10.9522 24.4961C11.1246 24.7933 11.2771 25.0493 11.4695 25.2194C11.6492 25.3769 11.8834 25.4754 12.2446 25.4861C12.3808 25.4897 12.5097 25.4664 12.6331 25.4163C12.7093 25.3859 12.7837 25.3447 12.8564 25.291L12.0885 23.8748C12.0214 23.7495 12.0686 23.5937 12.1956 23.5275C12.3227 23.4612 12.4806 23.5078 12.5478 23.6331L13.3828 25.1728C13.6932 25.3286 13.9745 25.3769 14.2287 25.3232C14.461 25.2731 14.6788 25.137 14.8839 24.915L13.6823 23.053C13.6042 22.9331 13.6405 22.7755 13.7621 22.6985C13.8838 22.6216 14.0435 22.6574 14.1216 22.7773L15.3886 24.7396C15.61 24.8291 15.8242 24.8488 16.0275 24.7933C16.2218 24.7413 16.416 24.6178 16.6102 24.4191L15.463 22.2868C15.3958 22.1614 15.443 22.0057 15.5719 21.9376C15.699 21.8714 15.8569 21.9179 15.9259 22.0451L17.113 24.2508C17.2673 24.3385 17.447 24.3618 17.6177 24.3349C17.7683 24.3099 17.9117 24.2472 18.0315 24.1541C18.1459 24.0646 18.2348 23.9482 18.2784 23.8157C18.3256 23.6725 18.322 23.506 18.2439 23.3234L18.1404 23.1497ZM18.4853 22.7021H19.3548V17.3221C19.3548 17.3149 19.3548 17.3078 19.3548 17.3024C19.3548 17.2182 19.3603 17.1395 19.3712 17.0643L18.4871 16.4824C18.4817 16.4788 18.4781 16.4752 18.4726 16.4717C18.3583 16.3947 18.2203 16.2998 18.0805 16.2049C17.5777 15.8576 17.0604 15.4995 16.5031 15.3563C16.2018 15.2775 15.8533 15.2256 15.4993 15.2202C15.1889 15.2148 14.8731 15.2435 14.5754 15.3187C14.3975 15.3634 14.2268 15.4261 14.0671 15.5102C13.9255 15.5836 13.793 15.6749 13.6768 15.7824L13.0833 16.4788C13.0651 16.5128 13.0397 16.5433 13.0088 16.5665L11.3715 18.484C11.3951 18.6058 11.4441 18.7096 11.5113 18.792C11.5893 18.8869 11.6928 18.9585 11.8126 19.0014C11.9397 19.048 12.0849 19.0659 12.2392 19.0534C12.4897 19.0337 12.7547 18.9316 12.9943 18.7418L13.4409 18.3766C13.5552 18.2835 13.6514 18.1976 13.7458 18.1116C13.9564 17.9218 14.1651 17.7339 14.3848 17.6049C14.8948 17.306 15.4013 17.2952 15.9041 18.1259L15.9095 18.1349L18.4853 22.7021ZM19.451 23.2177H18.7558C18.8502 23.4881 18.8447 23.7459 18.7703 23.9733C18.6941 24.2078 18.5434 24.4065 18.3528 24.5569C18.1658 24.7037 17.9371 24.804 17.6993 24.8416C17.4652 24.8792 17.2219 24.8595 16.995 24.7664C16.7318 25.0385 16.4541 25.2104 16.1637 25.2892C15.8732 25.368 15.5774 25.3519 15.2797 25.2498C14.9965 25.5595 14.6843 25.7529 14.3394 25.8263C13.989 25.9015 13.6151 25.8496 13.2194 25.6652C13.096 25.7601 12.9671 25.8353 12.8328 25.8908C12.6422 25.9695 12.4425 26.0053 12.2319 25.9982C11.7309 25.9839 11.3933 25.837 11.1246 25.6007C10.8687 25.3751 10.6963 25.0851 10.502 24.7503L9.39293 22.8436H8.53252C8.48351 23.0906 8.39638 23.2983 8.26569 23.4648C8.04605 23.7441 7.72475 23.8909 7.28003 23.882C7.27458 23.882 7.27095 23.882 7.2655 23.882H5.79155C5.42125 23.9411 5.10177 23.8515 4.85309 23.5669C4.63163 23.3144 4.48097 22.9062 4.41744 22.3047C4.41562 22.2975 4.41562 22.2903 4.41381 22.2832L4.02717 17.5477C3.95093 17.0356 4.03443 16.6758 4.24318 16.4287C4.45374 16.1798 4.76959 16.067 5.15986 16.0491C5.17075 16.0473 5.18164 16.0473 5.19253 16.0473H7.17656V16.0491C7.55049 16.0455 7.8736 16.1064 8.11865 16.2622C8.3147 16.3875 8.45628 16.5647 8.52889 16.8047H10.1444C10.551 16.5343 10.9304 16.323 11.3515 16.1924C11.7636 16.0652 12.2065 16.0205 12.7366 16.0831L13.2775 15.4476C13.2866 15.435 13.2975 15.4225 13.3102 15.4118C13.4645 15.2685 13.6369 15.1486 13.822 15.0519C14.0163 14.9498 14.2268 14.8729 14.4429 14.8174C14.7877 14.7296 15.1526 14.6956 15.5066 14.7028C15.9077 14.7099 16.298 14.769 16.6356 14.8549C17.28 15.0215 17.8373 15.4064 18.3782 15.7806C18.4998 15.8647 18.6178 15.9471 18.7612 16.0402C18.7667 16.0438 18.7703 16.0455 18.7758 16.0491L19.5599 16.5647C19.5999 16.511 19.6452 16.4609 19.6961 16.4161C19.9212 16.2174 20.2425 16.1189 20.6545 16.1189V16.1172H22.4806C22.4879 16.1172 22.4951 16.1172 22.5024 16.1189C22.9707 16.1172 23.3501 16.2013 23.6133 16.4018C23.9074 16.6238 24.0435 16.964 23.9854 17.451C23.9854 17.4546 23.9836 17.4599 23.9836 17.4635L23.4917 22.4909C23.4554 22.9402 23.3338 23.2929 23.1196 23.5364C22.8908 23.796 22.5732 23.9249 22.1557 23.907H20.5329C20.2515 23.9214 20.0137 23.8462 19.8141 23.6886C19.6688 23.574 19.5472 23.4165 19.451 23.2177ZM6.27803 21.2645C6.47226 21.2645 6.63018 21.4202 6.63018 21.6118C6.63018 21.8034 6.47226 21.9591 6.27803 21.9591C6.0838 21.9591 5.92588 21.8034 5.92588 21.6118C5.92588 21.4202 6.0838 21.2645 6.27803 21.2645ZM21.6511 21.2645C21.8453 21.2645 22.0032 21.4202 22.0032 21.6118C22.0032 21.8034 21.8453 21.9591 21.6511 21.9591C21.4568 21.9591 21.2989 21.8034 21.2989 21.6118C21.2989 21.4202 21.4568 21.2645 21.6511 21.2645ZM8.06057 19.7802L8.05149 17.0625C8.05149 17.0571 8.05149 17.0517 8.05149 17.0464C8.02245 16.8763 7.94802 16.7635 7.8373 16.6919C7.68663 16.5952 7.45973 16.5576 7.18019 16.5612H7.17656V16.563H5.19435C5.19072 16.563 5.18709 16.563 5.18346 16.563C4.9384 16.5737 4.74962 16.6328 4.64434 16.7581C4.5318 16.8906 4.49186 17.1216 4.54632 17.4796C4.54813 17.4868 4.54813 17.4939 4.54813 17.5011L4.93659 22.242V22.2509C4.98923 22.7415 5.09633 23.0566 5.2488 23.2303C5.36861 23.3681 5.53379 23.4075 5.72984 23.3717C5.74436 23.3681 5.76069 23.3681 5.77522 23.3681V23.3663H7.26369C7.27277 23.3663 7.28184 23.3663 7.29092 23.3681C7.55412 23.3717 7.73564 23.2947 7.85 23.1497C7.98433 22.9796 8.04786 22.7129 8.05875 22.3691L8.06057 19.7802Z" />
              </mask>
              <path
                d="M19.9266 16.9783C19.923 17.0088 19.9139 17.0392 19.8994 17.0661C19.883 17.1341 19.8758 17.2111 19.8758 17.2952C19.8758 17.3042 19.8776 17.3113 19.8776 17.3203V22.8973C19.9484 23.0727 20.0373 23.2034 20.1426 23.2858C20.2406 23.3628 20.3604 23.4004 20.5075 23.3932C20.5184 23.3914 20.5274 23.3914 20.5383 23.3914H22.1593C22.1629 23.3914 22.1684 23.3914 22.172 23.3914C22.4243 23.4039 22.6077 23.3359 22.7293 23.198C22.8672 23.0423 22.9471 22.7881 22.9743 22.4497V22.4461L23.4681 17.4134C23.4681 17.4062 23.4681 17.3973 23.4699 17.3901C23.5044 17.1036 23.4409 16.9175 23.2975 16.81C23.1341 16.6847 22.86 16.6328 22.5078 16.6346C22.4988 16.6346 22.4915 16.6364 22.4824 16.6364H20.6563V16.6346C20.3768 16.6346 20.1735 16.6901 20.0482 16.8011C19.9956 16.8494 19.9556 16.9067 19.9266 16.9783ZM13.0179 8.83037H15.483C15.5792 8.83037 15.6572 8.90557 15.6572 8.99867V10.9C15.6572 10.9931 15.5792 11.0683 15.483 11.0683H13.0179C12.9217 11.0683 12.8436 10.9931 12.8436 10.9V8.99867C12.8436 8.90557 12.9217 8.83037 13.0179 8.83037ZM15.3087 10.1176H14.3938V10.7317H15.3087V10.1176ZM14.0526 10.1176H13.1922V10.7317H14.0526V10.1176ZM13.1922 9.78106H14.0526V9.16696H13.1922V9.78106ZM14.3938 9.78106H15.3087V9.16696H14.3938V9.78106ZM8.29473 15.3634V10.9305C8.01519 11.0361 7.75561 11.0379 7.53779 10.9663C7.36897 10.9108 7.22557 10.8087 7.12029 10.678C7.01501 10.5473 6.94784 10.388 6.92788 10.2143C6.89702 9.94398 6.9787 9.63425 7.21468 9.34779C7.22557 9.33347 7.24009 9.31914 7.25461 9.3084L14.0617 4.06442C14.1488 3.98386 14.2867 3.97669 14.3829 4.05189L21.2045 9.28154C21.2154 9.28871 21.2245 9.29766 21.2336 9.3084C21.5494 9.6432 21.6275 10.0156 21.5512 10.3307C21.5131 10.4847 21.4387 10.6261 21.3352 10.7407C21.2317 10.8553 21.0992 10.9448 20.9486 10.9985C20.709 11.0844 20.4258 11.0809 20.139 10.9466V15.1593L19.6398 14.7601V10.6548C19.6398 10.6386 19.6416 10.6225 19.6452 10.6082L14.274 6.49216L8.78665 10.61C8.7921 10.6297 8.79573 10.6512 8.79573 10.6727V15.2309L8.29473 15.3634ZM7.5868 9.67363C7.45429 9.84372 7.40709 10.0138 7.42343 10.1588C7.4325 10.2412 7.46336 10.3146 7.51056 10.3737C7.55594 10.431 7.61947 10.4757 7.69571 10.5008C7.82641 10.5438 7.9934 10.533 8.18582 10.4435L14.1234 5.98727C14.196 5.93356 14.2867 5.92282 14.3648 5.95326C14.4156 5.97295 14.4664 6.01771 14.51 6.04994L20.1989 10.4095C20.2007 10.4113 20.2043 10.4131 20.2061 10.4148C20.4167 10.5652 20.62 10.5885 20.7761 10.533C20.8487 10.5079 20.9123 10.465 20.9613 10.4095C21.0103 10.354 21.0466 10.2877 21.0647 10.2143C21.1047 10.0514 21.0575 9.85267 20.8796 9.65573L14.2305 4.55856L7.5868 9.67363ZM12.6349 12.6635H15.6246C15.7589 12.6635 15.8678 12.7745 15.8678 12.9106V13.9598L15.3831 13.7933V13.1577H12.8763V13.7933L12.3917 13.9598V12.9106C12.3917 12.7745 12.5006 12.6635 12.6349 12.6635ZM18.1404 23.1497C18.1005 23.1139 18.0733 23.0656 18.0624 23.0119L15.4576 18.3873C15.1689 17.9111 14.9075 17.8986 14.6552 18.0472C14.4773 18.1528 14.2886 18.3229 14.0998 18.493C13.9854 18.595 13.8729 18.6989 13.7749 18.7758L13.3283 19.1411L13.3247 19.1429C12.9998 19.4025 12.6313 19.5403 12.2827 19.569C12.054 19.5869 11.8308 19.5582 11.6311 19.4848C11.4242 19.4078 11.2426 19.2843 11.1047 19.116C10.9558 18.9352 10.8596 18.7042 10.836 18.4303H10.8378C10.8324 18.3659 10.8524 18.2978 10.8977 18.2441L12.3209 16.5737C12.0214 16.5701 11.7563 16.6095 11.5077 16.6865C11.1283 16.8029 10.7743 17.007 10.3859 17.2666C10.3423 17.3006 10.286 17.3221 10.2243 17.3221H8.58153V20.3764L8.58879 22.3297H9.54178C9.63254 22.3297 9.71967 22.3763 9.76868 22.4586L10.9522 24.4961C11.1246 24.7933 11.2771 25.0493 11.4695 25.2194C11.6492 25.3769 11.8834 25.4754 12.2446 25.4861C12.3808 25.4897 12.5097 25.4664 12.6331 25.4163C12.7093 25.3859 12.7837 25.3447 12.8564 25.291L12.0885 23.8748C12.0214 23.7495 12.0686 23.5937 12.1956 23.5275C12.3227 23.4612 12.4806 23.5078 12.5478 23.6331L13.3828 25.1728C13.6932 25.3286 13.9745 25.3769 14.2287 25.3232C14.461 25.2731 14.6788 25.137 14.8839 24.915L13.6823 23.053C13.6042 22.9331 13.6405 22.7755 13.7621 22.6985C13.8838 22.6216 14.0435 22.6574 14.1216 22.7773L15.3886 24.7396C15.61 24.8291 15.8242 24.8488 16.0275 24.7933C16.2218 24.7413 16.416 24.6178 16.6102 24.4191L15.463 22.2868C15.3958 22.1614 15.443 22.0057 15.5719 21.9376C15.699 21.8714 15.8569 21.9179 15.9259 22.0451L17.113 24.2508C17.2673 24.3385 17.447 24.3618 17.6177 24.3349C17.7683 24.3099 17.9117 24.2472 18.0315 24.1541C18.1459 24.0646 18.2348 23.9482 18.2784 23.8157C18.3256 23.6725 18.322 23.506 18.2439 23.3234L18.1404 23.1497ZM18.4853 22.7021H19.3548V17.3221C19.3548 17.3149 19.3548 17.3078 19.3548 17.3024C19.3548 17.2182 19.3603 17.1395 19.3712 17.0643L18.4871 16.4824C18.4817 16.4788 18.4781 16.4752 18.4726 16.4717C18.3583 16.3947 18.2203 16.2998 18.0805 16.2049C17.5777 15.8576 17.0604 15.4995 16.5031 15.3563C16.2018 15.2775 15.8533 15.2256 15.4993 15.2202C15.1889 15.2148 14.8731 15.2435 14.5754 15.3187C14.3975 15.3634 14.2268 15.4261 14.0671 15.5102C13.9255 15.5836 13.793 15.6749 13.6768 15.7824L13.0833 16.4788C13.0651 16.5128 13.0397 16.5433 13.0088 16.5665L11.3715 18.484C11.3951 18.6058 11.4441 18.7096 11.5113 18.792C11.5893 18.8869 11.6928 18.9585 11.8126 19.0014C11.9397 19.048 12.0849 19.0659 12.2392 19.0534C12.4897 19.0337 12.7547 18.9316 12.9943 18.7418L13.4409 18.3766C13.5552 18.2835 13.6514 18.1976 13.7458 18.1116C13.9564 17.9218 14.1651 17.7339 14.3848 17.6049C14.8948 17.306 15.4013 17.2952 15.9041 18.1259L15.9095 18.1349L18.4853 22.7021ZM19.451 23.2177H18.7558C18.8502 23.4881 18.8447 23.7459 18.7703 23.9733C18.6941 24.2078 18.5434 24.4065 18.3528 24.5569C18.1658 24.7037 17.9371 24.804 17.6993 24.8416C17.4652 24.8792 17.2219 24.8595 16.995 24.7664C16.7318 25.0385 16.4541 25.2104 16.1637 25.2892C15.8732 25.368 15.5774 25.3519 15.2797 25.2498C14.9965 25.5595 14.6843 25.7529 14.3394 25.8263C13.989 25.9015 13.6151 25.8496 13.2194 25.6652C13.096 25.7601 12.9671 25.8353 12.8328 25.8908C12.6422 25.9695 12.4425 26.0053 12.2319 25.9982C11.7309 25.9839 11.3933 25.837 11.1246 25.6007C10.8687 25.3751 10.6963 25.0851 10.502 24.7503L9.39293 22.8436H8.53252C8.48351 23.0906 8.39638 23.2983 8.26569 23.4648C8.04605 23.7441 7.72475 23.8909 7.28003 23.882C7.27458 23.882 7.27095 23.882 7.2655 23.882H5.79155C5.42125 23.9411 5.10177 23.8515 4.85309 23.5669C4.63163 23.3144 4.48097 22.9062 4.41744 22.3047C4.41562 22.2975 4.41562 22.2903 4.41381 22.2832L4.02717 17.5477C3.95093 17.0356 4.03443 16.6758 4.24318 16.4287C4.45374 16.1798 4.76959 16.067 5.15986 16.0491C5.17075 16.0473 5.18164 16.0473 5.19253 16.0473H7.17656V16.0491C7.55049 16.0455 7.8736 16.1064 8.11865 16.2622C8.3147 16.3875 8.45628 16.5647 8.52889 16.8047H10.1444C10.551 16.5343 10.9304 16.323 11.3515 16.1924C11.7636 16.0652 12.2065 16.0205 12.7366 16.0831L13.2775 15.4476C13.2866 15.435 13.2975 15.4225 13.3102 15.4118C13.4645 15.2685 13.6369 15.1486 13.822 15.0519C14.0163 14.9498 14.2268 14.8729 14.4429 14.8174C14.7877 14.7296 15.1526 14.6956 15.5066 14.7028C15.9077 14.7099 16.298 14.769 16.6356 14.8549C17.28 15.0215 17.8373 15.4064 18.3782 15.7806C18.4998 15.8647 18.6178 15.9471 18.7612 16.0402C18.7667 16.0438 18.7703 16.0455 18.7758 16.0491L19.5599 16.5647C19.5999 16.511 19.6452 16.4609 19.6961 16.4161C19.9212 16.2174 20.2425 16.1189 20.6545 16.1189V16.1172H22.4806C22.4879 16.1172 22.4951 16.1172 22.5024 16.1189C22.9707 16.1172 23.3501 16.2013 23.6133 16.4018C23.9074 16.6238 24.0435 16.964 23.9854 17.451C23.9854 17.4546 23.9836 17.4599 23.9836 17.4635L23.4917 22.4909C23.4554 22.9402 23.3338 23.2929 23.1196 23.5364C22.8908 23.796 22.5732 23.9249 22.1557 23.907H20.5329C20.2515 23.9214 20.0137 23.8462 19.8141 23.6886C19.6688 23.574 19.5472 23.4165 19.451 23.2177ZM6.27803 21.2645C6.47226 21.2645 6.63018 21.4202 6.63018 21.6118C6.63018 21.8034 6.47226 21.9591 6.27803 21.9591C6.0838 21.9591 5.92588 21.8034 5.92588 21.6118C5.92588 21.4202 6.0838 21.2645 6.27803 21.2645ZM21.6511 21.2645C21.8453 21.2645 22.0032 21.4202 22.0032 21.6118C22.0032 21.8034 21.8453 21.9591 21.6511 21.9591C21.4568 21.9591 21.2989 21.8034 21.2989 21.6118C21.2989 21.4202 21.4568 21.2645 21.6511 21.2645ZM8.06057 19.7802L8.05149 17.0625C8.05149 17.0571 8.05149 17.0517 8.05149 17.0464C8.02245 16.8763 7.94802 16.7635 7.8373 16.6919C7.68663 16.5952 7.45973 16.5576 7.18019 16.5612H7.17656V16.563H5.19435C5.19072 16.563 5.18709 16.563 5.18346 16.563C4.9384 16.5737 4.74962 16.6328 4.64434 16.7581C4.5318 16.8906 4.49186 17.1216 4.54632 17.4796C4.54813 17.4868 4.54813 17.4939 4.54813 17.5011L4.93659 22.242V22.2509C4.98923 22.7415 5.09633 23.0566 5.2488 23.2303C5.36861 23.3681 5.53379 23.4075 5.72984 23.3717C5.74436 23.3681 5.76069 23.3681 5.77522 23.3681V23.3663H7.26369C7.27277 23.3663 7.28184 23.3663 7.29092 23.3681C7.55412 23.3717 7.73564 23.2947 7.85 23.1497C7.98433 22.9796 8.04786 22.7129 8.05875 22.3691L8.06057 19.7802Z"
                fill="#0D47A1" />
              <path
                d="M19.9266 16.9783C19.923 17.0088 19.9139 17.0392 19.8994 17.0661C19.883 17.1341 19.8758 17.2111 19.8758 17.2952C19.8758 17.3042 19.8776 17.3113 19.8776 17.3203V22.8973C19.9484 23.0727 20.0373 23.2034 20.1426 23.2858C20.2406 23.3628 20.3604 23.4004 20.5075 23.3932C20.5184 23.3914 20.5274 23.3914 20.5383 23.3914H22.1593C22.1629 23.3914 22.1684 23.3914 22.172 23.3914C22.4243 23.4039 22.6077 23.3359 22.7293 23.198C22.8672 23.0423 22.9471 22.7881 22.9743 22.4497V22.4461L23.4681 17.4134C23.4681 17.4062 23.4681 17.3973 23.4699 17.3901C23.5044 17.1036 23.4409 16.9175 23.2975 16.81C23.1341 16.6847 22.86 16.6328 22.5078 16.6346C22.4988 16.6346 22.4915 16.6364 22.4824 16.6364H20.6563V16.6346C20.3768 16.6346 20.1735 16.6901 20.0482 16.8011C19.9956 16.8494 19.9556 16.9067 19.9266 16.9783ZM13.0179 8.83037H15.483C15.5792 8.83037 15.6572 8.90557 15.6572 8.99867V10.9C15.6572 10.9931 15.5792 11.0683 15.483 11.0683H13.0179C12.9217 11.0683 12.8436 10.9931 12.8436 10.9V8.99867C12.8436 8.90557 12.9217 8.83037 13.0179 8.83037ZM15.3087 10.1176H14.3938V10.7317H15.3087V10.1176ZM14.0526 10.1176H13.1922V10.7317H14.0526V10.1176ZM13.1922 9.78106H14.0526V9.16696H13.1922V9.78106ZM14.3938 9.78106H15.3087V9.16696H14.3938V9.78106ZM8.29473 15.3634V10.9305C8.01519 11.0361 7.75561 11.0379 7.53779 10.9663C7.36897 10.9108 7.22557 10.8087 7.12029 10.678C7.01501 10.5473 6.94784 10.388 6.92788 10.2143C6.89702 9.94398 6.9787 9.63425 7.21468 9.34779C7.22557 9.33347 7.24009 9.31914 7.25461 9.3084L14.0617 4.06442C14.1488 3.98386 14.2867 3.97669 14.3829 4.05189L21.2045 9.28154C21.2154 9.28871 21.2245 9.29766 21.2336 9.3084C21.5494 9.6432 21.6275 10.0156 21.5512 10.3307C21.5131 10.4847 21.4387 10.6261 21.3352 10.7407C21.2317 10.8553 21.0992 10.9448 20.9486 10.9985C20.709 11.0844 20.4258 11.0809 20.139 10.9466V15.1593L19.6398 14.7601V10.6548C19.6398 10.6386 19.6416 10.6225 19.6452 10.6082L14.274 6.49216L8.78665 10.61C8.7921 10.6297 8.79573 10.6512 8.79573 10.6727V15.2309L8.29473 15.3634ZM7.5868 9.67363C7.45429 9.84372 7.40709 10.0138 7.42343 10.1588C7.4325 10.2412 7.46336 10.3146 7.51056 10.3737C7.55594 10.431 7.61947 10.4757 7.69571 10.5008C7.82641 10.5438 7.9934 10.533 8.18582 10.4435L14.1234 5.98727C14.196 5.93356 14.2867 5.92282 14.3648 5.95326C14.4156 5.97295 14.4664 6.01771 14.51 6.04994L20.1989 10.4095C20.2007 10.4113 20.2043 10.4131 20.2061 10.4148C20.4167 10.5652 20.62 10.5885 20.7761 10.533C20.8487 10.5079 20.9123 10.465 20.9613 10.4095C21.0103 10.354 21.0466 10.2877 21.0647 10.2143C21.1047 10.0514 21.0575 9.85267 20.8796 9.65573L14.2305 4.55856L7.5868 9.67363ZM12.6349 12.6635H15.6246C15.7589 12.6635 15.8678 12.7745 15.8678 12.9106V13.9598L15.3831 13.7933V13.1577H12.8763V13.7933L12.3917 13.9598V12.9106C12.3917 12.7745 12.5006 12.6635 12.6349 12.6635ZM18.1404 23.1497C18.1005 23.1139 18.0733 23.0656 18.0624 23.0119L15.4576 18.3873C15.1689 17.9111 14.9075 17.8986 14.6552 18.0472C14.4773 18.1528 14.2886 18.3229 14.0998 18.493C13.9854 18.595 13.8729 18.6989 13.7749 18.7758L13.3283 19.1411L13.3247 19.1429C12.9998 19.4025 12.6313 19.5403 12.2827 19.569C12.054 19.5869 11.8308 19.5582 11.6311 19.4848C11.4242 19.4078 11.2426 19.2843 11.1047 19.116C10.9558 18.9352 10.8596 18.7042 10.836 18.4303H10.8378C10.8324 18.3659 10.8524 18.2978 10.8977 18.2441L12.3209 16.5737C12.0214 16.5701 11.7563 16.6095 11.5077 16.6865C11.1283 16.8029 10.7743 17.007 10.3859 17.2666C10.3423 17.3006 10.286 17.3221 10.2243 17.3221H8.58153V20.3764L8.58879 22.3297H9.54178C9.63254 22.3297 9.71967 22.3763 9.76868 22.4586L10.9522 24.4961C11.1246 24.7933 11.2771 25.0493 11.4695 25.2194C11.6492 25.3769 11.8834 25.4754 12.2446 25.4861C12.3808 25.4897 12.5097 25.4664 12.6331 25.4163C12.7093 25.3859 12.7837 25.3447 12.8564 25.291L12.0885 23.8748C12.0214 23.7495 12.0686 23.5937 12.1956 23.5275C12.3227 23.4612 12.4806 23.5078 12.5478 23.6331L13.3828 25.1728C13.6932 25.3286 13.9745 25.3769 14.2287 25.3232C14.461 25.2731 14.6788 25.137 14.8839 24.915L13.6823 23.053C13.6042 22.9331 13.6405 22.7755 13.7621 22.6985C13.8838 22.6216 14.0435 22.6574 14.1216 22.7773L15.3886 24.7396C15.61 24.8291 15.8242 24.8488 16.0275 24.7933C16.2218 24.7413 16.416 24.6178 16.6102 24.4191L15.463 22.2868C15.3958 22.1614 15.443 22.0057 15.5719 21.9376C15.699 21.8714 15.8569 21.9179 15.9259 22.0451L17.113 24.2508C17.2673 24.3385 17.447 24.3618 17.6177 24.3349C17.7683 24.3099 17.9117 24.2472 18.0315 24.1541C18.1459 24.0646 18.2348 23.9482 18.2784 23.8157C18.3256 23.6725 18.322 23.506 18.2439 23.3234L18.1404 23.1497ZM18.4853 22.7021H19.3548V17.3221C19.3548 17.3149 19.3548 17.3078 19.3548 17.3024C19.3548 17.2182 19.3603 17.1395 19.3712 17.0643L18.4871 16.4824C18.4817 16.4788 18.4781 16.4752 18.4726 16.4717C18.3583 16.3947 18.2203 16.2998 18.0805 16.2049C17.5777 15.8576 17.0604 15.4995 16.5031 15.3563C16.2018 15.2775 15.8533 15.2256 15.4993 15.2202C15.1889 15.2148 14.8731 15.2435 14.5754 15.3187C14.3975 15.3634 14.2268 15.4261 14.0671 15.5102C13.9255 15.5836 13.793 15.6749 13.6768 15.7824L13.0833 16.4788C13.0651 16.5128 13.0397 16.5433 13.0088 16.5665L11.3715 18.484C11.3951 18.6058 11.4441 18.7096 11.5113 18.792C11.5893 18.8869 11.6928 18.9585 11.8126 19.0014C11.9397 19.048 12.0849 19.0659 12.2392 19.0534C12.4897 19.0337 12.7547 18.9316 12.9943 18.7418L13.4409 18.3766C13.5552 18.2835 13.6514 18.1976 13.7458 18.1116C13.9564 17.9218 14.1651 17.7339 14.3848 17.6049C14.8948 17.306 15.4013 17.2952 15.9041 18.1259L15.9095 18.1349L18.4853 22.7021ZM19.451 23.2177H18.7558C18.8502 23.4881 18.8447 23.7459 18.7703 23.9733C18.6941 24.2078 18.5434 24.4065 18.3528 24.5569C18.1658 24.7037 17.9371 24.804 17.6993 24.8416C17.4652 24.8792 17.2219 24.8595 16.995 24.7664C16.7318 25.0385 16.4541 25.2104 16.1637 25.2892C15.8732 25.368 15.5774 25.3519 15.2797 25.2498C14.9965 25.5595 14.6843 25.7529 14.3394 25.8263C13.989 25.9015 13.6151 25.8496 13.2194 25.6652C13.096 25.7601 12.9671 25.8353 12.8328 25.8908C12.6422 25.9695 12.4425 26.0053 12.2319 25.9982C11.7309 25.9839 11.3933 25.837 11.1246 25.6007C10.8687 25.3751 10.6963 25.0851 10.502 24.7503L9.39293 22.8436H8.53252C8.48351 23.0906 8.39638 23.2983 8.26569 23.4648C8.04605 23.7441 7.72475 23.8909 7.28003 23.882C7.27458 23.882 7.27095 23.882 7.2655 23.882H5.79155C5.42125 23.9411 5.10177 23.8515 4.85309 23.5669C4.63163 23.3144 4.48097 22.9062 4.41744 22.3047C4.41562 22.2975 4.41562 22.2903 4.41381 22.2832L4.02717 17.5477C3.95093 17.0356 4.03443 16.6758 4.24318 16.4287C4.45374 16.1798 4.76959 16.067 5.15986 16.0491C5.17075 16.0473 5.18164 16.0473 5.19253 16.0473H7.17656V16.0491C7.55049 16.0455 7.8736 16.1064 8.11865 16.2622C8.3147 16.3875 8.45628 16.5647 8.52889 16.8047H10.1444C10.551 16.5343 10.9304 16.323 11.3515 16.1924C11.7636 16.0652 12.2065 16.0205 12.7366 16.0831L13.2775 15.4476C13.2866 15.435 13.2975 15.4225 13.3102 15.4118C13.4645 15.2685 13.6369 15.1486 13.822 15.0519C14.0163 14.9498 14.2268 14.8729 14.4429 14.8174C14.7877 14.7296 15.1526 14.6956 15.5066 14.7028C15.9077 14.7099 16.298 14.769 16.6356 14.8549C17.28 15.0215 17.8373 15.4064 18.3782 15.7806C18.4998 15.8647 18.6178 15.9471 18.7612 16.0402C18.7667 16.0438 18.7703 16.0455 18.7758 16.0491L19.5599 16.5647C19.5999 16.511 19.6452 16.4609 19.6961 16.4161C19.9212 16.2174 20.2425 16.1189 20.6545 16.1189V16.1172H22.4806C22.4879 16.1172 22.4951 16.1172 22.5024 16.1189C22.9707 16.1172 23.3501 16.2013 23.6133 16.4018C23.9074 16.6238 24.0435 16.964 23.9854 17.451C23.9854 17.4546 23.9836 17.4599 23.9836 17.4635L23.4917 22.4909C23.4554 22.9402 23.3338 23.2929 23.1196 23.5364C22.8908 23.796 22.5732 23.9249 22.1557 23.907H20.5329C20.2515 23.9214 20.0137 23.8462 19.8141 23.6886C19.6688 23.574 19.5472 23.4165 19.451 23.2177ZM6.27803 21.2645C6.47226 21.2645 6.63018 21.4202 6.63018 21.6118C6.63018 21.8034 6.47226 21.9591 6.27803 21.9591C6.0838 21.9591 5.92588 21.8034 5.92588 21.6118C5.92588 21.4202 6.0838 21.2645 6.27803 21.2645ZM21.6511 21.2645C21.8453 21.2645 22.0032 21.4202 22.0032 21.6118C22.0032 21.8034 21.8453 21.9591 21.6511 21.9591C21.4568 21.9591 21.2989 21.8034 21.2989 21.6118C21.2989 21.4202 21.4568 21.2645 21.6511 21.2645ZM8.06057 19.7802L8.05149 17.0625C8.05149 17.0571 8.05149 17.0517 8.05149 17.0464C8.02245 16.8763 7.94802 16.7635 7.8373 16.6919C7.68663 16.5952 7.45973 16.5576 7.18019 16.5612H7.17656V16.563H5.19435C5.19072 16.563 5.18709 16.563 5.18346 16.563C4.9384 16.5737 4.74962 16.6328 4.64434 16.7581C4.5318 16.8906 4.49186 17.1216 4.54632 17.4796C4.54813 17.4868 4.54813 17.4939 4.54813 17.5011L4.93659 22.242V22.2509C4.98923 22.7415 5.09633 23.0566 5.2488 23.2303C5.36861 23.3681 5.53379 23.4075 5.72984 23.3717C5.74436 23.3681 5.76069 23.3681 5.77522 23.3681V23.3663H7.26369C7.27277 23.3663 7.28184 23.3663 7.29092 23.3681C7.55412 23.3717 7.73564 23.2947 7.85 23.1497C7.98433 22.9796 8.04786 22.7129 8.05875 22.3691L8.06057 19.7802Z"
                stroke="#0D47A1" stroke-width="0.6" mask="url(#path-2-outside-1_143_62822)" />
            </g>
            <defs>
              <clipPath id="clip0_143_62822">
                <rect width="25" height="27" fill="white" transform="translate(4 4)" />
              </clipPath>
            </defs>
          </svg>

        </span>
        <span class="menu-title" translate="MENU.SUBSCRIPTION"></span>
      </a>
    </div>

    <!-- ********************************************************** -->
    <!-- developer -->
    <!-- Dashboard -->
    <div class="menu-item" *ngIf="user?.role === 'developer'">
      <a class="menu-link without-sub" routerLink="/developer/dashboards" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_66_13748" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
              height="24">
              <rect width="24" height="24" fill="#0D47A1" />
            </mask>
            <g mask="url(#mask0_66_13748)">
              <path stroke="#0D47A1" stroke-width="1"
                d="M4.40723 15.5H9.84766C9.97964 15.5001 10.0645 15.5391 10.1377 15.6123C10.2093 15.6839 10.2499 15.7699 10.25 15.9082V19.5918C10.2499 19.6944 10.2273 19.7683 10.1855 19.8291L10.1367 19.8867C10.0617 19.961 9.97477 20 9.84277 20H4.40234C4.30348 20 4.23083 19.9783 4.16992 19.9365L4.1123 19.8877C4.0407 19.8161 4.00009 19.73 4 19.5918V15.9082C4.00007 15.7712 4.04084 15.6852 4.11328 15.6133C4.18833 15.539 4.27521 15.5 4.40723 15.5ZM14.1572 12H19.5977C19.6965 12.0001 19.7692 12.0217 19.8301 12.0635L19.8877 12.1123C19.9618 12.1864 20 12.2713 20 12.4004V19.6006C19.9999 19.6962 19.9786 19.7673 19.9365 19.8281L19.8867 19.8867C19.8117 19.961 19.7248 20 19.5928 20H14.1523C14.0535 20 13.9808 19.9783 13.9199 19.9365L13.8623 19.8877C13.7882 19.8136 13.75 19.7288 13.75 19.5996V12.3994C13.7501 12.3038 13.7714 12.2327 13.8135 12.1719L13.8633 12.1133C13.9383 12.039 14.0252 12 14.1572 12ZM4.40723 4H9.84766C9.94652 4.00006 10.0192 4.02169 10.0801 4.06348L10.1377 4.1123C10.2117 4.18639 10.25 4.27126 10.25 4.40039V11.6006C10.2499 11.6962 10.2286 11.7673 10.1865 11.8281L10.1367 11.8867C10.0617 11.961 9.97477 12 9.84277 12H4.40234C4.3035 12 4.23083 11.9783 4.16992 11.9365L4.1123 11.8877C4.03823 11.8136 4 11.7287 4 11.5996V4.39941L4.00684 4.31152C4.01628 4.25758 4.03538 4.21246 4.06348 4.17188L4.11328 4.11328C4.18833 4.03897 4.27522 4 4.40723 4ZM14.1572 4H19.5977C19.6965 4.00006 19.7692 4.02171 19.8301 4.06348L19.8877 4.1123C19.9593 4.18391 19.9999 4.26999 20 4.4082V8.0918C19.9999 8.22881 19.9592 8.31485 19.8867 8.38672C19.8117 8.46103 19.7248 8.5 19.5928 8.5H14.1523C14.0203 8.49993 13.9355 8.46085 13.8623 8.3877C13.7907 8.3161 13.7501 8.23005 13.75 8.0918V4.4082C13.7501 4.30557 13.7727 4.2317 13.8145 4.1709L13.8633 4.11328C13.9383 4.03898 14.0252 4 14.1572 4Z" />
            </g>
          </svg>
        </span>
        <span class="menu-title" translate="MENU.DASHBOARD"></span>
      </a>
    </div>

    <!-- project -->
    <div class="menu-item menu-accordion" data-kt-menu-trigger="click" *ngIf="user?.role === 'developer'"
      [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
      <span class="menu-link">
        <span class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_136_62239)">
              <path stroke="#0D47A1" fill="#0D47A1" stroke-width="0.5"
                d="M19.4998 18.9999H18.9998V11H17.9998V10H16.9998V11H15.9998V13H14.9999V11V9.00003H13.9999V11H12.9999V8.00004H11.9999V11V13H10.9999V11H9.9999V9.00003H8.99991V11H7.99992V18.9999H5.99994V8.00004H6.99993C7.55227 8.00004 7.99992 7.55239 7.99992 7.00005V6.00006H16.9998V9.00003H17.9998V6.00006H18.9998C19.4635 6.00006 19.8665 5.68108 19.9731 5.22972C20.0798 4.7784 19.8618 4.31305 19.4471 4.10575L17.4471 3.10576C17.3082 3.03642 17.1551 3.00009 16.9998 3.00009H6.10928L4.89429 0.552773C4.7223 0.208792 4.3723 0.00012207 4.00031 0.00012207C3.92399 0.00012207 3.84699 0.00879386 3.77031 0.027114C3.31895 0.133441 2.99997 0.536445 2.99997 1.00011V3.00009H0.99999C0.447652 3.00009 0 3.44774 0 4.00008V5.00007C0 5.55241 0.447652 6.00006 0.99999 6.00006H2.99997V7.00005V18.9999H0.499995C0.22367 18.9999 0 19.2236 0 19.4999C0 19.7763 0.22367 19.9999 0.499995 19.9999H19.4998C19.7761 19.9999 19.9998 19.7763 19.9998 19.4999C19.9998 19.2236 19.7761 18.9999 19.4998 18.9999ZM2.99997 5.00007H0.99999V4.00008H2.99997V5.00007ZM16.9998 4.00008L18.9998 5.00007H7.99992C7.99992 4.73472 7.89457 4.48043 7.70692 4.29309L7.41426 4.00008H16.9998ZM3.99996 1.00011L4.99995 3.00009H3.99996V1.00011ZM3.99996 4.00008H5.99994L6.99993 5.00007V7.00005H3.99996V4.00008ZM4.99995 18.9999H3.99996V8.00004H4.99995V18.9999ZM17.9998 18.9999H8.99991V12H9.9999V14H12.9999V12H13.9999V14H16.9998V12H17.9998V18.9999Z" />
            </g>
            <defs>
              <clipPath id="clip0_136_62239">
                <rect width="20" height="20" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </span>
        <span class="menu-title">{{ getTranslatedText('PROJECT') }}</span>
        <span class="menu-arrow" [class.rtl-arrow]="translationService.getCurrentLanguage() === 'ar'"></span>
      </span>
      <div class="menu-sub menu-sub-accordion" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
        <div class="menu-item">
          <a class="menu-link" routerLink="/developer/projects" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }">
            <span class="menu-bullet" [class.rtl-bullet]="translationService.getCurrentLanguage() === 'ar'">
              <span class="bullet bullet-dot"></span>
            </span>
            <span class="menu-title">{{ getTranslatedText('VIEW_ALL_PROJECTS') }}</span>
          </a>
        </div>
        <div class="menu-item">
          <a class="menu-link" routerLink="/developer/projects/create" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }">
            <span class="menu-bullet" [class.rtl-bullet]="translationService.getCurrentLanguage() === 'ar'">
              <span class="bullet bullet-dot"></span>
            </span>
            <span class="menu-title">{{ getTranslatedText('CREATE_PROJECT') }}</span>
          </a>
        </div>
      </div>
    </div>

    <!-- contract request -->
    <div class="menu-item" *ngIf="user?.role === 'developer'"
      [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
      <a class="menu-link without-sub" routerLink="/developer/projects/requests" routerLinkActive="active">
        <span class="menu-icon">
          <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_24_2533)">
              <path stroke="#0D47A1" stroke-width="1"
                d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
              <path stroke="#0D47A1" stroke-width="1"
                d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
              <path stroke="#0D47A1" stroke-width="1"
                d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
            </g>
            <defs>
              <clipPath id="clip0_24_2533">
                <rect width="19" height="19" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </span>
        <span class="menu-title">{{ getTranslatedText('CONTRACT_REQUESTS') }}</span>
      </a>
    </div>

    <!-- __________________________________________________________________________________________________________________________________________ -->
     <!-- Accounts -->
    <div class="menu-item" *ngIf="user?.role === 'developer' || user?.role === 'broker'">
      <a class="menu-link without-sub" (click)="navigateToAccounts()" style="cursor: pointer;">
        <span class="menu-icon">
          <app-keenicon name="people" class="fs-2 text-dark-blue" type="outline"></app-keenicon>
        </span>
        <span class="menu-title" translate="MENU.ACCOUNTS"></span>
      </a>
    </div>


    <!-- Profile -->
    <div class="menu-item" *ngIf="user?.role != 'admin'">
      <a class="menu-link without-sub" (click)="navigateToProfile()" style="cursor: pointer;">
        <span class="menu-icon">
          <app-keenicon name="user" class="fs-2 text-dark-blue" type="outline"></app-keenicon>
        </span>
        <span class="menu-title" translate="MENU.PROFILE"></span>
      </a>
    </div>

    <!-- Notifications -->
    <!-- <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/" routerLinkActive="">
        <span class="menu-icon">
          <app-keenicon name="notification-on" class="fs-2 text-dark-blue" type="outline"></app-keenicon>
        </span>
        <span class="menu-title" translate="MENU.NOTIFICATIONS"></span>
      </a>
    </div> -->

    <!-- chat -->
    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/chat" routerLinkActive="active">
        <span class="menu-icon">
          <app-keenicon name="messages" class="fs-2 text-dark-blue" type="outline"></app-keenicon>
          <!-- <span *ngIf="messageNotificationCounter > 0" -->
          <span class="notification-badge position-absolute translate-middle badge rounded-pill bg-success">
            {{ messageNotificationCounter }}
          </span>
        </span>
        <span class="menu-title" translate="MENU.MESSAGES"></span>
      </a>
    </div>

    <!-- Settings -->
    <!-- <div class="menu-item" *ngIf="user?.role === 'broker' || user?.role === 'developer'"> -->
    <div class="menu-item" *ngIf="user?.role === 'admin' ">
      <a class="menu-link without-sub" routerLink="/super-admin/settings" routerLinkActive="active">
        <span class="menu-icon">
          <app-keenicon name="gear" class="fs-2 text-dark-blue" type="outline"></app-keenicon>
        </span>
        <span class="menu-title" translate="MENU.SETTINGS"></span>
      </a>
    </div>

    <!-- Help -->
    <!-- <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/" routerLinkActive="">
        <span class="menu-icon">
          <i class="fa-regular fa-circle-question fs-3 text-dark-blue"></i>
        </span>
        <span class="menu-title" translate="MENU.HELP"></span>
      </a>
    </div> -->
  </div>
</div>

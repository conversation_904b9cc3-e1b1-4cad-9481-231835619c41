<div class="filter-dropdown" [class.rtl-filter]="translationService.getCurrentLanguage() === 'ar'">
  <div class="mb-2">
    <div class="d-flex gap-3 flex-wrap">
      <!-- Top row: Inside & Village -->
      <div class="form-check">
        <input
          type="radio"
          value="inside_compound"
          id="inside_compound"
          class="form-check-input"
          [(ngModel)]="filter.compoundType"
          name="compoundType"
        />
        <label class="form-check-label ms-1 badge badge-light-primary" for="inside_compound">
          {{ getTranslatedText('INSIDE_COMPOUND') }}
        </label>
      </div>

      <div class="form-check">
        <input
          type="radio"
          value="village"
          id="village"
          class="form-check-input"
          [(ngModel)]="filter.compoundType"
          name="compoundType"
        />
        <label class="form-check-label ms-1 badge badge-light-success" for="village">
          {{ getTranslatedText('VILLAGE') }}
        </label>
      </div>
    </div>

    <!-- Bottom row: Outside Compound -->
    <div class="form-check mt-2">
      <input
        type="radio"
        value="outside_compound"
        id="outside_compound"
        class="form-check-input"
        [(ngModel)]="filter.compoundType"
        name="compoundType"
      />
      <label class="form-check-label ms-1 badge badge-light-warning" for="outside_compound">
        {{ getTranslatedText('OUTSIDE_COMPOUND') }}
      </label>
    </div>
  </div>

  <!-- Separator Line -->
  <hr class="my-3" />

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('AREA') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let area of areas" [value]="area.id">{{ getAreaName(area) }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_TYPE') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.unitType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let unit of unitTypes" [value]="unit.value">{{ unit.key}}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('VIEW') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let view of views" [value]="view.value">{{ getViewName(view) }}</option>
    </select>
  </div>

   <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_AREA') }}:</label>
    <div class="d-flex gap-2">
      <input
        type="number"
        class="form-control form-control-sm"
        [placeholder]="getTranslatedText('FROM')"
        [(ngModel)]="filter.unitAreaFrom"
      />
      <input
        type="number"
        class="form-control form-control-sm"
        [placeholder]="getTranslatedText('TO')"
        [(ngModel)]="filter.unitAreaTo"
      />
    </div>
  </div>

   <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('PRICE') }}:</label>
    <!-- <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_PRICE')"
      [(ngModel)]="filter.price" /> -->
    <div class="d-flex gap-2">
      <input
        type="number"
        class="form-control form-control-sm"
        [placeholder]="getTranslatedText('FROM')"
        [(ngModel)]="filter.priceFrom"
      />
      <input
        type="number"
        class="form-control form-control-sm"
        [placeholder]="getTranslatedText('TO')"
        [(ngModel)]="filter.priceTo"
      />
    </div>
  </div>

  <!-- <div class="mb-2">
    <label class="form-label">Finishing Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">select</option>
      <option *ngFor="let type of finishingTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.status">
      <option value="">Select</option>
      <option *ngFor="let state of status" [value]="state.value">{{ state.key }}</option>
    </select>
  </div> -->

  <button class="btn btn-sm btn-primary w-100" (click)="apply()">{{ getTranslatedText('APPLY') }}</button>
</div>

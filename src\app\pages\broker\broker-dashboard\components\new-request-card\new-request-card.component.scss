// ===== NEW REQUEST CARD RESPONSIVE DESIGN =====

// Mobile first - stack vertically
.card-header {
  flex-direction: column !important;
  align-items: stretch !important;
  gap: 1rem !important;
  padding: 1rem !important;
  flex-wrap: nowrap !important;

  .card-title {
    width: 100% !important;
    text-align: center !important;

    .card-label {
      font-size: 1rem !important;
      line-height: 1.4 !important;
      word-wrap: break-word !important;
      white-space: normal !important;

      svg {
        margin-right: 0.5rem !important;
        vertical-align: middle !important;
      }
    }

    .text-muted {
      font-size: 0.8rem !important;
      margin-top: 0.5rem !important;
      text-align: center !important;
    }
  }

  .card-toolbar {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    margin-left: 0 !important;

    .btn {
      font-size: 0.85rem !important;
      padding: 0.6rem 1.5rem !important;
      white-space: nowrap !important;
      min-width: 140px !important;

      i {
        margin-right: 0.5rem !important;
      }
    }
  }
}

// Small screens (576px+) - side by side
@media (min-width: 576px) {
  .card-header {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.25rem !important;

    .card-title {
      flex: 1 !important;
      text-align: left !important;
      margin-right: 1rem !important;

      .card-label {
        font-size: 1.1rem !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }

      .text-muted {
        font-size: 0.85rem !important;
        text-align: left !important;
      }
    }

    .card-toolbar {
      width: auto !important;
      justify-content: flex-end !important;
      flex-shrink: 0 !important;

      .btn {
        font-size: 0.9rem !important;
        padding: 0.6rem 1.2rem !important;
        min-width: auto !important;
      }
    }
  }
}

// Medium screens (768px+)
@media (min-width: 768px) {
  .card-header {
    padding: 1.5rem !important;

    .card-title {
      .card-label {
        font-size: 1.2rem !important;
      }

      .text-muted {
        font-size: 0.9rem !important;
      }
    }

    .card-toolbar {
      .btn {
        font-size: 0.9rem !important;
        padding: 0.7rem 1.4rem !important;
      }
    }
  }
}

// Large screens (1024px+)
@media (min-width: 1024px) {
  .card-header {
    padding: 1.5rem 1.5rem 1.25rem 1.5rem !important;

    .card-title {
      .card-label {
        font-size: 1rem !important;
      }

      .text-muted {
        font-size: 0.8rem !important;
      }
    }

    .card-toolbar {
      .btn {
        font-size: 0.85rem !important;
        padding: 0.5rem 1rem !important;
      }
    }
  }
}

// Arabic specific fixes
:host-context(html[lang="ar"]) {
  .card-header {
    .card-title {
      .card-label {
        svg {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }
    }

    .card-toolbar {
      .btn {
        i {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }
    }
  }

  // Small screens and up - RTL adjustments
  @media (min-width: 576px) {
    .card-header {
      .card-title {
        text-align: right !important;
        margin-right: 0 !important;
        margin-left: 1rem !important;

        .text-muted {
          text-align: right !important;
        }
      }

      .card-toolbar {
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }
    }
  }
}

import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { ContractService } from '../../../services/contract.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslationService } from 'src/app/modules/i18n';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-contract-requests',
  templateUrl: './contract-requests.component.html',
  styleUrl: './contract-requests.component.scss',
})
export class ContractRequestsComponent
  extends BaseGridComponent
  implements OnInit
{
  user: any;

  selectedImages: any[] = [];
  currentImageIndex: number = 0;
  selectedCompanyName: string = '';

  constructor(
    protected cd: ChangeDetectorRef,
    private contractService: ContractService,
    private modalService: NgbModal,
    public translationService: TranslationService
  ) {
    super(cd);
    this.setService(contractService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.page.filters = {developerId : this.user.developerId};

  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this.contractService.getContractRequests(this.page).subscribe(
      (response: any) => {
        console.log('API Response:', response);
        console.log('Response Data:', response.data);
        this.rows = Array.isArray(response.data) ? response.data : [];
        console.log('Rows after assignment:', this.rows);
        this.rows = [...this.rows];

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire(
          'Failed to load contract requests. Please try again later.',
          '',
          'error'
        );
      }
    );
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase().trim()) {
      case 'pending':
        return 'badge-light-secondary';
      case 'accepted':
        return 'badge-light-success';
      case 'declined':
        return 'badge-light-danger';

      default:
        return 'badge-light-secondary';
    }
  }

  openCompanyModal(content: any, request: any): void {
    this.selectedCompanyName = request.companyName || 'N/A';
    this.selectedImages = [];

    if (request.broker?.image) {
      this.selectedImages.push({
        url: request.broker.image,
        type: 'image',
      });
    }

    if (request.broker?.gallery && Array.isArray(request.broker.gallery)) {
      request.broker.gallery.forEach((item: any) => {
        if (item.url) {
          this.selectedImages.push({
            url: item.url,
            type: 'image',
          });
        }
      });
    }

    this.currentImageIndex = 0;

    this.modalService.open(content, {
      centered: true,
      size: 'lg',
      windowClass: 'company-images-modal',
      backdrop: 'static',
    });
  }

  nextImage(): void {
    if (this.selectedImages.length > 1) {
      this.currentImageIndex =
        (this.currentImageIndex + 1) % this.selectedImages.length;
    }
  }

  prevImage(): void {
    if (this.selectedImages.length > 1) {
      this.currentImageIndex =
        this.currentImageIndex > 0
          ? this.currentImageIndex - 1
          : this.selectedImages.length - 1;
    }
  }

  hasMultipleImages(): boolean {
    return this.selectedImages.length > 1;
  }

  getCurrentImage(): any {
    return this.selectedImages[this.currentImageIndex] || null;
  }

  hasImages(): boolean {
    return this.selectedImages.length > 0;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'CONTRACT_REQUESTS': 'طلبات التعاقد',
        'VIEW_AND_MANAGE_CONTRACT_REQUESTS': 'عرض وإدارة طلبات التعاقد من الوسطاء',
        'BROKER_NAME': 'اسم الوسيط',
        'CONTRACT_IMAGE': 'صورة التعاقد',
        'REQUEST_DATE': 'تاريخ الطلب',
        'STATUS': 'الحالة',
        'CONTACT_INFO': 'معلومات الاتصال',
        'ACTIONS': 'الإجراءات',
        'CHAT': 'المحادثة',
        'CHATS': 'المحادثات',
        'CONTRACT_IMAGES': 'صور التعاقد',
        'PENDING': 'قيد الانتظار',
        'ACCEPTED': 'مقبول',
        'DECLINED': 'مرفوض',
        'LOADING': 'جاري التحميل...',
        'NO_CONTRACT_REQUESTS_FOUND': 'لا توجد طلبات تعاقد',
        'ID': 'المعرف'
      },
      'en': {
        'CONTRACT_REQUESTS': 'Contract Requests',
        'VIEW_AND_MANAGE_CONTRACT_REQUESTS': 'View and manage contract requests from brokers',
        'BROKER_NAME': 'Broker Name',
        'CONTRACT_IMAGE': 'Contract Image',
        'REQUEST_DATE': 'Request Date',
        'STATUS': 'Status',
        'CONTACT_INFO': 'Contact Info',
        'ACTIONS': 'Actions',
        'CHAT': 'Chat',
        'CHATS': 'Chats',
        'CONTRACT_IMAGES': 'Contract Images',
        'PENDING': 'Pending',
        'ACCEPTED': 'Accepted',
        'DECLINED': 'Declined',
        'LOADING': 'Loading...',
        'NO_CONTRACT_REQUESTS_FOUND': 'No contract requests found',
        'ID': 'ID'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  getTranslatedStatus(status: string): string {
    if (!status) return this.getTranslatedText('PENDING');
    
    switch (status.toLowerCase()) {
      case 'pending':
        return this.getTranslatedText('PENDING');
      case 'accepted':
        return this.getTranslatedText('ACCEPTED');
      case 'declined':
        return this.getTranslatedText('DECLINED');
      default:
        return status;
    }
  }

}

<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"
            [style.font-family]="translationService.isRTL() ? 'Noto Ku<PERSON> Arabic, sans-serif' : 'inherit'"
            [style.font-size]="translationService.isRTL() ? '1.6rem' : 'inherit'"
            [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
            {{ isEditMode ? ('SUBSCRIPTION.EDIT_TITLE' | translate) : ('SUBSCRIPTION.CREATE_TITLE' | translate) }}
          </h3>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()" (click)="onCancel()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUBSCRIPTION.BACK_TO_SUBSCRIPTIONS' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subscription Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form [formGroup]="subscriptionForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <!-- Basic Information -->
              <div class="col-md-6">
                <h5 class="mb-4"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
                  [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'"
                  [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  {{ 'SUBSCRIPTION.BASIC_INFORMATION' | translate }}
                </h5>

                <!-- Name -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.SUBSCRIPTION_NAME' | translate }}
                  </label>
                  <input type="text" class="form-control" formControlName="name"
                    [class.is-invalid]="isFieldInvalid('name')"
                    [placeholder]="'SUBSCRIPTION.ENTER_SUBSCRIPTION_NAME' | translate"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('name')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('name') }}
                  </div>
                </div>

                <!-- Description -->
                <div class="mb-4">
                  <label class="form-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.DESCRIPTION' | translate }}
                  </label>
                  <textarea class="form-control" formControlName="description" rows="3"
                    [placeholder]="'SUBSCRIPTION.ENTER_SUBSCRIPTION_DESCRIPTION' | translate"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'"></textarea>
                </div>

                <!-- Price -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.PRICE' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="price"
                    [class.is-invalid]="isFieldInvalid('price')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('price')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('price') }}
                  </div>
                </div>

                <!-- Image Upload -->
                <div class="mb-4">
                  <label class="form-label" [class.required]="!isEditMode"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.SUBSCRIPTION_IMAGE' | translate }}
                  </label>
                  <input type="file" class="form-control" (change)="onImageSelected($event)"
                    accept="image/jpg,image/jpeg,image/png,image/webp" [class.is-invalid]="isFieldInvalid('image')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  <div class="form-text"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    Supported formats: JPG, JPEG, PNG, WEBP. Max size: 10MB
                  </div>
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('image')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('image') }}
                  </div>

                  <!-- Image Preview -->
                  <div class="mt-3" *ngIf="imagePreview">
                    <img [src]="imagePreview" alt="Preview" class="img-thumbnail"
                      style="max-width: 200px; max-height: 150px;">
                  </div>
                  <div class="mt-3" *ngIf="!imagePreview && isEditMode">
                    <img [src]="image" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                  </div>
                </div>
              </div>

              <!-- Limits & Features -->
              <div class="col-md-6">
                <h5 class="mb-4"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
                  [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'"
                  [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  {{ 'SUBSCRIPTION.SUBSCRIPTION_LIMITS' | translate }}
                </h5>

                <!-- Max Specialization Scopes -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.MAX_SPECIALIZATION_SCOPES' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="maxSpecializationScopes"
                    [class.is-invalid]="isFieldInvalid('maxSpecializationScopes')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxSpecializationScopes')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('maxSpecializationScopes') }}
                  </div>
                </div>

                <!-- Max Specializations -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.MAX_SPECIALIZATIONS' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="maxSpecializations"
                    [class.is-invalid]="isFieldInvalid('maxSpecializations')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxSpecializations')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('maxSpecializations') }}
                  </div>
                </div>

                <!-- Max Locations -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.MAX_LOCATIONS' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="maxLocations"
                    [class.is-invalid]="isFieldInvalid('maxLocations')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxLocations')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('maxLocations') }}
                  </div>
                </div>

                <!-- Max Advertisements -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.MAX_ADVERTISEMENTS' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="maxAdvertisements"
                    [class.is-invalid]="isFieldInvalid('maxAdvertisements')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxAdvertisements')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('maxAdvertisements') }}
                  </div>
                </div>

                <!-- Max Operations -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.MAX_OPERATIONS' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="maxOperations"
                    [class.is-invalid]="isFieldInvalid('maxOperations')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxOperations')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('maxOperations') }}
                  </div>
                </div>

                <!-- Special Advertisements Count -->
                <div class="mb-4">
                  <label class="form-label required"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUBSCRIPTION.SPECIAL_ADVERTISEMENTS_COUNT' | translate }}
                  </label>
                  <input type="number" class="form-control" formControlName="specialAdvertisementsCount"
                    [class.is-invalid]="isFieldInvalid('specialAdvertisementsCount')" placeholder="0" min="0"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('specialAdvertisementsCount')"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ getFieldError('specialAdvertisementsCount') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="row mt-5">
              <div class="col-12">
                <div class="d-flex gap-3" [class.justify-content-end]="!translationService.isRTL()"
                  [class.justify-content-start]="translationService.isRTL()">
                  <button type="button" class="btn btn-secondary" (click)="onCancel()"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    <i class="fas fa-times" [class.me-2]="!translationService.isRTL()"
                      [class.ms-2]="translationService.isRTL()"></i>
                    {{ 'SUBSCRIPTION.CANCEL' | translate }}
                  </button>
                  <button type="submit" class="btn btn-primary" [disabled]="subscriptionForm.invalid"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    <i class="fas fa-save" [class.me-2]="!translationService.isRTL()"
                      [class.ms-2]="translationService.isRTL()"></i>
                    {{ isEditMode ? ('SUBSCRIPTION.UPDATE_SUBSCRIPTION' | translate) :
                    ('SUBSCRIPTION.CREATE_SUBSCRIPTION' | translate) }}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { RequestService, Filters } from './services/request.service';
import { Subscription } from 'rxjs';
import { AuthenticationService } from '../authentication';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
  styleUrls: ['./requests.component.scss']
})

export class RequestsComponent implements OnInit, OnDestroy {

  user : any;
  currentRoute: 'received' | 'sent' = 'sent';
  isLoading: boolean = false;
  searchInput: string = '';
  newRequestsCount: number = 0;
  private newRequestsCountSubscription: Subscription;

  constructor(public router: Router, private requestService: RequestService, private authenticationService:AuthenticationService, public translationService: TranslationService) {}

  ngOnInit() {
      this.user = this.authenticationService.getSessionUser();
    // Subscribe to newRequestsCount changes
    this.newRequestsCountSubscription = this.requestService.getNewRequestsCount().subscribe(count => {
      this.newRequestsCount = count;
      console.log('Received count from service:', count);
    });
  }

  ngOnDestroy() {
    // Unsubscribe to prevent memory leaks
    if (this.newRequestsCountSubscription) {
      this.newRequestsCountSubscription.unsubscribe();
    }
  }

  onFiltersChanged(filters: Filters) {
    console.log('Filters changed:', filters);
    this.requestService.setFilters(filters);
  }

  onSearchChanged(event: any) {
    this.searchInput = event.target.value;
    this.requestService.setFilters({ search: this.searchInput });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'REQUESTS': 'الطلبات',
        'SEARCH': 'بحث...',
        'RECEIVED_REQUESTS': 'الطلبات المستلمة',
        'SENT_REQUESTS': 'الطلبات المرسلة'
      },
      'en': {
        'REQUESTS': 'Requests',
        'SEARCH': 'Search...',
        'RECEIVED_REQUESTS': 'Received Requests',
        'SENT_REQUESTS': 'Sent Requests'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}

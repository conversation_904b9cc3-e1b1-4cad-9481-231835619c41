// RTL Support for Properties Table
:host-context(html[lang="ar"]) {
  .table {
    direction: rtl;
    text-align: right;

    th, td {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
    }

    .text-end {
      text-align: left !important;
    }

    .rounded-start {
      border-radius: 0 0.375rem 0.375rem 0 !important;
    }

    .rounded-end {
      border-radius: 0.375rem 0 0 0.375rem !important;
    }

    .ps-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .pe-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .ms-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .me-1, .me-2 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .btn {
      font-family: 'Hacen Liner Screen St', sans-serif;
      font-size: 1rem;
      // margin-right: 30% !important;
    }

    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .dropdown-menu {
      text-align: right;

      .dropdown-item {
        text-align: right;
        font-family: 'Hacen Liner Screen St', sans-serif;
        padding: 0.5rem 1rem;
        border: none !important;
        background: none !important;
        color: inherit !important;

        &:hover {
          background-color: #f8f9fa !important;
          color: #495057 !important;
        }

        i {
          margin-left: 0.5rem !important;
          margin-right: 0 !important;
        }
      }
    }
  }

  // Pagination RTL
  app-pagination {
    direction: rtl;
  }
}

// General dropdown styling for both languages
.dropdown-menu {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  min-width: 200px;

  .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none !important;
    background: none !important;
    color: #495057 !important;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f8f9fa !important;
      color: #495057 !important;
      transform: translateX(2px);
    }

    &:active {
      background-color: #e9ecef !important;
    }

    i {
      font-size: 0.875rem;
      width: 16px;
      text-align: center;
    }
  }

  // Specific action colors
  .dropdown-item:has(i.fa-bullhorn) {
    &:hover {
      background-color: #fff3cd !important;
      color: #856404 !important;
    }
  }

  .dropdown-item:has(i.fa-check-circle) {
    &:hover {
      background-color: #d1e7dd !important;
      color: #0f5132 !important;
    }
  }

  .dropdown-item:has(i.fa-archive) {
    &:hover {
      background-color: #f8d7da !important;
      color: #721c24 !important;
    }
  }

  .dropdown-item:has(i.fa-edit) {
    &:hover {
      background-color: #cff4fc !important;
      color: #055160 !important;
    }
  }

  .dropdown-item:has(i.fa-save) {
    &:hover {
      background-color: #d1e7dd !important;
      color: #0f5132 !important;
    }
  }
}

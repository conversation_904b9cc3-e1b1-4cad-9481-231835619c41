import { Component, Input, OnInit } from '@angular/core';
import { getCSSVariableValue } from '../../../../../_metronic/kt/_utils';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-pie-chart',
  templateUrl: './pie-chart.component.html',
  styleUrls: ['./pie-chart.component.scss']
})

export class PieChartComponent implements OnInit {

  @Input() cssClass: string = '';
  @Input() chartSize: number = 150;
  @Input() chartLine: number = 30;
  @Input() chartRotate: number = 145;
  @Input() newRequestsPercent: number = 0;
  @Input() finishedRequestsPercent: number = 0;
  @Input() inProcessingRequestsPercent: number = 0;

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.initChart();
    }, 10);
  }

  private initChart() {
    const el = document.getElementById('requests_pie_chart');
    if (!el) return;

    const options = {
      size: this.chartSize,
      lineWidth: this.chartLine,
      rotate: this.chartRotate
    };

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.height = options.size;

    el.innerHTML = '';
    el.appendChild(canvas);

    if (!ctx) return;

    // Translate and rotate the canvas
    ctx.translate(options.size / 2, options.size / 2);
    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

    const radius = (options.size - options.lineWidth) / 2;

    // Draw a single circle for the background
    const drawCircle = (color: string, lineWidth: number, startPercent: number, endPercent: number) => {
      ctx.beginPath();
      ctx.arc(
        0,
        0,
        radius,
        Math.PI * 2 * (startPercent / 100),
        Math.PI * 2 * (endPercent / 100),
        false
      );
      ctx.strokeStyle = color;
      ctx.lineCap = 'round';
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    let startPercent = 0;

    drawCircle(getCSSVariableValue('--bs-warning'), options.lineWidth, startPercent, startPercent + this.newRequestsPercent);
    startPercent += this.newRequestsPercent;

    drawCircle(getCSSVariableValue('--bs-success'), options.lineWidth, startPercent, startPercent + this.finishedRequestsPercent);
    startPercent += this.finishedRequestsPercent;

    drawCircle(getCSSVariableValue('--bs-mid-blue'), options.lineWidth, startPercent, startPercent + this.inProcessingRequestsPercent);
    // startPercent += this.inProcessingRequestsPercent;
  }

  getTranslatedText(key: string): string {
    const translations: { [key: string]: { ar: string; en: string } } = {
      'NEW': {
        ar: 'جديد',
        en: 'New'
      },
      'IN_PROCESSING': {
        ar: 'قيد المعالجة',
        en: 'In Processing'
      },
      'FINISHED': {
        ar: 'مكتمل',
        en: 'Finished'
      }
    };

    const currentLang = this.translate.currentLang || 'en';
    return translations[key]?.[currentLang as 'ar' | 'en'] || key;
  }
}

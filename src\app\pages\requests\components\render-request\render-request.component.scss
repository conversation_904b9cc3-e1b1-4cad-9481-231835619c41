// ===== SIMPLE HEADER STYLING =====

// Card improvements
.card {
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

// Title styling
h2.fs-2 {
  font-size: 1.75rem !important;
  color: #1e293b;

  @media (max-width: 768px) {
    font-size: 1.5rem !important;
  }
}

// Badge styling
.badge.badge-light-success {
  background-color: #10b981 !important;
  color: white !important;
  border-radius: 8px;
  font-weight: 600;
}

// Button improvements
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 8px;
  font-weight: 600;

  &.btn-primary {
    background-color: #3b82f6;
    border-color: #3b82f6;

    &:hover {
      background-color: #2563eb;
      border-color: #2563eb;
    }
  }

  &.btn-danger {
    background-color: #ef4444;
    border-color: #ef4444;

    &:hover {
      background-color: #dc2626;
      border-color: #dc2626;
    }
  }
}

// Info cards styling
.bg-light.rounded {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f1f5f9 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  i {
    margin-bottom: 0.5rem !important;
  }

  .text-gray-800 {
    color: #1e293b !important;
    font-size: 1rem !important;
  }

  .text-gray-600 {
    color: #64748b !important;
    font-size: 0.875rem !important;
  }
}

// Status badge and buttons alignment
.d-flex.align-items-center.gap-2 {
  flex-wrap: wrap;

  .badge {
    white-space: nowrap;
  }

  .d-flex.gap-2 {
    flex-wrap: wrap;
  }
}

// Responsive improvements
@media (max-width: 992px) {
  .d-flex.flex-lg-row {
    flex-direction: column !important;
    align-items: stretch !important;

    .d-flex.align-items-center.gap-2 {
      justify-content: center;
      margin-top: 1rem;
      flex-direction: row;
    }
  }
}

@media (max-width: 768px) {
  .card-body {
    padding: 1.5rem !important;
  }

  // Center title in small screens
  .mb-3.mb-lg-0 {
    text-align: center !important;

    h2 {
      text-align: center !important;

      // Override RTL alignment in small screens
      &.text-end {
        text-align: center !important;
      }
    }
  }

  .row.g-3 {
    .col-md-3 {
      margin-bottom: 0.75rem;
    }
  }

  .d-flex.align-items-center.gap-2 {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;

    .badge {
      text-align: center;
      align-self: center;
    }

    .d-flex.gap-2 {
      flex-direction: column !important;
      gap: 0.5rem !important;

      .btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .card-body {
    padding: 1rem !important;
  }

  // Force center alignment for title in very small screens
  .mb-3.mb-lg-0 {
    text-align: center !important;

    h2 {
      text-align: center !important;
      font-size: 1.25rem !important;

      // Override RTL alignment in very small screens
      &.text-end {
        text-align: center !important;
      }
    }
  }

  .badge {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  .bg-light.rounded {
    padding: 1rem !important;

    i {
      font-size: 1.25rem !important;
    }

    .text-gray-800 {
      font-size: 0.9rem !important;
    }

    .text-gray-600 {
      font-size: 0.8rem !important;
    }
  }
}

// ===== RTL SUPPORT FOR RENDER REQUEST =====

// Arabic font support
:host-context(html[lang="ar"]) {
  // Title styling
  .rtl-title {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    text-align: right !important;
    direction: rtl !important;
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    line-height: 1.3 !important;
  }

  // Header layout
  .rtl-header {
    direction: rtl !important;
    text-align: right !important;
    justify-content: space-between !important;
    align-items: flex-start !important;

    .d-flex.flex-column {
      align-items: flex-end !important; // Far right alignment
      order: 1 !important; // Title comes first (far right)
      flex: 1 !important; // Take available space

      .d-flex.align-items-center {
        justify-content: flex-end !important; // Far right alignment
        flex-direction: row-reverse !important; // Reversed order
      }
    }

    // Actions section
    .d-flex.my-4.rtl-actions {
      order: 2 !important; // Actions come second (far left)
      justify-content: flex-start !important;
      flex-shrink: 0 !important; // Don't shrink
    }
  }

  // Actions and Stats in same line - General layout
  .d-flex.flex-wrap.flex-stack {
    align-items: center !important; // Align items vertically
    justify-content: space-between !important; // Space between actions and stats
    flex-wrap: nowrap !important; // Keep in one line
    gap: 1rem !important; // Space between sections

    .d-flex.flex-column.flex-grow-1 {
      flex-direction: row !important; // Make stats horizontal
      align-items: center !important;
      justify-content: flex-end !important; // Stats on right side
      margin: 0 !important;
      padding: 0 !important;
      flex-shrink: 0 !important; // Don't shrink stats

      .d-flex.flex-wrap {
        margin: 0 !important;
        gap: 1rem !important;
        flex-wrap: nowrap !important; // Keep stats in one line
      }
    }
  }

  // Ensure actions stay on left and stats on right
  .rtl-actions {
    flex-shrink: 0 !important; // Don't shrink actions
    order: 1 !important; // Actions first (left side)
  }

  .d-flex.flex-column.flex-grow-1 {
    order: 2 !important; // Stats second (right side)
  }

  // User info styling - Far right alignment
  .rtl-info {
    direction: rtl !important;
    justify-content: flex-end !important; // Far right alignment
    text-align: right !important; // Right text alignment
    flex-wrap: wrap !important; // Allow wrapping on very small screens
    align-items: center !important;
    margin-right: 0 !important; // No right margin
    padding-right: 0 !important; // No right padding

    .rtl-info-item {
      font-family: 'Markazi Text', sans-serif !important;
      text-align: right !important; // Right text alignment
      margin-left: 0 !important;
      margin-right: 1.5rem !important; // More space between items for far right
      display: inline-flex !important;
      align-items: center !important;
      white-space: nowrap !important; // Prevent text wrapping within items
      flex-direction: row-reverse !important; // Icon after text for RTL

      i {
        margin-left: 0.5rem !important; // Icon on left of text in RTL
        margin-right: 0 !important;
        flex-shrink: 0 !important; // Prevent icon from shrinking
        order: 2 !important; // Icon comes after text
      }

      // Text comes first in RTL
      span, .info-text {
        order: 1 !important;
      }

      &:last-child {
        margin-right: 0 !important; // Last item without margin
      }

      // Responsive text sizing
      @media screen and (max-width: 991px) {
        font-size: 0.9rem !important;
        margin-right: 1rem !important;

        i {
          margin-right: 0.4rem !important;
          font-size: 0.85rem !important;
        }
      }

      @media screen and (max-width: 767px) {
        font-size: 0.85rem !important;
        margin-right: 0.75rem !important;

        i {
          margin-right: 0.35rem !important;
          font-size: 0.8rem !important;
        }
      }

      @media screen and (max-width: 575px) {
        font-size: 0.8rem !important;
        margin-right: 0.5rem !important;

        i {
          margin-right: 0.3rem !important;
          font-size: 0.75rem !important;
        }
      }
    }
  }

  // Status badge and actions
  .rtl-actions {
    direction: rtl !important;
    justify-content: flex-start !important; // Actions on far left
    align-items: center !important;

    .rtl-badge {
      font-family: 'Markazi Text', sans-serif !important;
      direction: rtl !important;
      text-align: center !important;
      margin-right: 0 !important;
      margin-left: 1rem !important; // Space from left

    }

    .rtl-button {
      font-family: 'Markazi Text', sans-serif !important;
      direction: rtl !important;
      text-align: center !important;
      margin-right: 0 !important;
      margin-left: 0.75rem !important; // Space between buttons

      i {
        margin-right: 0 !important;
        margin-left: 0.5rem !important; // Icon on left side of text
      }
    }
  }

  // Navigation tabs
  .rtl-tabs {
    direction: rtl !important;
    overflow-x: hidden !important; // Hide horizontal scroll
    justify-content: center !important; // Center the tabs

    .rtl-nav {
      flex-wrap: wrap !important; // Allow wrapping on small screens
      justify-content: center !important; // Center the nav items
      gap: 0.5rem !important; // Add gap between items

      .nav-item {
        flex-shrink: 0 !important; // Prevent shrinking

        .rtl-tab-link {
          font-family: 'Markazi Text', sans-serif !important;
          text-align: center !important;
          font-size: 1rem !important;
          font-weight: 600 !important;
          padding: 0.75rem 1.25rem !important;
          margin: 0 0.25rem !important; // Small margin between tabs
          border-radius: 0.5rem !important;
          white-space: nowrap !important; // Prevent text wrapping
          min-width: auto !important;

          &.active {
            font-family: 'Noto Kufi Arabic', sans-serif !important;
            font-weight: 700 !important;
          }
        }
      }
    }
  }

  // Statistics section - Compact design
  .rtl-stats {
    direction: rtl !important;
    text-align: right !important; // Right text alignment
    margin-left: 0 !important;
    margin-right: 1.5rem !important; // Space from right

    // Smaller overall size
    padding: 0.5rem 0.75rem !important; // Reduced padding
    min-width: 80px !important; // Smaller minimum width
    border-radius: 0.5rem !important; // Rounded corners
    background-color: #f8f9fa !important; // Light background
    border: 1px solid #e9ecef !important; // Light border

    .d-flex.align-items-center {
      justify-content: flex-end !important; // Far right alignment
      margin-bottom: 0.25rem !important; // Small margin

      .fs-2.fw-bolder {
        font-size: 1.25rem !important; // Smaller number
        font-weight: 600 !important;
        color: #495057 !important; // Darker text

        i {
          margin-left: 0 !important;
          margin-right: 0.4rem !important; // Icon on right side of number
          font-size: 1rem !important; // Smaller icon
          color: #6c757d !important; // Gray icon
        }
      }
    }

    .rtl-text {
      font-family: 'Markazi Text', sans-serif !important;
      text-align: right !important; // Right text alignment
      font-size: 0.75rem !important; // Smaller text
      font-weight: 500 !important;
      color: #6c757d !important; // Gray text
      margin: 0 !important;
    }
  }

  // Card layout
  .rtl-layout {
    direction: rtl !important;

    .card-body {
      text-align: right !important;
    }

    // Main container adjustments
    .d-flex.flex-wrap.flex-sm-nowrap {
      .flex-grow-1 {
        .d-flex.justify-content-between {
          flex-direction: row-reverse !important; // عكس الترتيب
          justify-content: space-between !important;
        }

        .d-flex.flex-wrap.flex-stack {
          justify-content: flex-end !important; // Statistics on far right

          .d-flex.flex-column.flex-grow-1 {
            align-items: flex-end !important; // Far right alignment

            .d-flex.flex-wrap {
              justify-content: flex-end !important; // Far right alignment
            }
          }
        }
      }
    }
  }
}

// ===== RESPONSIVE DESIGN =====

// Large screens (1200px+)
@media screen and (min-width: 1200px) {
  .rtl-title {
    font-size: 2rem !important;
  }
}

// Medium-large screens (992px - 1199px)
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .rtl-title {
    font-size: 1.75rem !important;
  }
}

// Medium screens (768px - 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .rtl-title {
    font-size: 1.5rem !important;
    line-height: 1.4 !important;
  }

  // Responsive tabs for medium screens
  .rtl-tabs {
    .rtl-nav {
      justify-content: center !important;
      gap: 0.4rem !important;

      .nav-item {
        .rtl-tab-link {
          font-size: 0.9rem !important;
          padding: 0.6rem 1rem !important;
          margin: 0.2rem !important;
        }
      }
    }
  }

  :host-context(html[lang="ar"]) {
    .rtl-header {
      flex-direction: column !important; // Vertical layout

      .d-flex.flex-column {
        align-items: flex-end !important; // Far right alignment for medium screens
        text-align: right !important;
        order: 1 !important;
        width: 100% !important;
      }

      .rtl-info {
        justify-content: flex-end !important;
        flex-wrap: nowrap !important; // Keep all items on one line
        overflow-x: auto !important; // Allow horizontal scroll if needed

        .rtl-info-item {
          margin-right: 0.6rem !important;
          white-space: nowrap !important;
          font-size: 0.9rem !important;

          i {
            margin-right: 0.35rem !important;
            font-size: 0.85rem !important;
          }
        }
      }

      .rtl-actions {
        order: 2 !important;
        justify-content: flex-start !important; // Actions on far left
        width: 100% !important;
        margin-top: 1rem !important;
      }
    }

    .rtl-stats {
      margin-right: 0 !important; // Remove margin for medium screens
    }
  }
}

// Small screens (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .rtl-title {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
  }

  // Responsive tabs for small screens
  .rtl-tabs {
    .rtl-nav {
      justify-content: center !important;
      gap: 0.3rem !important;

      .nav-item {
        .rtl-tab-link {
          font-size: 0.85rem !important;
          padding: 0.55rem 0.85rem !important;
          margin: 0.15rem !important;
          border-radius: 0.4rem !important;
        }
      }
    }
  }

  // Center actions and stats on small screens
  :host-context(html[lang="ar"]) {
    .rtl-header {
      flex-direction: column !important;
      align-items: center !important;

      .d-flex.my-4.rtl-actions {
        justify-content: center !important; // Center actions
        order: 2 !important;
        margin-top: 1.5rem !important;
        width: 100% !important;

        .rtl-badge, .rtl-button {
          font-size: 0.8rem !important;
          padding: 0.5rem 0.75rem !important;
          margin: 0 0.3rem !important;
        }
      }
    }

    // Center and resize stats
    .d-flex.flex-wrap.flex-stack {
      justify-content: center !important;
      margin-top: 1rem !important;

      .d-flex.flex-column.flex-grow-1 {
        justify-content: center !important;
        align-items: center !important;

        .rtl-stats {
          margin: 0 !important;
          padding: 0.4rem 0.6rem !important;
          min-width: 70px !important;

          .fs-2.fw-bolder {
            font-size: 1rem !important;

            i {
              font-size: 0.85rem !important;
            }
          }

          .rtl-text {
            font-size: 0.7rem !important;
          }
        }
      }
    }
  }
}

  :host-context(html[lang="ar"]) {
    .rtl-header {
      flex-direction: column !important;
      align-items: center !important; // Center everything

      .d-flex.flex-column {
        align-items: center !important; // Center title and info
        text-align: center !important; // Center text
        order: 1 !important;
        width: 100% !important;
        margin-bottom: 1rem !important;

        .rtl-title {
          text-align: center !important; // Center title
        }
      }

      .rtl-info {
        justify-content: center !important; // Center info items
        flex-wrap: nowrap !important; // Keep all items on one line
        overflow-x: auto !important; // Allow horizontal scroll if needed

        .rtl-info-item {
          margin-right: 0.4rem !important; // Compact spacing
          margin-left: 0.4rem !important; // Equal spacing both sides
          white-space: nowrap !important; // Prevent text wrapping
          font-size: 0.85rem !important; // Smaller font for compact layout

          i {
            margin-right: 0.3rem !important;
            font-size: 0.8rem !important; // Smaller icon
          }

          &:first-child {
            margin-left: 0 !important;
          }

          &:last-child {
            margin-right: 0 !important;
          }
        }
      }

      .rtl-actions {
        order: 2 !important;
        justify-content: center !important; // Center actions
        flex-wrap: wrap !important;
        width: 100% !important;

        .rtl-badge, .rtl-button {
          margin: 0.25rem !important; // Equal margin all around
        }
      }
    }

    // Actions and Stats side by side on small screens
    .d-flex.flex-wrap.flex-stack {
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: center !important;
      margin-top: 1rem !important;

      .d-flex.flex-column.flex-grow-1 {
        flex-direction: row !important;
        justify-content: flex-end !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }

    .rtl-stats {
      margin-right: 0 !important;
      margin-left: 1rem !important;
    }
  }


// Extra small screens (≤575px)
@media screen and (max-width: 575px) {
  .rtl-title {
    font-size: 1.1rem !important;
    line-height: 1.5 !important;
  }

  // Center everything on extra small screens
  :host-context(html[lang="ar"]) {
    .rtl-header {
      flex-direction: column !important;
      align-items: center !important;

      .d-flex.my-4.rtl-actions {
        justify-content: center !important; // Center actions
        order: 2 !important;
        margin-top: 1.5rem !important;
        width: 100% !important;
        flex-wrap: wrap !important;

        .rtl-badge, .rtl-button {
          font-size: 0.75rem !important;
          padding: 0.4rem 0.6rem !important;
          margin: 0.2rem !important;
        }
      }
    }

    // Center and make stats very compact
    .d-flex.flex-wrap.flex-stack {
      justify-content: center !important;
      margin-top: 1rem !important;

      .d-flex.flex-column.flex-grow-1 {
        justify-content: center !important;
        align-items: center !important;

        .rtl-stats {
          margin: 0 !important;
          padding: 0.3rem 0.5rem !important;
          min-width: 60px !important;
          border-radius: 0.4rem !important;

          .fs-2.fw-bolder {
            font-size: 0.85rem !important;
            margin-bottom: 0.1rem !important;

            i {
              font-size: 0.7rem !important;
              margin-right: 0.3rem !important;
            }
          }

          .rtl-text {
            font-size: 0.65rem !important;
            line-height: 1.2 !important;
          }
        }
      }
    }
  }
}

  :host-context(html[lang="ar"]) {
    .rtl-header {
      flex-direction: column !important;
      align-items: center !important; // Center everything

      .d-flex.flex-column {
        align-items: center !important; // Center title and info
        text-align: center !important; // Center text
        order: 1 !important;
        width: 100% !important;
        margin-bottom: 1rem !important;

        .rtl-title {
          text-align: center !important; // Center title
        }
      }

      .rtl-info {
        justify-content: center !important; // Center info items
        flex-direction: row !important; // Keep horizontal layout
        flex-wrap: nowrap !important; // Force single line
        align-items: center !important;
        overflow-x: auto !important; // Allow horizontal scroll if needed
        gap: 0 !important;

        .rtl-info-item {
          margin-left: 0.2rem !important;
          margin-right: 0.2rem !important; // Very compact spacing
          white-space: nowrap !important; // Prevent text wrapping
          font-size: 0.7rem !important; // Very small font for extra small screens

          i {
            margin-right: 0.2rem !important;
            margin-left: 0 !important;
            font-size: 0.65rem !important; // Very small icon
          }

          &:first-child {
            margin-left: 0 !important;
          }

          &:last-child {
            margin-right: 0 !important;
          }
        }
      }

      .rtl-actions {
        order: 2 !important;
        justify-content: center !important; // Center actions
        flex-direction: row !important; // Keep horizontal for actions
        flex-wrap: wrap !important;
        align-items: center !important;
        width: 100% !important;

        .rtl-badge, .rtl-button {
          margin: 0.2rem !important; // Small equal margins
          font-size: 0.75rem !important; // Smaller text
          padding: 0.4rem 0.6rem !important; // Smaller padding
        }
      }
    }

    // Actions and Stats side by side on extra small screens
    .d-flex.flex-wrap.flex-stack {
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: center !important;
      margin-top: 1rem !important;

      .d-flex.flex-column.flex-grow-1 {
        flex-direction: row !important;
        justify-content: flex-end !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }

    .rtl-stats {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;

      .rtl-text {
        font-size: 0.7rem !important; // Smaller stats text
      }

      .fs-2 {
        font-size: 1.2rem !important; // Smaller stats number
      }
    }
  }

    .rtl-stats {
      margin-right: 0 !important;
      width: 100% !important;
    }

    // Responsive tabs for extra small screens
    .rtl-tabs {
      .rtl-nav {
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 0.25rem !important;

        .nav-item {
          .rtl-tab-link {
            font-size: 0.85rem !important;
            padding: 0.5rem 0.75rem !important;
            margin: 0.125rem !important;
            border-radius: 0.375rem !important;
          }
        }
      }
    }



// ===== GENERAL ENHANCEMENTS =====

// Hide scrollbars for tabs
.rtl-tabs {
  // Hide horizontal scrollbar
  overflow-x: hidden !important;
  -ms-overflow-style: none !important; // IE and Edge
  scrollbar-width: none !important; // Firefox

  &::-webkit-scrollbar {
    display: none !important; // Chrome, Safari, Opera
  }

  .rtl-nav {
    // Hide scrollbar for nav
    overflow-x: hidden !important;
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;

    &::-webkit-scrollbar {
      display: none !important;
    }
  }
}

// Force single line layout for user info on all screen sizes
.rtl-info {
  min-width: 0 !important; // Allow flex items to shrink

  .rtl-info-item {
    flex-shrink: 1 !important; // Allow items to shrink if needed
    min-width: 0 !important; // Allow text to be truncated if necessary

    // Truncate text if it's too long
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 150px !important; // Limit max width per item

    @media screen and (max-width: 575px) {
      max-width: 120px !important; // Smaller max width on small screens
    }

    @media screen and (max-width: 425px) {
      max-width: 100px !important; // Even smaller on very small screens
    }
  }
}

// Enhanced compact stats design
.rtl-stats {
  transition: all 0.3s ease !important;

  &:hover {
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  }

  // Responsive sizing
  @media screen and (max-width: 991px) {
    padding: 0.4rem 0.6rem !important;
    min-width: 70px !important;

    .fs-2.fw-bolder {
      font-size: 1.1rem !important;

      i {
        font-size: 0.9rem !important;
      }
    }

    .rtl-text {
      font-size: 0.7rem !important;
    }
  }

  @media screen and (max-width: 767px) {
    padding: 0.35rem 0.5rem !important;
    min-width: 65px !important;

    .fs-2.fw-bolder {
      font-size: 1rem !important;

      i {
        font-size: 0.8rem !important;
      }
    }

    .rtl-text {
      font-size: 0.65rem !important;
    }
  }

  @media screen and (max-width: 575px) {
    padding: 0.3rem 0.45rem !important;
    min-width: 55px !important;

    .fs-2.fw-bolder {
      font-size: 0.9rem !important;

      i {
        font-size: 0.7rem !important;
      }
    }

    .rtl-text {
      font-size: 0.6rem !important;
    }
  }
}

// Card styling
.card {
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
}

// Navigation tabs
.nav-tabs, .rtl-tabs {
  border-bottom: 2px solid #e4e6ef;

  .nav-link, .rtl-tab-link {
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    color: #7e8299;
    font-weight: 600;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;

    &:hover {
      border-bottom-color: #009ef7;
      color: #009ef7;
      background: rgba(0, 158, 247, 0.1);
      text-decoration: none;
    }

    &.active {
      color: #009ef7;
      border-bottom-color: #009ef7;
      background: rgba(0, 158, 247, 0.1);
    }
  }
}

// Responsive navigation tabs
@media screen and (max-width: 991px) {
  .nav-tabs, .rtl-tabs {
    .nav-link, .rtl-tab-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
}

@media screen and (max-width: 767px) {
  .nav-tabs, .rtl-tabs {
    .nav-link, .rtl-tab-link {
      padding: 0.6rem 0.8rem;
      font-size: 0.85rem;
    }
  }
}

@media screen and (max-width: 575px) {
  .nav-tabs, .rtl-tabs {
    .nav-link, .rtl-tab-link {
      padding: 0.5rem 0.6rem;
      font-size: 0.8rem;
    }
  }
}

// Status badge enhancements
.badge {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

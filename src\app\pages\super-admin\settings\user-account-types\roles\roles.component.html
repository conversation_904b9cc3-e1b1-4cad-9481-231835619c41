<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header custom-roles-header" [class.arabic-header]="translationService.isRTL()">
          <div class="header-content-wrapper">
            <div *ngIf="!translationService.isRTL()" class="header-icon-section">
              <div class="custom-icon-container">
                <i class="fas fa-user-tag"></i>
              </div>
            </div>
            <div class="header-text-section">
              <h3 class="header-title">{{ 'ROLES.TITLE' | translate }}</h3>
              <span class="header-subtitle">{{ 'ROLES.SUBTITLE' | translate }}</span>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()" (click)="goBack()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'ROLES.BACK' | translate }}
            </button>
            <button class="btn btn-warning btn-sm" (click)="addRole()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-plus" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'ROLES.ADD_ROLE' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Roles Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header custom-table-header" [class.arabic-table-header]="translationService.isRTL()">
          <div class="table-header-content">
            <h3 class="table-header-title">{{ 'ROLES.ALL_ROLES' | translate }}</h3>
          </div>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()">
                <i *ngIf="!translationService.isRTL()"
                  class="fas fa-search position-absolute top-50 translate-middle-y text-muted"
                  [class.start-0]="!translationService.isRTL()" [class.end-0]="translationService.isRTL()"
                  [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"></i>
                <input type="text" class="form-control form-control-sm" [class.ps-10]="!translationService.isRTL()"
                  [class.pe-10]="translationService.isRTL()" [placeholder]="'ROLES.SEARCH_PLACEHOLDER' | translate"
                  [(ngModel)]="searchText" (ngModelChange)="onSearchChange($event)"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-rounded table-striped border gy-7 gs-7">
              <thead class="bg-light">
                <tr class="fw-bold fs-6 text-gray-800 border-bottom-2 border-gray-200"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                  <th class="min-w-300px ps-4" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'ROLES.ROLE' | translate }}
                  </th>
                  <th class="min-w-150px" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'ROLES.CREATED_DATE' | translate }}
                  </th>
                  <th class="min-w-100px" [style.text-align]="translationService.isRTL() ? 'left' : 'right'">
                    {{ 'ROLES.ACTIONS' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="roles.length === 0">
                  <td colspan="3" class="text-center py-10">
                    <div class="custom-empty-state" [class.arabic-empty-state]="translationService.isRTL()">
                      <div *ngIf="!translationService.isRTL()" class="empty-state-icon">
                        <div class="symbol symbol-100px mb-5">
                          <div class="symbol-label bg-light-warning">
                            <i class="fas fa-user-tag text-warning fs-2x"></i>
                          </div>
                        </div>
                      </div>
                      <div class="empty-state-content">
                        <div class="empty-state-title">
                          {{ searchText ? ('ROLES.NO_ROLES_MATCH' | translate) : ('ROLES.NO_ROLES_FOUND' | translate) }}
                        </div>
                        <div class="empty-state-subtitle">
                          {{ 'ROLES.NO_ROLES_DESCRIPTION' | translate }}
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr *ngFor="let role of roles" class="border-bottom border-gray-300">
                  <td class="ps-4" [class.pe-4]="translationService.isRTL()" [class.ps-4]="!translationService.isRTL()">
                    <div class="d-flex align-items-center role-row-layout"
                      [class.arabic-layout]="translationService.isRTL()">
                      <div *ngIf="!translationService.isRTL()" class="symbol symbol-50px role-icon">
                        <div class="symbol-label bg-light-warning">
                          <i class="fas fa-user-tag text-warning fs-2"></i>
                        </div>
                      </div>
                      <div class="d-flex flex-column role-details"
                        [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                        <div class="fw-bold text-gray-800 fs-5 mb-1">{{ role.name }}</div>
                        <div class="text-muted fs-7">{{ 'ROLES.ROLE_DESCRIPTION' | translate }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="text-muted fs-6 fw-semibold role-date-column"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    <div class="d-flex flex-column" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                      <span>{{ role.createdAt | date:'short' }}</span>
                      <span class="text-muted fs-7">{{ role.createdAt | date:'mediumDate' }}</span>
                    </div>
                  </td>
                  <td [style.text-align]="translationService.isRTL() ? 'left' : 'right'">
                    <div class="dropdown position-relative">
                      <button class="btn btn-sm btn-light btn-active-light-primary" type="button"
                        (click)="toggleDropdown(role.id)">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu" [class.show]="isDropdownOpen(role.id)"
                        [style.display]="isDropdownOpen(role.id) ? 'block' : 'none'"
                        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                        <li><button class="dropdown-item" type="button"
                            (click)="viewPermissions(role.id); toggleDropdown(role.id)">
                            <i class="fas fa-shield-alt" [class.me-2]="!translationService.isRTL()"
                              [class.ms-2]="translationService.isRTL()"></i>
                            {{ 'ROLES.PERMISSIONS' | translate }}
                          </button></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="!loading && roles.length > 0" class="d-flex justify-content-center mt-5 mb-5">
          <app-pagination [totalItems]="totalElements" [itemsPerPage]="pageSize" [currentPage]="currentPage"
            (pageChange)="onPageChange($event)"></app-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Permissions Modal - Simple Angular Way -->
<div class="permissions-modal-overlay" *ngIf="showPermissionsModal" (click)="closePermissionsModal()">
  <div class="permissions-modal-content" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="modal-header" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
      <h5 class="modal-title"
        [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
        [style.font-size]="translationService.isRTL() ? '1.3rem' : 'inherit'">
        <i class="fas fa-shield-alt text-primary" [class.me-2]="!translationService.isRTL()"
          [class.ms-2]="translationService.isRTL()"></i>
        {{ 'ROLES.ROLE_PERMISSIONS' | translate }}: {{ selectedRole?.name }}
      </h5>
      <button type="button" class="btn-close" (click)="closePermissionsModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
      <div class="row" *ngIf="allPermissions && allPermissions.length > 0">
        <div class="col-12">
          <div class="form-check mb-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
            <input class="form-check-input" type="checkbox" id="selectAll" [checked]="areAllPermissionsSelected()"
              (change)="toggleAllPermissions($event)">
            <label class="form-check-label fw-bold" for="selectAll"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.font-size]="translationService.isRTL() ? '1.1rem' : 'inherit'">
              {{ 'ROLES.SELECT_ALL_PERMISSIONS' | translate }}
            </label>
          </div>
          <hr>
        </div>
        <div class="col-md-6 mb-3" *ngFor="let permission of allPermissions; let i = index">
          <div class="card permission-card">
            <div class="card-body p-3">
              <div class="form-check" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <input class="form-check-input" type="checkbox" [id]="'permission_' + i"
                  [(ngModel)]="permission.isSelected" (change)="onPermissionChange()">
                <label class="form-check-label" [for]="'permission_' + i"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  <div class="fw-bold text-gray-800">{{ permission.name }}</div>
                  <div class="text-muted fs-7 fw-semibold">{{ permission.description }}</div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center py-5" *ngIf="!allPermissions || allPermissions.length === 0">
        <i class="fas fa-shield-alt fs-2x text-muted mb-3"></i>
        <div class="text-muted"
          [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
          {{ 'ROLES.NO_PERMISSIONS_FOUND' | translate }}
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="closePermissionsModal()"
        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
        <i class="fas fa-times" [class.me-2]="!translationService.isRTL()"
          [class.ms-2]="translationService.isRTL()"></i>
        {{ 'ROLES.CANCEL' | translate }}
      </button>
      <button type="button" class="btn btn-primary" (click)="savePermissions()"
        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
        <i class="fas fa-save" [class.me-2]="!translationService.isRTL()" [class.ms-2]="translationService.isRTL()"></i>
        {{ 'ROLES.SAVE_CHANGES' | translate }}
      </button>
    </div>
  </div>
</div>
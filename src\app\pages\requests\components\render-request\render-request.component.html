<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body p-4">

    <!-- Simple Header -->
    <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start mb-4"
      [class.flex-row-reverse]="translationService.getCurrentLanguage() === 'ar'">

      <!-- Title -->
      <div class="mb-3 mb-lg-0">
        <h2 class="text-gray-900 fw-bold fs-2 mb-3" [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
          {{ getTranslatedTitle(request?.title) }}
        </h2>
      </div>

      <!-- Status Badge and Action Buttons -->
      <div class="d-flex align-items-center gap-2"
        [class.flex-row-reverse]="translationService.getCurrentLanguage() === 'ar'">

        <!-- Status Badge -->
        <span class="badge badge-light-success fs-6 px-3 py-2">
          {{ getTranslatedStatus(request?.status) }}
        </span>

        <!-- Action Buttons -->
        <div class="d-flex gap-2" *ngIf="user?.role == 'broker'">
          <button *ngIf="request?.status == 'new'" class="btn btn-primary btn-sm fw-bold"
            (click)="updateRequestStatus(request.id, userId, 'in_processing')">
            <i class="fa-solid fa-play me-1" [class.me-0]="translationService.getCurrentLanguage() === 'ar'"
              [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            {{ getTranslatedText('START_PROCESSING') }}
          </button>

          <button class="btn btn-danger btn-sm fw-bold" (click)="archiveRequest(request.id, brokerId)">
            <i class="fa-regular fa-eye-slash me-1" [class.me-0]="translationService.getCurrentLanguage() === 'ar'"
              [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            {{ getTranslatedText('ARCHIVE') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Info Cards Row -->
    <div class="row g-3 mb-4">
      <!-- User Info -->
      <div class="col-12 col-md-3">
        <div class="bg-light rounded p-3 text-center">
          <i class="fa-solid fa-user text-primary fs-3 mb-2"></i>
          <div class="text-gray-800 fw-bold fs-6 mb-1">
            {{ request?.user?.name || getTranslatedText('N_A') }}
          </div>
          <div class="text-gray-600 fs-7">{{ getTranslatedText('CLIENT_NAME') }}</div>
        </div>
      </div>

      <!-- Date Info -->
      <div class="col-12 col-md-3">
        <div class="bg-light rounded p-3 text-center">
          <i class="fa-solid fa-calendar text-success fs-3 mb-2"></i>
          <div class="text-gray-800 fw-bold fs-6 mb-1">
            {{ request?.createdAt || getTranslatedText('N_A') }}
          </div>
          <div class="text-gray-600 fs-7">{{ getTranslatedText('REQUEST_DATE') }}</div>
        </div>
      </div>

      <!-- Phone Info -->
      <div class="col-12 col-md-3">
        <div class="bg-light rounded p-3 text-center">
          <i class="fa-solid fa-phone text-info fs-3 mb-2"></i>
          <div class="text-gray-800 fw-bold fs-6 mb-1">
            {{ request?.user?.phone || getTranslatedText('N_A') }}
          </div>
          <div class="text-gray-600 fs-7">{{ getTranslatedText('PHONE_NUMBER') }}</div>
        </div>
      </div>

      <!-- Replies Info -->
      <div class="col-12 col-md-3">
        <div class="bg-light rounded p-3 text-center">
          <i class="fa-solid fa-reply text-warning fs-3 mb-2"></i>
          <div class="text-gray-800 fw-bold fs-6 mb-1">
            {{ request?.numberOfReplies || 0 }}
          </div>
          <div class="text-gray-600 fs-7">{{ getTranslatedText('REPLIES') }}</div>
        </div>
      </div>
    </div>

    <div class="d-flex overflow-auto mb-5" [class.rtl-tabs]="translationService.getCurrentLanguage() === 'ar'">
      <ul class="nav nav-stretch nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap"
        [class.rtl-nav]="translationService.getCurrentLanguage() === 'ar'">
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="overview"
            routerLinkActive="active">
            {{ getTranslatedText('OVERVIEW') }}
          </a>
        </li>
        <li class="nav-item" *ngIf="canReply">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="units-recommendation"
            routerLinkActive="active">
            {{ getTranslatedText('UNITS_RECOMMENDATION') }}
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" [routerLink]="['history']"
            [queryParams]="{ requestUserId: request?.user?.id }" routerLinkActive="active">
            {{ getTranslatedText('HISTORY') }}
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="replies"
            routerLinkActive="active">
            {{ getTranslatedText('REPLIES') }}
          </a>
        </li>
      </ul>
    </div>

    <div class="card-body pt-3 pb-0 px-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
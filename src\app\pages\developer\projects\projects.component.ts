import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';
import { ProjectsService } from '../services/projects.service';
import Swal from 'sweetalert2';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss',
})
export class ProjectsComponent extends BaseGridComponent implements OnInit {
  user: any;

  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private projectsService: ProjectsService,
    private route: ActivatedRoute,
    public translationService: TranslationService
  ) {
    super(cd);
    this.setService(projectsService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.route.queryParams.subscribe(params => {
      const queryDeveloperId = params['developerId'];
      const developerId = queryDeveloperId ? parseInt(queryDeveloperId) : this.user.developerId;

      this.page.filters = { developerId: developerId };
      this.reloadTable(this.page);
    });
  }

  updateCardVisibility() {
    this.showEmptyCard = this.rows.length === 0;
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout
    this.searchTimeout = setTimeout(() => {
      this.page.filters = { ...this.page.filters, searchName: value.trim() };
      console.log(this.page.filters);
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = { ...this.page.filters, ...filters };

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (response: any) => {
        console.log(response.data);
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.updateCardVisibility();

        this.afterGridLoaded();

        setTimeout(() => {
          MenuComponent.reinitialization();
        }, 150);
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire(this.getTranslatedText('FAILED_TO_LOAD_DATA'), '', 'error');
      }
    );
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'PROJECTS': 'المشاريع',
        'SEARCH_BY_PROJECT_NAME': 'البحث باسم المشروع..',
        'FILTER': 'تصفية',
        'CREATE_PROJECT': 'إنشاء مشروع',
        'NO_PROJECTS_FOUND': 'لم يتم العثور على مشاريع',
        'NO_PROJECTS_FOR_BROKER': 'لم يتم العثور على مشاريع لهذا الوسيط',
        'PROJECT': 'مشروع',
        'AREA': 'المنطقة',
        'TYPE': 'النوع',
        'PROJECT_TYPE': 'نوع المشروع',
        'PROJECT_EXECUTOR': 'منفذ المشروع',
        'UNITS_SUMMARY': 'ملخص الوحدات',
        'BUILDINGS': 'المباني',
        'APARTMENTS': 'الشقق',
        'DUPLEXES': 'الدوبلكس',
        'PENTHOUSES': 'البنت هاوس',
        'I_VILLA': 'آي فيلا',
        'STUDIOS': 'الاستوديوهات',
        'ROOFS': 'الأسطح',
        'BASEMENTS': 'البيزمنت',
        'TWIN_HOUSES': 'التوين هاوس',
        'TOWN_HOUSES': 'التاون هاوس',
        'STANDALONE_VILLAS': 'الستاند الون',
        'ADMINISTRATIVE': 'إداري',
        'COMMERCIAL': 'تجاري',
        'MEDICAL_CLINICS': 'العيادات الطبية',
        'PHARMACIES': 'الصيدليات',
        'ACTION': 'الإجراءات',
        'FAILED_TO_LOAD_DATA': 'فشل في تحميل البيانات. يرجى المحاولة مرة أخرى لاحق.',
        'INSIDE_COMPOUND': 'داخل الكومباند',
        'OUTSIDE_COMPOUND': 'خارج الكومباند',
        'RESIDENTIAL': 'سكنى',
        'MIXED': 'متنوع',
      },
      'en': {
        'PROJECTS': 'Projects',
        'SEARCH_BY_PROJECT_NAME': 'Search By Project Name..',
        'FILTER': 'Filter',
        'CREATE_PROJECT': 'Create Project',
        'NO_PROJECTS_FOUND': 'No Projects Found',
        'NO_PROJECTS_FOR_BROKER': 'No projects found for this broker',
        'PROJECT': 'project',
        'AREA': 'Area',
        'TYPE': 'Type',
        'PROJECT_TYPE': 'Project Type',
        'PROJECT_EXECUTOR': 'Project Executor',
        'UNITS_SUMMARY': 'Units Summary',
        'BUILDINGS': 'Buildings',
        'APARTMENTS': 'Apartments',
        'DUPLEXES': 'Duplexes',
        'PENTHOUSES': 'Penthouses',
        'I_VILLA': 'I-Villa',
        'STUDIOS': 'Studios',
        'ROOFS': 'Roofs',
        'BASEMENTS': 'Basements',
        'TWIN_HOUSES': 'Twin Houses',
        'TOWN_HOUSES': 'Town Houses',
        'STANDALONE_VILLAS': 'Standalone Villas',
        'ADMINISTRATIVE': 'Administrative',
        'COMMERCIAL': 'Commercial',
        'MEDICAL_CLINICS': 'Medical Clinics',
        'PHARMACIES': 'Pharmacies',
        'ACTION': 'Action',
        'FAILED_TO_LOAD_DATA': 'Failed to load data. please try again later.',
        'INSIDE_COMPOUND': 'Inside Compound',
        'OUTSIDE_COMPOUND': 'Outside Compound',
        'RESIDENTIAL': 'Residential',
        'MIXED': 'Mixed',
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  getTranslatedCompoundType(type: string): string {
    if (!type) return 'N/A';

    switch (type.toLowerCase()) {
      case 'inside_compound':
        return this.getTranslatedText('INSIDE_COMPOUND');
      case 'outside_compound':
        return this.getTranslatedText('OUTSIDE_COMPOUND');
      default:
        return type;
    }
  }
  getTranslatedProjectType(project_type: string): string {
    if (!project_type) return 'N/A';

    switch (project_type.toLowerCase()) {
      case 'residential':
        return this.getTranslatedText('RESIDENTIAL');
      case 'commercial':
        return this.getTranslatedText('COMMERCIAL');
      case 'mixed':
        return this.getTranslatedText('MIXED');
      default:
        return project_type;
    }
  }

}

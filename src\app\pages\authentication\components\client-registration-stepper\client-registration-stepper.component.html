<div class="client-registration-stepper">
  <!-- Stepper Header -->
  <div class="stepper-header">
    <h2 class="stepper-title">{{ 'AUTH.CLIENT_REGISTRATION.TITLE' | translate }}</h2>
    <div class="stepper-progress">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
      </div>
      <div class="d-flex">
        <span class="progress-text">{{ 'AUTH.CLIENT_REGISTRATION.STEP_OF' | translate: {current: currentStep, total:
          totalSteps} }}</span>
        <button *ngIf="currentStep > 0 && currentStep < 4" type="button" class="back-to-previous"
          (click)="previousStep()">
          {{ 'AUTH.CLIENT_REGISTRATION.BACK_TO_PREVIOUS' | translate }}
        </button>
      </div>
    </div>
  </div>

  <!-- Stepper Content -->
  <form [formGroup]="registrationForm" class="stepper-form">
    <!-- Step 1: Gender Selection, Full Name, Email/Phone -->
    <div *ngIf="currentStep === 1" class="step-content">
      <h3 class="step-title">{{ 'AUTH.CLIENT_REGISTRATION.CHOOSE_GENDER' | translate }} <span class="required"></span>
      </h3>

      <!-- Gender Selection -->
      <div class="gender-selection">
        <div class="gender-buttons">
          <button type="button" class="gender-btn female fs-5 fw-bold" [class.selected]="selectedGender === 'female'"
            (click)="selectGender('female')">
            <i class="ki-outline ki-lovely"></i>
            {{ 'AUTH.CLIENT_REGISTRATION.FEMALE' | translate }}
          </button>
          <button type="button" class="gender-btn fs-5 fw-bold" [class.selected]="selectedGender === 'male'"
            (click)="selectGender('male')">
            <i class="ki-outline ki-profile-user"></i>
            {{ 'AUTH.CLIENT_REGISTRATION.MALE' | translate }}
          </button>
        </div>
        <div *ngIf="isFieldInvalid('gender')" class="invalid-feedback fs-4 fw-bold">
          {{ 'AUTH.VALIDATION.SELECT_GENDER' | translate }}
        </div>
      </div>

      <!-- Full Name -->
      <div class="form-group">
        <label for="fullName" class="form-label fs-2 fw-bold">
          <i class="ki-outline ki-user"></i>
          {{ 'AUTH.CLIENT_REGISTRATION.FULL_NAME' | translate }} <span class="required"></span>
        </label>
        <input type="text" id="fullName" formControlName="fullName" class="form-control fs-3 fw-bold"
          [class.is-invalid]="isFieldInvalid('fullName')" [placeholder]="'AUTH.INPUT.NAME_PLACEHOLDER' | translate"
          pattern="[^0-9]*" title="Name cannot contain numbers" (blur)="markFieldAsTouched('fullName')" required />
        <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
          {{ getFieldError("fullName") }}
        </div>
      </div>

      <!-- Email or Phone -->
      <div class="form-group">
        <label class="form-label fs-2 fw-bold">
          <i class="ki-outline ki-phone"></i>
          {{ 'AUTH.INPUT.ENTER_PHONE' | translate }} <span class="required"></span>
        </label>
        <input type="text" formControlName="email_phone" class="form-control fs-3 fw-bold"
          [class.is-invalid]="isFieldInvalid('email_phone')"
          [placeholder]="'AUTH.INPUT.PHONE_PLACEHOLDER' | translate"
          title="Enter a valid phone number" autocomplete="email tel"
          (blur)="markFieldAsTouched('email_phone')" required />
        <div *ngIf="isFieldInvalid('email_phone')" class="invalid-feedback">
          {{ getFieldError("email_phone") }}
        </div>
      </div>

      <!-- Send Verification Button -->
      <button type="button" class="btn btn-primary btn-verification  fs-4" [class.loading]="isLoadingSendOtp"
        [disabled]="!isStep1Valid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
        <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingSendOtp">{{ 'AUTH.CLIENT_REGISTRATION.SENDING' | translate }}</span>
        <span *ngIf="!isLoadingSendOtp">{{ 'AUTH.CLIENT_REGISTRATION.SEND_VERIFICATION_CODE' | translate }}</span>
      </button>

      <!-- Help Text -->
      <div class="help-text">
        {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 2: Verification Code -->
    <div *ngIf="currentStep === 2" class="step-content">
      <h3 class="step-title">{{ 'AUTH.CLIENT_REGISTRATION.ENTER_VERIFICATION_CODE' | translate }}</h3>

      <div class="verification-code-section">
        <div formArrayName="verificationCode" class="verification-inputs">
          <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
            <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
              (input)="autoFocusNext($event, i)" />
          </div>
        </div>
      </div>

      <div class="countdown-section">
        <span class="countdown-text" *ngIf="!showResendButton">
          {{ 'AUTH.VERIFICATION.RESEND_IN' | translate }}
          <span class="countdown-timer">
            0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
          </span>

          <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
            {{ 'AUTH.VERIFICATION.RESEND_CODE' | translate }}
          </button>
      </div>

      <!-- OTP Error Message -->
      <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
        {{ otpErrorMessage }}
      </div>

      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
        [disabled]="!isStep2Valid() || isLoadingCheckOtp" (click)="checkOTP()">
        <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCheckOtp">{{ 'AUTH.CLIENT_REGISTRATION.VERIFYING' | translate }}</span>
        <span *ngIf="!isLoadingCheckOtp">{{ 'AUTH.CLIENT_REGISTRATION.VERIFICATION_CODE_NEXT' | translate }}</span>
      </button>

      <div class="help-text">
        {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 3: Username and Password -->
    <div *ngIf="currentStep === 3" class="step-content">
      <h3 class="step-title">{{ 'AUTH.CLIENT_REGISTRATION.EMAIL_PHONE_PASSWORD' | translate }}</h3>

      <!-- Email -->
      <div class="form-group">
        <label for="email" class="form-label fs-2 fw-bold">
          <i class="ki-outline ki-user"></i>
          {{ 'AUTH.INPUT.EMAIL' | translate }}
        </label>
        <input type="email" id="email" formControlName="email" class="form-control fs-3 fw-bold"
          [class.is-invalid]="isFieldInvalid('email')" [placeholder]="'AUTH.INPUT.EMAIL_PLACEHOLDER' | translate"
          autocomplete="email" />
        <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
          {{ getFieldError("email") }}
        </div>
      </div>

      <!-- Phone -->
      <div class="form-group">
        <label for="phone" class="form-label d-flex align-items-center fs-2 fw-bold">
          <i class="ki-outline ki-phone me-2"></i>
          <span>{{ 'AUTH.INPUT.PHONE' | translate }}</span>
          <span class="required ms-1"></span>
        </label>
        <input type="tel" id="phone" formControlName="phone" class="form-control fs-3 fw-bold"
          [class.is-invalid]="isFieldInvalid('phone')" [placeholder]="'AUTH.INPUT.PHONE_PLACEHOLDER' | translate"
          required autocomplete="tel" />
        <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
          {{ getFieldError("phone") }}
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password" class="form-label fs-2 fw-bold">
          <i class="ki-outline ki-lock"></i>
          {{ 'AUTH.INPUT.PASSWORD' | translate }} <span class="required"></span>
        </label>
        <div class="password-input-container">
          <input [type]="showPassword ? 'text' : 'password'" id="password" formControlName="password" class="form-control fs-3 fw-bold"
            [class.is-invalid]="isFieldInvalid('password')" [placeholder]="'AUTH.INPUT.PASSWORD_PLACEHOLDER' | translate"
            minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
            title="Password must be at least 8 characters with uppercase, lowercase and number" required
            autocomplete="new-password" />
          <i [class]="showPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'" class="password-toggle-icon"
            (click)="togglePasswordVisibility()"></i>
        </div>
        <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
          {{ getFieldError("password") }}
        </div>
      </div>

      <!--Confirm Password -->
      <div class="form-group">
        <label for="confirmPassword" class="form-label fs-2 fw-bold">
          <i class="ki-outline ki-lock"></i>
          {{ 'AUTH.INPUT.CONFIRM_PASSWORD' | translate }} <span class="required"></span>
        </label>
        <div class="password-input-container">
          <input [type]="showConfirmPassword ? 'text' : 'password'" id="confirmPassword" formControlName="password_confirmation" class="form-control fs-3 fw-bold"
            [class.is-invalid]="
              isFieldInvalid('password_confirmation') || getFormError()
            " [placeholder]="'AUTH.INPUT.PASSWORD_PLACEHOLDER' | translate" required autocomplete="new-password" />
          <i [class]="showConfirmPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'" class="password-toggle-icon"
            (click)="toggleConfirmPasswordVisibility()"></i>
        </div>
        <div *ngIf="isFieldInvalid('password_confirmation')" class="invalid-feedback">
          {{ getFieldError("password_confirmation") }}
        </div>
        <div *ngIf="getFormError()" class="invalid-feedback">
          {{ getFormError() }}
        </div>
      </div>

      <!-- Terms Agreement -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" id="agreeTerms" formControlName="agreeTerms" class="form-check-input"
            [class.is-invalid]="isFieldInvalid('agreeTerms')" />
          <label for="agreeTerms" class="form-check-label">
            {{ 'AUTH.CLIENT_REGISTRATION.AGREE_TERMS' | translate }} <span class="required"></span>
          </label>
        </div>
        <div *ngIf="isFieldInvalid('agreeTerms')" class="invalid-feedback">
          {{ getFieldError("agreeTerms") }}
        </div>
      </div>

      <!-- Create Account Button -->
      <button type="button" class="btn btn-primary btn-verification fs-3 fw-bold" [class.loading]="isLoadingCreateAccount"
        [disabled]="!isStep3Valid() || isLoadingCreateAccount" (click)="createAccount()">
        <span *ngIf="isLoadingCreateAccount" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCreateAccount">{{ 'AUTH.CLIENT_REGISTRATION.CREATING_ACCOUNT' | translate }}</span>
        <span *ngIf="!isLoadingCreateAccount">{{ 'AUTH.CLIENT_REGISTRATION.CREATE_ACCOUNT' | translate }}</span>
      </button>
    </div>

    <!-- Step 4: Success Page -->
    <div *ngIf="currentStep === 4" class="step-content success-step">
      <div class="success-content">
        <div class="success-icon">
          <i class="ki-outline ki-check-circle"></i>
        </div>

        <h3 class="success-title">{{ 'AUTH.CLIENT_REGISTRATION.REGISTRATION_SUCCESS' | translate }}</h3>

        <p class="success-message">
          {{ 'AUTH.CLIENT_REGISTRATION.SUCCESS_MESSAGE' | translate }}
        </p>

        <div class="success-illustration">
          <!-- Success illustration will go here -->
          <!-- <img src="/assets/media/login/successfully.png" alt="Success" class="success-image" /> -->
          <!-- <img src="~src/assets/media/login/successfully.png" alt="Success" class="success-image" /> -->
          <img src="assets/media/login/successfully.png" alt="Success" class="success-image" />
        </div>

        <button type="button" class="btn btn-primary btn-success-action" [routerLink]="['/requests']">
          {{ 'AUTH.CLIENT_REGISTRATION.GO_TO_WEBSITE' | translate }}
        </button>

        <div class="additional-info">
          <span class="info-link">{{ 'AUTH.CLIENT_REGISTRATION.LEARN_MORE' | translate }}</span>
        </div>
      </div>
    </div>
  </form>
</div>

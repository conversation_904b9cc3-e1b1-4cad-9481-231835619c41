<div class="filter-dropdown">
  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('AREA') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.area">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let area of areas" [value]="area.id">
        {{ translationService.getCurrentLanguage() === 'ar' ? (area.name_ar || area.name_en) : area.name_en }}
      </option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_AREA') }}:</label>
    <input type="text" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_UNIT_AREA')"
      [(ngModel)]="filter.unitArea" />
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('VIEW') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.view">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let view of views" [value]="view.value">{{ view.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_TYPE') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.unitType">
      <option value="">{{ getTranslatedText('SELECT_UNIT_TYPE') }}</option>
      <option *ngFor="let type of unitTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('PRICE') }}:</label>
    <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_PRICE')"
      [(ngModel)]="filter.price" />
  </div>

  <div class="d-flex gap-2">
    <button class="btn btn-sm btn-primary flex-fill" (click)="apply()">{{ getTranslatedText('APPLY') }}</button>
    <button class="btn btn-sm btn-secondary flex-fill" (click)="reset()">{{ getTranslatedText('RESET') }}</button>
  </div>
</div>

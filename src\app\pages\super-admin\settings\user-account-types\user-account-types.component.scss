.header-icon {
  .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(49, 40, 167, 0.3);

    i {
      font-size: 1.5rem;
      color: white;
    }
  }
}

.header-content {
  .header-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
  }
}

// Custom style for Arabic header with specific layout
.rtl-header-row {
  flex-direction: row !important;
  gap: 10px !important;

  .symbol {
    margin: 0 !important;
  }

  div {
    margin: 0 !important;
  }
}

// Specific RTL fixes for this component only
[dir="rtl"] {
  .card-body {
    .d-flex.justify-content-between {
      // Only apply to the main container, not all justify-content-between elements
      &:not(.rtl-header-row) {
        flex-direction: row-reverse !important;
      }
    }
  }
}

// RTL and Arabic Language Support
[dir="rtl"] {
  .card {
    .card-body {
      .d-flex.align-items-center {
        text-align: right;

        &.arabic-header-layout {
          flex-direction: row !important;
          gap: 10px !important;
        }

        .symbol {
          margin-left: 0;
          margin-right: 0;
        }

        div {
          h3 {
            font-family: 'Noto Kufi Arabic', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            line-height: 1.4;
            margin-bottom: 0.5rem;
            text-align: right;
          }

          p {
            font-family: 'Hacen Liner Screen', sans-serif;
            font-size: 1.1rem;
            line-height: 1.5;
            text-align: right;
          }
        }
      }
    }
  }

  // Card layout improvements
  .row.justify-content-center {
    .col-lg-4 {
      margin-bottom: 1.5rem;
    }
  }

  // Header improvements
  .header-content {
    text-align: right;

    .header-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      font-size: 1.75rem;
      line-height: 1.4;
    }

    .header-subtitle {
      font-family: 'Hacen Liner Screen', sans-serif;
      font-size: 1rem;
      line-height: 1.5;
    }
  }
}

.settings-container {
  padding: 20px;
}

.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e1e3ea;
  margin-bottom: 25px;
  transition: all 0.3s ease;

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    min-height: 80px;

    .card-title-wrapper {
      flex-grow: 1;

      .card-title {
        margin: 0;
        color: #2d3748;
        font-weight: 700;
        font-size: 1.5rem;
        line-height: 1.4;
      }
    }

    .card-toolbar {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;

        i {
          font-size: 0.85rem;
        }
      }
    }
  }

  .card-body {
    padding: 20px;
  }
}

// RTL Support
[dir="rtl"] {
  .card-header {
    flex-direction: row-reverse;
    text-align: right;

    .card-title-wrapper {
      text-align: right;
      justify-content: flex-start !important;

      .card-title {
        text-align: right;
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.75rem;
        line-height: 1.3;
        justify-content: flex-start !important;
        display: flex;
        align-items: center;
      }
    }

    .card-toolbar {
      flex-direction: row-reverse;

      .btn {
        font-family: 'Hacen Liner Screen', sans-serif;

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }
}

.rtl-layout {
  direction: rtl;
  text-align: right;

  .row {
    direction: rtl;
  }

  .col-lg-4,
  .col-md-6 {
    text-align: right;
  }

  .btn {
    .fas {
      margin-right: 0;
      margin-left: 0.5rem;
    }
  }
}

// Arabic Fonts Support
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.arabic-font {
  font-family: 'Noto Kufi Arabic', sans-serif;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif;
  line-height: 1.8;
}

// Enhanced card styling for RTL
.rtl-layout .card {
  .card-header {
    text-align: right;

    .card-title {
      text-align: right;
    }
  }
}

// Responsive adjustments for RTL
@media (max-width: 768px) {
  .rtl-layout {
    .card-header {
      .d-flex {
        flex-direction: column;
        align-items: flex-end;
      }

      .card-toolbar {
        margin-top: 1rem;
        justify-content: center;
      }
    }
  }
}



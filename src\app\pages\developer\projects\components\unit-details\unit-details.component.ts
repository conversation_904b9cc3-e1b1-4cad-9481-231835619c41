import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UnitsService } from '../../../services/units.service';
import { TranslationService } from 'src/app/modules/i18n';
import { PROPERTY_TYPES, COMPOUND_TYPES } from 'src/app/shared/interfaces/property-types.interface';

@Component({
  selector: 'app-unit-details',
  templateUrl: './unit-details.component.html',
  styleUrl: './unit-details.component.scss',
})
export class UnitDetailsComponent implements OnInit {
  unitId: number | null = null;
  unitDetails: any;
  isLoading: boolean = false;

  // Carousel properties
  AllImages: any[] = [];
  isAutoRotating: boolean = true;

  // Features mapping
  features = [
    { name: 'Garage', value: 'garage' },
    { name: 'Clubhouse', value: 'clubhouse' },
    { name: 'Club', value: 'club' },
    { name: 'Storage', value: 'storage' },
    { name: 'Elevator', value: 'elevator' },
    { name: 'Swimming Pool', value: 'swimming_pool' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private unitsService: UnitsService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) { }

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      if (params['unitId']) {
        this.unitId = +params['unitId'];
      }
    });
    this.loadUnitDetails();
  }

  loadUnitDetails() {
    if (this.unitId) {
      this.isLoading = true;
      this.unitsService.getUnitById(this.unitId).subscribe({
        next: (response) => {
          console.log(response.data);
          this.unitDetails = response.data;
          console.log(this.unitDetails);
          this.concate(this.unitDetails);
          this.isLoading = false;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading unit details:', error);
          this.isLoading = false;
        },
      });
    }
  }

  concate(details: any) {


    // Add gallery items if they exist
    if (details && details.gallery && details.gallery.length > 0) {
      this.AllImages = [...details.gallery];
    }

    // Add diagram if it exists
    if (details && details.diagram) {
      this.AllImages.push({
        type: 'image',
        url: details.diagram,

      });
    }

    // Add location in master plan if it exists
    if (details && details.locationInMasterPlan) {
      this.AllImages.push({
        type: 'image',
        url: details.locationInMasterPlan,

      });
    }

  }

  isFeatureEnabled(featureValue: string): boolean {
    return this.unitDetails?.otherAccessories?.includes(featureValue) || false;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'UNKNOWN': 'غير معروف',
        'UNKNOWN_UNIT_CODE': 'كود وحدة غير معروف',
        'ROOMS': 'غرف',
        'PROPERTY_DETAILS': 'تفاصيل العقار',
        'UNIT_INFORMATION': 'معلومات الوحدة',
        'FEATURES': 'المميزات',
        'BEDROOMS': 'غرف النوم',
        'BATHROOMS': 'الحمامات',
        'AREA': 'المساحة',
        'FLOOR': 'الطابق',
        'COMPOUND': 'كمبوند',
        'GARAGE': 'جراج',
        'CLUBHOUSE': 'نادي اجتماعي',
        'CLUB': 'نادي',
        'STORAGE': 'مخزن',
        'ELEVATOR': 'مصعد',
        'SWIMMING_POOL': 'حمام سباحة',
        'UNIT_IMAGE': 'صورة الوحدة',
        'UNIT_DIAGRAM': 'مخطط الوحدة',
        'LOCATION_IN_MASTER_PLAN': 'الموقع في المخطط الرئيسي',
        'THIS_IS_A': 'هذه وحدة',
        'UNIT_LOCATED_IN': 'تقع في',
        'BUILDING_NUMBER': 'رقم المبنى',
        'ON_THE': 'في الطابق',
        'IT_SPANS_AREA': 'تمتد على مساحة',
        'UNIT_FEATURES': 'تتميز الوحدة بتشطيب',
        'AVAILABLE_WITH': 'ومتاحة مع',
        'FINISHING': 'تشطيب',
        'PRICE_PER_METER_CASH': 'سعر المتر نقدéré',
        'PRICE_PER_METER_INSTALLMENT': 'بينما سعر المتر بالتقسيط',
        'TOTAL_CASH_PRICE': 'السعر الإجمالي نقدéré',
        'TOTAL_INSTALLMENT_PRICE': 'والسعر الإجمالي بالتقسيط',
        'PAYMENT_OPTIONS': 'خيارات الدفع',
        'EGP': 'جنيه مصري',
        'SQUARE_METER': 'متر مربع',
        'FLOOR_WORD': 'الطابق',
        'GROUND': 'أرضي',
        'LAST_FLOOR': 'الطابق الأخير',
        'REPEATED': 'متكرر',
        'ALL_OF_THE_ABOVE': 'جميع ما سبق مناسب',
        'CASH': 'نقدéré',
        'INSTALLMENT': 'تقسيط',
        'ALL_OF_THE_ABOVE_ARE_SUITABLE': 'جميع ما سبق مناسب',
        'PAID_IN_FULL': 'مدفوع بالكامل',
        'PARTIALLY_PAID_WITH_REMAINING_INSTALLMENTS': 'مدفوع جزئي مع أقساط متبقية',
        'MONTHLY': 'شهري',
        'DAILY': 'يومي',
        'ANNUALLY': 'سنوي',
        'IMMEDIATE_DELIVERY': 'تسليم فوري',
        'ON_BRICK': 'على الطوب',
        'SEMI_FINISHED': 'نصف تشطيب',
        'COMPANY_FINISHED': 'تشطيب شركة',
        'SUPER_LUX': 'سوبر لوكس',
        'ULTRA_SUPER_LUX': 'ألترا سوبر لوكس',
        'AVAILABLE': 'متاح',
        'SOLD': 'مباع',
        'RESERVED': 'محجوز',
        'PENDING': 'معلق',
        'NEW': 'جديد',
        'UNAVAILABLE': 'غير متاح',
        'FULL_FINISHED': 'تشطيب كامل',
        'STANDARD': 'تشطيب عادى'
      },
      'en': {
        'UNKNOWN': 'unknown',
        'UNKNOWN_UNIT_CODE': 'unknown unit code',
        'ROOMS': 'Rooms',
        'PROPERTY_DETAILS': 'Property details',
        'UNIT_INFORMATION': 'Unit Information',
        'FEATURES': 'Features',
        'BEDROOMS': 'Bedrooms',
        'BATHROOMS': 'Bathrooms',
        'AREA': 'Area',
        'FLOOR': 'Floor',
        'COMPOUND': 'Compound',
        'GARAGE': 'Garage',
        'CLUBHOUSE': 'Clubhouse',
        'CLUB': 'Club',
        'STORAGE': 'Storage',
        'ELEVATOR': 'Elevator',
        'SWIMMING_POOL': 'Swimming Pool',
        'UNIT_IMAGE': 'Unit Image',
        'UNIT_DIAGRAM': 'Unit Diagram',
        'LOCATION_IN_MASTER_PLAN': 'Location in Master Plan',
        'THIS_IS_A': 'This is a',
        'UNIT_LOCATED_IN': 'unit located in',
        'BUILDING_NUMBER': 'building number',
        'ON_THE': 'on the',
        'IT_SPANS_AREA': 'It spans an area of',
        'UNIT_FEATURES': 'The unit features',
        'AVAILABLE_WITH': 'finishing and is available with',
        'FINISHING': 'finishing',
        'PRICE_PER_METER_CASH': 'The price per meter in cash is',
        'PRICE_PER_METER_INSTALLMENT': 'while the price per meter in installments is',
        'TOTAL_CASH_PRICE': 'The total cash price is',
        'TOTAL_INSTALLMENT_PRICE': 'and the total installment price is',
        'PAYMENT_OPTIONS': 'payment options',
        'EGP': 'EGP',
        'SQUARE_METER': 'm²',
        'FLOOR_WORD': 'floor',
        'GROUND': 'Ground',
        'LAST_FLOOR': 'Last Floor',
        'REPEATED': 'Repeated',
        'ALL_OF_THE_ABOVE': 'All Of The Above',
        'CASH': 'Cash',
        'INSTALLMENT': 'Installment',
        'ALL_OF_THE_ABOVE_ARE_SUITABLE': 'All Of The Above Are Suitable',
        'PAID_IN_FULL': 'Paid In Full',
        'PARTIALLY_PAID_WITH_REMAINING_INSTALLMENTS': 'Partially Paid With Remaining Installments',
        'MONTHLY': 'Monthly',
        'DAILY': 'Daily',
        'ANNUALLY': 'Annually',
        'IMMEDIATE_DELIVERY': 'Immediate Delivery',
        'ON_BRICK': 'On Brick',
        'SEMI_FINISHED': 'Semi Finished',
        'COMPANY_FINISHED': 'Company Finished',
        'SUPER_LUX': 'Super Lux',
        'ULTRA_SUPER_LUX': 'Ultra Super Lux',
        'AVAILABLE': 'Available',
        'SOLD': 'Sold',
        'RESERVED': 'Reserved',
        'PENDING': 'Pending',
        'NEW': 'New',
        'UNAVAILABLE': 'Unavailable',
        'FULL_FINISHED': 'Full Finished',
        'STANDARD': 'Standard'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Get translated feature name
  getTranslatedFeature(featureName: string): string {
    return this.getTranslatedText(featureName.toUpperCase().replace(' ', '_'));
  }

  // Get translated property type
  getTranslatedPropertyType(type: string): string {
    if (!type) return this.getTranslatedText('UNKNOWN');

    const currentLang = this.translationService.getCurrentLanguage();
    const propertyType = PROPERTY_TYPES[type.toLowerCase()];

    if (propertyType) {
      return currentLang === 'ar' ? propertyType.ar : propertyType.en;
    }

    return type;
  }

  // Get translated area/city name
  getTranslatedLocation(location: any): string {
    if (!location) return this.getTranslatedText('UNKNOWN');

    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar' && location.name_ar) {
      return location.name_ar;
    }

    return location.name_en || location.name || this.getTranslatedText('UNKNOWN');
  }

  // Get translated floor type
  getTranslatedFloor(floor: string): string {
    if (!floor) return this.getTranslatedText('UNKNOWN');

    const floorKey = floor.toLowerCase();

    switch (floorKey) {
      case 'ground':
        return this.getTranslatedText('GROUND');
      case 'last_floor':
        return this.getTranslatedText('LAST_FLOOR');
      case 'repeated':
        return this.getTranslatedText('REPEATED');
      case 'all_the_above_are_suitable':
        return this.getTranslatedText('ALL_OF_THE_ABOVE_ARE_SUITABLE');
      default:
        return floor;
    }
  }

  // Get translated payment system
  getTranslatedPaymentSystem(paymentSystem: string): string {
    if (!paymentSystem) return this.getTranslatedText('UNKNOWN');

    // Convert payment system text to translation key
    const paymentKey = paymentSystem.toUpperCase().replace(/\s+/g, '_');

    // Check if we have a translation for this payment system
    const paymentTranslations = [
      'CASH', 'INSTALLMENT', 'ALL_OF_THE_ABOVE_ARE_SUITABLE',
      'PAID_IN_FULL', 'PARTIALLY_PAID_WITH_REMAINING_INSTALLMENTS',
      'MONTHLY', 'DAILY', 'ANNUALLY', 'IMMEDIATE_DELIVERY'
    ];

    if (paymentTranslations.includes(paymentKey)) {
      return this.getTranslatedText(paymentKey);
    }

    // If no specific translation, return the original payment system text
    return paymentSystem;
  }

  // Get translated finishing type
  getTranslatedFinishingType(finishingType: string): string {
    if (!finishingType) return this.getTranslatedText('UNKNOWN');

    // Convert finishing type text to translation key
    const finishingKey = finishingType.toUpperCase().replace(/\s+/g, '_');

    // Check if we have a translation for this finishing type
    const finishingTranslations = [
      'ON_BRICK', 'SEMI_FINISHED', 'COMPANY_FINISHED',
      'SUPER_LUX', 'ULTRA_SUPER_LUX', 'FULL_FINISHED', 'STANDARD', 'ALL_OF_THE_ABOVE'
    ];

    if (finishingTranslations.includes(finishingKey)) {
      return this.getTranslatedText(finishingKey);
    }

    // If no specific translation, return the original finishing type text
    return finishingType;
  }

  // Get translated unit status
  getTranslatedStatus(status: string): string {
    if (!status) return this.getTranslatedText('UNKNOWN');

    // Convert status text to translation key
    const statusKey = status.toUpperCase().replace(/\s+/g, '_');

    // Check if we have a translation for this status
    const statusTranslations = [
      'AVAILABLE', 'SOLD', 'RESERVED', 'PENDING', 'NEW', 'UNAVAILABLE'
    ];

    if (statusTranslations.includes(statusKey)) {
      return this.getTranslatedText(statusKey);
    }

    // If no specific translation, return the original status text
    return status;
  }

  // goBack() {
  //   this.router.navigate(['/developer/projects/models/units']);
  // }
}

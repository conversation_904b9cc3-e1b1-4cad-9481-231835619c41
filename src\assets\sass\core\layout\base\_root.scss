//
// Root
//

@include color-mode(light) {
    // Layouts
    @include property(--#{$prefix}app-bg-color, $app-bg-color);
    @include property(--#{$prefix}app-blank-bg-color, $app-blank-bg-color);    

    // Header base
    @include property(--#{$prefix}app-header-base-bg-color, $app-header-base-bg-color);
    @include property(--#{$prefix}app-header-base-box-shadow, $app-header-base-box-shadow);
    @include property(--#{$prefix}app-header-base-border-bottom, $app-header-base-border-bottom);

    // Header minimize
    @include property(--#{$prefix}app-header-minimize-bg-color, $app-header-minimize-bg-color);
    @include property(--#{$prefix}app-header-minimize-box-shadow, $app-header-minimize-box-shadow);
    @include property(--#{$prefix}app-header-minimize-border-bottom, $app-header-minimize-border-bottom);

    // Header sticky
    @include property(--#{$prefix}app-header-sticky-bg-color, $app-header-sticky-bg-color);
    @include property(--#{$prefix}app-header-sticky-box-shadow, $app-header-sticky-box-shadow);
    @include property(--#{$prefix}app-header-sticky-border-bottom, $app-header-sticky-border-bottom);

    // Header primary
    @include property(--#{$prefix}app-header-primary-base-bg-color, $app-header-primary-base-bg-color);
    @include property(--#{$prefix}app-header-primary-base-box-shadow, $app-header-primary-base-box-shadow);
    @include property(--#{$prefix}app-header-primary-base-border-bottom, $app-header-primary-base-border-bottom);

    @include property(--#{$prefix}app-header-primary-minimize-bg-color, $app-header-primary-minimize-bg-color);
    @include property(--#{$prefix}app-header-primary-minimize-box-shadow, $app-header-primary-minimize-box-shadow);
    @include property(--#{$prefix}app-header-primary-minimize-border-top, $app-header-primary-minimize-border-top);
    @include property(--#{$prefix}app-header-primary-minimize-border-bottom, $app-header-primary-minimize-border-bottom);

    @include property(--#{$prefix}app-header-primary-sticky-bg-color, $app-header-primary-sticky-bg-color);
    @include property(--#{$prefix}app-header-primary-sticky-box-shadow, $app-header-primary-sticky-box-shadow);
    @include property(--#{$prefix}app-header-primary-sticky-border-top, $app-header-primary-sticky-border-top);
    @include property(--#{$prefix}app-header-primary-sticky-border-bottom, $app-header-primary-sticky-border-bottom);

    // Header secondary base
    @include property(--#{$prefix}app-header-secondary-base-bg-color, $app-header-secondary-base-bg-color);
    @include property(--#{$prefix}app-header-secondary-base-box-shadow, $app-header-secondary-base-box-shadow);
    @include property(--#{$prefix}app-header-secondary-base-border-top, $app-header-secondary-base-border-top);
    @include property(--#{$prefix}app-header-secondary-base-border-bottom, $app-header-secondary-base-border-bottom);

    // Header secondary minimize
    @include property(--#{$prefix}app-header-secondary-minimize-bg-color, $app-header-secondary-minimize-bg-color);
    @include property(--#{$prefix}app-header-secondary-minimize-box-shadow, $app-header-secondary-minimize-box-shadow);
    @include property(--#{$prefix}app-header-secondary-minimize-border-top, $app-header-secondary-minimize-border-top);
    @include property(--#{$prefix}app-header-secondary-minimize-border-bottom, $app-header-secondary-minimize-border-bottom);

    // Header secondary sticky
    @include property(--#{$prefix}app-header-secondary-sticky-bg-color, $app-header-secondary-sticky-bg-color);
    @include property(--#{$prefix}app-header-secondary-sticky-box-shadow, $app-header-secondary-sticky-box-shadow);
    @include property(--#{$prefix}app-header-secondary-sticky-border-top, $app-header-secondary-sticky-border-top);
    @include property(--#{$prefix}app-header-secondary-sticky-border-bottom, $app-header-secondary-sticky-border-bottom);

    // Header tertiary base
    @include property(--#{$prefix}app-header-tertiary-base-bg-color, $app-header-tertiary-base-bg-color);     
    @include property(--#{$prefix}app-header-tertiary-base-box-shadow, $app-header-tertiary-base-box-shadow);
    @include property(--#{$prefix}app-header-tertiary-base-border-top, $app-header-tertiary-base-border-top);
    @include property(--#{$prefix}app-header-tertiary-base-border-bottom, $app-header-tertiary-base-border-bottom);

    // Header tertiary minimize
    @include property(--#{$prefix}app-header-tertiary-minimize-bg-color, $app-header-tertiary-minimize-bg-color);
    @include property(--#{$prefix}app-header-tertiary-minimize-box-shadow, $app-header-tertiary-minimize-box-shadow);
    @include property(--#{$prefix}app-header-tertiary-minimize-border-top, $app-header-tertiary-minimize-border-top);
    @include property(--#{$prefix}app-header-tertiary-minimize-border-bottom, $app-header-tertiary-minimize-border-bottom);

    // Header tertiary sticky
    @include property(--#{$prefix}app-header-tertiary-sticky-bg-color, $app-header-tertiary-sticky-bg-color);
    @include property(--#{$prefix}app-header-tertiary-sticky-box-shadow, $app-header-tertiary-sticky-box-shadow);
    @include property(--#{$prefix}app-header-tertiary-sticky-border-top, $app-header-tertiary-sticky-border-top);
    @include property(--#{$prefix}app-header-tertiary-sticky-border-bottom, $app-header-tertiary-sticky-border-bottom);

    // Toolbar base
    @include property(--#{$prefix}app-toolbar-base-bg-color, $app-toolbar-base-bg-color);
    @include property(--#{$prefix}app-toolbar-base-box-shadow, $app-toolbar-base-box-shadow);
    @include property(--#{$prefix}app-toolbar-base-border-top, $app-toolbar-base-border-top);
    @include property(--#{$prefix}app-toolbar-base-border-bottom, $app-toolbar-base-border-bottom);

    // Toolbar minimize
    @include property(--#{$prefix}app-toolbar-minimize-bg-color, $app-toolbar-minimize-bg-color);
    @include property(--#{$prefix}app-toolbar-minimize-box-shadow, $app-toolbar-minimize-box-shadow);
    @include property(--#{$prefix}app-toolbar-minimize-border-top, $app-toolbar-minimize-border-top);
    @include property(--#{$prefix}app-toolbar-minimize-border-bottom, $app-toolbar-minimize-border-bottom);

    // Toolbar sticky
    @include property(--#{$prefix}app-toolbar-sticky-bg-color, $app-toolbar-sticky-bg-color);
    @include property(--#{$prefix}app-toolbar-sticky-box-shadow, $app-toolbar-sticky-box-shadow);
    @include property(--#{$prefix}app-toolbar-sticky-border-top, $app-toolbar-sticky-border-top);
    @include property(--#{$prefix}app-toolbar-sticky-border-bottom, $app-toolbar-sticky-border-bottom);

    // Sidebar base
    @include property(--#{$prefix}app-sidebar-base-bg-color, $app-sidebar-base-bg-color);
    @include property(--#{$prefix}app-sidebar-base-box-shadow, $app-sidebar-base-box-shadow);
    @include property(--#{$prefix}app-sidebar-base-border-start, $app-sidebar-base-border-start);
    @include property(--#{$prefix}app-sidebar-base-border-end, $app-sidebar-base-border-end);

    // Hero
    @include property(--#{$prefix}app-hero-bg-color, $app-hero-bg-color);
    @include property(--#{$prefix}app-hero-box-shadow, $app-hero-box-shadow);
    @include property(--#{$prefix}app-hero-border-top, $app-hero-border-top);
    @include property(--#{$prefix}app-hero-border-bottom, $app-hero-border-bottom);

    // Sidebar sticky
    @include property(--#{$prefix}app-sidebar-sticky-bg-color, $app-sidebar-sticky-bg-color);
    @include property(--#{$prefix}app-sidebar-sticky-box-shadow, $app-sidebar-sticky-box-shadow);
    @include property(--#{$prefix}app-sidebar-sticky-border-start, $app-sidebar-sticky-border-start);
    @include property(--#{$prefix}app-sidebar-sticky-border-end, $app-sidebar-sticky-border-end);

    // Sidebar minimize
    @include property(--#{$prefix}app-sidebar-minimize-bg-color, $app-sidebar-minimize-bg-color);
    @include property(--#{$prefix}app-sidebar-minimize-box-shadow, $app-sidebar-minimize-box-shadow);
    @include property(--#{$prefix}app-sidebar-minimize-hover-box-shadow, $app-sidebar-minimize-hover-box-shadow);
    @include property(--#{$prefix}app-sidebar-minimize-border-start, $app-sidebar-minimize-border-start);
    @include property(--#{$prefix}app-sidebar-minimize-border-end, $app-sidebar-minimize-border-end);

    // Sidebar primary
    @include property(--#{$prefix}app-sidebar-primary-base-bg-color, $app-sidebar-primary-base-bg-color);
    @include property(--#{$prefix}app-sidebar-primary-base-box-shadow, $app-sidebar-primary-base-box-shadow);
    @include property(--#{$prefix}app-sidebar-primary-base-border-start, $app-sidebar-primary-base-border-start);
    @include property(--#{$prefix}app-sidebar-primary-base-border-end, $app-sidebar-primary-base-border-end);

    // Sidebar primary minimize
    @include property(--#{$prefix}app-sidebar-primary-minimize-bg-color, $app-sidebar-primary-minimize-bg-color);
    @include property(--#{$prefix}app-sidebar-primary-minimize-box-shadow, $app-sidebar-primary-minimize-box-shadow);
    @include property(--#{$prefix}app-sidebar-primary-minimize-hover-box-shadow, $app-sidebar-primary-minimize-hover-box-shadow);
    @include property(--#{$prefix}app-sidebar-primary-minimize-border-start, $app-sidebar-primary-minimize-border-start);
    @include property(--#{$prefix}app-sidebar-primary-minimize-border-end, $app-sidebar-primary-minimize-border-end);

    // Sidebar secondary base
    @include property(--#{$prefix}app-sidebar-secondary-base-bg-color, $app-sidebar-secondary-base-bg-color);
    @include property(--#{$prefix}app-sidebar-secondary-base-box-shadow, $app-sidebar-secondary-base-box-shadow);
    @include property(--#{$prefix}app-sidebar-secondary-base-border-start, $app-sidebar-secondary-base-border-start);
    @include property(--#{$prefix}app-sidebar-secondary-base-border-end, $app-sidebar-secondary-base-border-end);

    // Sidebar secondary minimize
    @include property(--#{$prefix}app-sidebar-secondary-minimize-bg-color, $app-sidebar-secondary-minimize-bg-color);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-box-shadow, $app-sidebar-secondary-minimize-box-shadow);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-hover-box-shadow, $app-sidebar-secondary-minimize-hover-box-shadow);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-border-start, $app-sidebar-secondary-minimize-border-start);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-border-end, $app-sidebar-secondary-minimize-border-end);

    // Sidebar panel base
    @include property(--#{$prefix}app-sidebar-panel-base-bg-color, $app-sidebar-panel-base-bg-color);
    @include property(--#{$prefix}app-sidebar-panel-base-box-shadow, $app-sidebar-panel-base-box-shadow);
    @include property(--#{$prefix}app-sidebar-panel-base-border-start, $app-sidebar-panel-base-border-start);
    @include property(--#{$prefix}app-sidebar-panel-base-border-end, $app-sidebar-panel-base-border-end);

    // Sidebar panel sticky
    @include property(--#{$prefix}app-sidebar-panel-sticky-bg-color, $app-sidebar-panel-sticky-bg-color);
    @include property(--#{$prefix}app-sidebar-panel-sticky-box-shadow, $app-sidebar-panel-sticky-box-shadow);
    @include property(--#{$prefix}app-sidebar-panel-sticky-border-start, $app-sidebar-panel-sticky-border-start);
    @include property(--#{$prefix}app-sidebar-panel-sticky-border-end, $app-sidebar-panel-sticky-border-end);

    // Sidebar panel minimize
    @include property(--#{$prefix}app-sidebar-panel-minimize-bg-color, $app-sidebar-panel-minimize-bg-color);
    @include property(--#{$prefix}app-sidebar-panel-minimize-box-shadow, $app-sidebar-panel-minimize-box-shadow);
    @include property(--#{$prefix}app-sidebar-panel-minimize-hover-box-shadow, $app-sidebar-panel-minimize-hover-box-shadow);   
    @include property(--#{$prefix}app-sidebar-panel-minimize-border-start, $app-sidebar-panel-minimize-border-start);
    @include property(--#{$prefix}app-sidebar-panel-minimize-border-end, $app-sidebar-panel-minimize-border-end);

    // Aside base
    @include property(--#{$prefix}app-aside-base-bg-color, $app-aside-base-bg-color);
    @include property(--#{$prefix}app-aside-base-box-shadow, $app-aside-base-box-shadow);

    // Aside sticky
    @include property(--#{$prefix}app-aside-sticky-bg-color, $app-aside-sticky-bg-color);
    @include property(--#{$prefix}app-aside-sticky-box-shadow, $app-aside-sticky-box-shadow);

    // Aside minimize
    @include property(--#{$prefix}app-aside-minimize-bg-color, $app-aside-minimize-bg-color);
    @include property(--#{$prefix}app-aside-minimize-box-shadow, $app-aside-minimize-box-shadow);
    @include property(--#{$prefix}app-aside-minimize-hover-box-shadow, $app-aside-minimize-hover-box-shadow);

    // Page
    @include property(--#{$prefix}app-page-bg-color, $app-page-bg-color);

    // Wrapper
    @include property(--#{$prefix}app-wrapper-bg-color, $app-wrapper-bg-color);

    // Footer
    @include property(--#{$prefix}app-footer-bg-color, $app-footer-bg-color);
    @include property(--#{$prefix}app-footer-box-shadow, $app-footer-box-shadow);
    @include property(--#{$prefix}app-footer-border-top, $app-footer-border-top);
}

@include color-mode(dark) {
    // Layouts
    @include property(--#{$prefix}app-bg-color, $app-bg-color-dark);
    @include property(--#{$prefix}app-blank-bg-color, $app-blank-bg-color-dark);

    // Header base
    @include property(--#{$prefix}app-header-base-bg-color, $app-header-base-bg-color-dark);
    @include property(--#{$prefix}app-header-base-box-shadow, $app-header-base-box-shadow-dark);
    @include property(--#{$prefix}app-header-base-border-bottom, $app-header-base-border-bottom-dark);

    // Header minimize
    @include property(--#{$prefix}app-header-minimize-bg-color, $app-header-minimize-bg-color-dark);
    @include property(--#{$prefix}app-header-minimize-box-shadow, $app-header-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-header-minimize-border-bottom, $app-header-minimize-border-bottom-dark);

    // Header sticky
    @include property(--#{$prefix}app-header-sticky-bg-color, $app-header-sticky-bg-color-dark);
    @include property(--#{$prefix}app-header-sticky-box-shadow, $app-header-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-header-sticky-border-bottom, $app-header-sticky-border-bottom-dark);

    // Header primary
    @include property(--#{$prefix}app-header-primary-base-bg-color, $app-header-primary-base-bg-color-dark);
    @include property(--#{$prefix}app-header-primary-base-box-shadow, $app-header-primary-base-box-shadow-dark);
    @include property(--#{$prefix}app-header-primary-base-border-bottom, $app-header-primary-base-border-bottom-dark);

    @include property(--#{$prefix}app-header-primary-minimize-bg-color, $app-header-primary-minimize-bg-color-dark);
    @include property(--#{$prefix}app-header-primary-minimize-box-shadow, $app-header-primary-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-header-primary-minimize-border-top, $app-header-primary-minimize-border-top-dark);
    @include property(--#{$prefix}app-header-primary-minimize-border-bottom, $app-header-primary-minimize-border-bottom-dark);

    @include property(--#{$prefix}app-header-primary-sticky-bg-color, $app-header-primary-sticky-bg-color-dark);
    @include property(--#{$prefix}app-header-primary-sticky-box-shadow, $app-header-primary-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-header-primary-sticky-border-top, $app-header-primary-sticky-border-top-dark);
    @include property(--#{$prefix}app-header-primary-sticky-border-bottom, $app-header-primary-sticky-border-bottom-dark);

    // Header secondary base
    @include property(--#{$prefix}app-header-secondary-base-bg-color, $app-header-secondary-base-bg-color-dark);
    @include property(--#{$prefix}app-header-secondary-base-box-shadow, $app-header-secondary-base-box-shadow-dark);
    @include property(--#{$prefix}app-header-secondary-base-border-top, $app-header-secondary-base-border-top-dark);
    @include property(--#{$prefix}app-header-secondary-base-border-bottom, $app-header-secondary-base-border-bottom-dark);

    // Header secondary minimize
    @include property(--#{$prefix}app-header-secondary-minimize-bg-color, $app-header-secondary-minimize-bg-color-dark);
    @include property(--#{$prefix}app-header-secondary-minimize-box-shadow, $app-header-secondary-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-header-secondary-minimize-border-top, $app-header-secondary-minimize-border-top-dark);
    @include property(--#{$prefix}app-header-secondary-minimize-border-bottom, $app-header-secondary-minimize-border-bottom-dark);

    // Header secondary sticky
    @include property(--#{$prefix}app-header-secondary-sticky-bg-color, $app-header-secondary-sticky-bg-color-dark);
    @include property(--#{$prefix}app-header-secondary-sticky-box-shadow, $app-header-secondary-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-header-secondary-sticky-border-top, $app-header-secondary-sticky-border-top-dark);
    @include property(--#{$prefix}app-header-secondary-sticky-border-bottom, $app-header-secondary-sticky-border-bottom-dark);

    // Header tertiary base
    @include property(--#{$prefix}app-header-tertiary-base-bg-color, $app-header-tertiary-base-bg-color-dark);     
    @include property(--#{$prefix}app-header-tertiary-base-box-shadow, $app-header-tertiary-base-box-shadow-dark);
    @include property(--#{$prefix}app-header-tertiary-base-border-top, $app-header-tertiary-base-border-top-dark);
    @include property(--#{$prefix}app-header-tertiary-base-border-bottom, $app-header-tertiary-base-border-bottom-dark);

    // Header tertiary minimize
    @include property(--#{$prefix}app-header-tertiary-minimize-bg-color, $app-header-tertiary-minimize-bg-color-dark);
    @include property(--#{$prefix}app-header-tertiary-minimize-box-shadow, $app-header-tertiary-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-header-tertiary-minimize-border-top, $app-header-tertiary-minimize-border-top-dark);
    @include property(--#{$prefix}app-header-tertiary-minimize-border-bottom, $app-header-tertiary-minimize-border-bottom-dark);

    // Header tertiary sticky
    @include property(--#{$prefix}app-header-tertiary-sticky-bg-color, $app-header-tertiary-sticky-bg-color-dark);
    @include property(--#{$prefix}app-header-tertiary-sticky-box-shadow, $app-header-tertiary-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-header-tertiary-sticky-border-top, $app-header-tertiary-sticky-border-top-dark);
    @include property(--#{$prefix}app-header-tertiary-sticky-border-bottom, $app-header-tertiary-sticky-border-bottom-dark);

    // Toolbar base
    @include property(--#{$prefix}app-toolbar-base-bg-color, $app-toolbar-base-bg-color-dark);
    @include property(--#{$prefix}app-toolbar-base-box-shadow, $app-toolbar-base-box-shadow-dark);
    @include property(--#{$prefix}app-toolbar-base-border-top, $app-toolbar-base-border-top-dark);
    @include property(--#{$prefix}app-toolbar-base-border-bottom, $app-toolbar-base-border-bottom-dark);

    // Toolbar minimize
    @include property(--#{$prefix}app-toolbar-minimize-bg-color, $app-toolbar-minimize-bg-color-dark);
    @include property(--#{$prefix}app-toolbar-minimize-box-shadow, $app-toolbar-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-toolbar-minimize-border-top, $app-toolbar-minimize-border-top-dark);
    @include property(--#{$prefix}app-toolbar-minimize-border-bottom, $app-toolbar-minimize-border-bottom-dark);

    // Toolbar sticky
    @include property(--#{$prefix}app-toolbar-sticky-bg-color, $app-toolbar-sticky-bg-color-dark);
    @include property(--#{$prefix}app-toolbar-sticky-box-shadow, $app-toolbar-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-toolbar-sticky-border-top, $app-toolbar-sticky-border-top-dark);
    @include property(--#{$prefix}app-toolbar-sticky-border-bottom, $app-toolbar-sticky-border-bottom-dark);

    // Hero
    @include property(--#{$prefix}app-hero-bg-color, $app-hero-bg-color-dark);
    @include property(--#{$prefix}app-hero-box-shadow, $app-hero-box-shadow-dark);
    @include property(--#{$prefix}app-hero-border-top, $app-hero-border-top-dark);
    @include property(--#{$prefix}app-hero-border-bottom, $app-hero-border-bottom-dark);

    // Sidebar base
    @include property(--#{$prefix}app-sidebar-base-bg-color, $app-sidebar-base-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-base-box-shadow, $app-sidebar-base-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-base-border-start, $app-sidebar-base-border-start-dark);
    @include property(--#{$prefix}app-sidebar-base-border-end, $app-sidebar-base-border-end-dark);

    // Sidebar sticky
    @include property(--#{$prefix}app-sidebar-sticky-bg-color, $app-sidebar-sticky-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-sticky-box-shadow, $app-sidebar-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-sticky-border-start, $app-sidebar-sticky-border-start-dark);
    @include property(--#{$prefix}app-sidebar-sticky-border-end, $app-sidebar-sticky-border-end-dark);

    // Sidebar minimize
    @include property(--#{$prefix}app-sidebar-minimize-bg-color, $app-sidebar-minimize-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-minimize-box-shadow, $app-sidebar-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-minimize-hover-box-shadow, $app-sidebar-minimize-hover-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-minimize-border-start, $app-sidebar-minimize-border-start-dark);
    @include property(--#{$prefix}app-sidebar-minimize-border-end, $app-sidebar-minimize-border-end-dark);

    // Sidebar primary
    @include property(--#{$prefix}app-sidebar-primary-base-bg-color, $app-sidebar-primary-base-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-primary-base-box-shadow, $app-sidebar-primary-base-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-primary-base-border-start, $app-sidebar-primary-base-border-start-dark);
    @include property(--#{$prefix}app-sidebar-primary-base-border-end, $app-sidebar-primary-base-border-end-dark);

    @include property(--#{$prefix}app-sidebar-primary-minimize-bg-color, $app-sidebar-primary-minimize-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-primary-minimize-box-shadow, $app-sidebar-primary-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-primary-minimize-hover-box-shadow, $app-sidebar-primary-minimize-hover-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-primary-minimize-border-start, $app-sidebar-primary-minimize-border-start-dark);
    @include property(--#{$prefix}app-sidebar-primary-minimize-border-end, $app-sidebar-primary-minimize-border-end-dark);

    // Sidebar secondary
    @include property(--#{$prefix}app-sidebar-secondary-base-bg-color, $app-sidebar-secondary-base-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-secondary-base-box-shadow, $app-sidebar-secondary-base-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-secondary-base-border-start, $app-sidebar-secondary-base-border-start-dark);
    @include property(--#{$prefix}app-sidebar-secondary-base-border-end, $app-sidebar-secondary-base-border-end-dark);

    @include property(--#{$prefix}app-sidebar-secondary-minimize-bg-color, $app-sidebar-secondary-minimize-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-box-shadow, $app-sidebar-secondary-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-hover-box-shadow, $app-sidebar-secondary-minimize-hover-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-border-start, $app-sidebar-secondary-minimize-border-start-dark);
    @include property(--#{$prefix}app-sidebar-secondary-minimize-border-end, $app-sidebar-secondary-minimize-border-end-dark);

    // Sidebar panel base
    @include property(--#{$prefix}app-sidebar-panel-base-bg-color, $app-sidebar-panel-base-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-panel-base-box-shadow, $app-sidebar-panel-base-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-panel-base-border-start, $app-sidebar-panel-base-border-start-dark);
    @include property(--#{$prefix}app-sidebar-panel-base-border-end, $app-sidebar-panel-base-border-end-dark);

    // Sidebar panel sticky
    @include property(--#{$prefix}app-sidebar-panel-sticky-bg-color, $app-sidebar-panel-sticky-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-panel-sticky-box-shadow, $app-sidebar-panel-sticky-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-panel-sticky-border-start, $app-sidebar-panel-sticky-border-start-dark);
    @include property(--#{$prefix}app-sidebar-panel-sticky-border-end, $app-sidebar-panel-sticky-border-end-dark);

    // Sidebar panel minimize
    @include property(--#{$prefix}app-sidebar-panel-minimize-bg-color, $app-sidebar-panel-minimize-bg-color-dark);
    @include property(--#{$prefix}app-sidebar-panel-minimize-box-shadow, $app-sidebar-panel-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-panel-minimize-hover-box-shadow, $app-sidebar-panel-minimize-hover-box-shadow-dark);
    @include property(--#{$prefix}app-sidebar-panel-minimize-border-start, $app-sidebar-panel-minimize-border-start-dark);
    @include property(--#{$prefix}app-sidebar-panel-minimize-border-end, $app-sidebar-panel-minimize-border-end-dark);

    // Aside base
    @include property(--#{$prefix}app-aside-base-bg-color, $app-aside-base-bg-color-dark);
    @include property(--#{$prefix}app-aside-base-box-shadow, $app-aside-base-box-shadow-dark);

    // Aside sticky
    @include property(--#{$prefix}app-aside-sticky-bg-color, $app-aside-sticky-bg-color-dark);

    // Aside minimize
    @include property(--#{$prefix}app-aside-minimize-bg-color, $app-aside-minimize-bg-color-dark);
    @include property(--#{$prefix}app-aside-minimize-box-shadow, $app-aside-minimize-box-shadow-dark);
    @include property(--#{$prefix}app-aside-minimize-hover-box-shadow, $app-aside-minimize-hover-box-shadow-dark);

    // Page
    @include property(--#{$prefix}app-page-bg-color, $app-page-bg-color-dark);

    // Wrapper
    @include property(--#{$prefix}app-wrapper-bg-color, $app-wrapper-bg-color-dark);

    // Footer
    @include property(--#{$prefix}app-footer-bg-color, $app-footer-bg-color-dark);
    @include property(--#{$prefix}app-footer-box-shadow, $app-footer-box-shadow-dark);
    @include property(--#{$prefix}app-footer-border-top, $app-footer-border-top-dark);
}
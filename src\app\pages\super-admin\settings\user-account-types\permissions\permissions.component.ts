import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import { TranslationService } from 'src/app/modules/i18n';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.scss']
})
export class PermissionsComponent implements OnInit {

  permissions: any[] = [];

  // Search and pagination properties (following all-developers pattern)
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Sort options
  sortBy = 'id';
  sortDir = 'desc';

  constructor(
    private router: Router,
    private cd: ChangeDetectorRef,
    private settingService: settingService,
    public translationService: TranslationService,
    private translateService: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadPermissions();
  }

  loadPermissions(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.settingService.getAllPermissions(params).subscribe({
      next: (response) => {
        console.log(response);
        this.permissions = response.data || [];
        this.totalElements = response.count || response.totalElements || 0;
        this.totalPages = Math.ceil(this.totalElements / this.pageSize);
        this.loading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        this.loading = false;
        Swal.fire(
          this.translateService.instant('PERMISSIONS.ERROR'),
          this.translateService.instant('PERMISSIONS.LOAD_PERMISSIONS_FAILED'),
          'error'
        );
      }
    });
  }

  onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadPermissions();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadPermissions();
  }

  addPermission(): void {
    const isRTL = this.translationService.isRTL();
    const titleFont = isRTL ? 'Noto Kufi Arabic, sans-serif' : 'inherit';
    const contentFont = isRTL ? 'Hacen Liner Screen, sans-serif' : 'inherit';

    Swal.fire({
      title: this.translateService.instant('PERMISSIONS.ADD_NEW_PERMISSION'),
      html: `
        <div style="direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};">
          <input id="name" class="swal2-input"
                 placeholder="${this.translateService.instant('PERMISSIONS.PERMISSION_NAME')}"
                 style="font-family: ${contentFont}; text-align: ${isRTL ? 'right' : 'left'}; direction: ${isRTL ? 'rtl' : 'ltr'};">
          <textarea id="description" class="swal2-textarea"
                    placeholder="${this.translateService.instant('PERMISSIONS.DESCRIPTION')}"
                    style="font-family: ${contentFont}; text-align: ${isRTL ? 'right' : 'left'}; direction: ${isRTL ? 'rtl' : 'ltr'}; min-height: 80px;"></textarea>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('PERMISSIONS.CREATE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      buttonsStyling: false,
      customClass: {
        popup: isRTL ? 'swal2-rtl custom-swal-arabic' : 'custom-swal-english',
        title: isRTL ? 'swal2-title-rtl' : '',
        htmlContainer: isRTL ? 'swal2-html-rtl' : '',
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-secondary me-2'
      },
      didOpen: () => {
        // Apply custom styles for Arabic
        if (isRTL) {
          const popup = document.querySelector('.swal2-popup') as HTMLElement;
          if (popup) {
            popup.style.fontFamily = titleFont;
            popup.style.direction = 'rtl';
          }

          const title = document.querySelector('.swal2-title') as HTMLElement;
          if (title) {
            title.style.fontFamily = titleFont;
            title.style.fontSize = '1.5rem';
            title.style.fontWeight = '700';
          }

          const buttons = document.querySelectorAll('.swal2-actions .btn');
          buttons.forEach((btn: any) => {
            btn.style.fontFamily = contentFont;
            btn.style.fontSize = '0.95rem';
          });
        }
      },
      preConfirm: () => {
        const name = (document.getElementById('name') as HTMLInputElement).value;
        const description = (document.getElementById('description') as HTMLTextAreaElement).value;

        if (!name) {
          Swal.showValidationMessage(this.translateService.instant('PERMISSIONS.PERMISSION_NAME_REQUIRED'));
          return false;
        }

        return { name, description };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const payload = {
          permissions: [
            {
              name: result.value.name,
              description: result.value.description
            }
          ]
        };

        this.settingService.createPermission(payload).subscribe({
          next: () => {
            Swal.fire({
              title: this.translateService.instant('PERMISSIONS.SUCCESS'),
              text: this.translateService.instant('PERMISSIONS.PERMISSION_CREATED_SUCCESS'),
              icon: 'success',
              customClass: {
                popup: isRTL ? 'swal2-rtl' : '',
                title: isRTL ? 'swal2-title-rtl' : ''
              }
            }).then(() => {
              this.loadPermissions();
            });
          },
          error: () => {
            Swal.fire({
              title: this.translateService.instant('PERMISSIONS.ERROR'),
              text: this.translateService.instant('PERMISSIONS.PERMISSION_CREATE_FAILED'),
              icon: 'error',
              customClass: {
                popup: isRTL ? 'swal2-rtl' : '',
                title: isRTL ? 'swal2-title-rtl' : ''
              }
            });
          }
        });
      }
    });
  }






  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}

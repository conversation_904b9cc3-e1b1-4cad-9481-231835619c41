// Users component styling using Metronic design
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 1.5rem;

  .card-title {
    margin-bottom: 0;

    h3 {
      color: #2d3748;
      font-weight: 700;
    }
  }

  .card-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.card-body {
  padding: 1.5rem;
}

// Table styling
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// User avatar and info
.symbol {
  &.symbol-45px {
    width: 45px;
    height: 45px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// Badge styling
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;

  &.badge-light-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  &.badge-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  &.badge-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }

  &.badge-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  &.badge-light-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
  }

  &.badge-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  &.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #5e6278;

    &:hover, &.btn-active-light-primary:hover {
      background-color: #e9ecef;
      border-color: #e9ecef;
      color: #0d6efd;
    }
  }
}

// Dropdown styling
.dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e3ea;

  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;

    &:hover {
      background-color: #f8f9fa;
    }

    &.text-danger:hover {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545;
    }

    i {
      width: 16px;
      text-align: center;
    }
  }
}

// Search input styling
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.ps-10 {
    padding-left: 2.5rem;
  }
}

.form-select {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Position utilities
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.top-50 {
  top: 50% !important;
}

.start-0 {
  left: 0 !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.fs-2 {
  font-size: 1.5rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fs-7 {
  font-size: 0.875rem !important;
}

.text-muted {
  color: #a1a5b7 !important;
}

.text-gray-800 {
  color: #1e2129 !important;
}

.text-primary {
  color: #0d6efd !important;
}

// RTL and Arabic Language Support
[dir="rtl"] {
  .card-header {
    .card-title {
      text-align: right;

      h3 {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.75rem;
        font-weight: 700;
        line-height: 1.4;
        margin-bottom: 0.25rem;
      }

      span {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
        line-height: 1.5;
      }
    }

    .card-toolbar {
      flex-direction: row-reverse;

      .btn {
        margin-left: 0.5rem;
        margin-right: 0;
        font-family: 'Hacen Liner Screen', sans-serif;

        &:first-child {
          margin-left: 0;
        }

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  // Table headers alignment
  .table thead th {
    text-align: right;
    font-family: 'Noto Kufi Arabic', sans-serif;
    font-size: 0.95rem;
    font-weight: 600;
  }

  .table tbody td {
    text-align: right;
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  // Search input RTL
  .form-control {
    text-align: right;
    direction: rtl;
    font-family: 'Hacen Liner Screen', sans-serif;

    &.pe-10 {
      padding-right: 2.5rem;
      padding-left: 0.75rem;
    }
  }

  .form-select {
    text-align: right;
    direction: rtl;
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  // Icon positioning for RTL
  .position-absolute {
    &.end-0 {
      right: 0 !important;
      left: auto !important;
    }

    &.me-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }
  }

  // Dropdown RTL
  .dropdown-menu {
    text-align: right;

    .dropdown-item {
      text-align: right;
      font-family: 'Hacen Liner Screen', sans-serif;

      i {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }

  // Badge RTL
  .badge {
    font-family: 'Hacen Liner Screen', sans-serif;
  }

  // Flex direction fixes
  .d-flex {
    &.align-items-center {
      &.flex-row-reverse {
        .symbol {
          margin-left: 1rem;
          margin-right: 0;
        }
      }
    }
  }
}

// Arabic font improvements
.arabic-header {
  font-family: 'Noto Kufi Arabic', sans-serif !important;
  font-weight: 700;
  line-height: 1.4;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  line-height: 1.6;
}

// Header icon spacing for RTL
[dir="rtl"] .card-header .d-flex.align-items-center {
  .fas {
    margin-left: 1rem;
    margin-right: 0;
  }
}

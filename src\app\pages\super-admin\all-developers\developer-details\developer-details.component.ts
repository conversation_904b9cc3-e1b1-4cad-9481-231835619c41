import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DevelopersService } from '../../services/developers.service';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-developer-details',
  templateUrl: './developer-details.component.html',
  styleUrls: ['./developer-details.component.scss']
})
export class DeveloperDetailsComponent implements OnInit {
  developer: any = null;

  developerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private developersService: DevelopersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.developerId = params['developerId'];
      if (this.developerId) {
        this.loadDeveloperDetails();
      } else {
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  loadDeveloperDetails(): void {


    this.developersService.getDeveloperById(this.developerId).subscribe({
      next: (response) => {
        console.log('Developer details:', response);
        this.developer = response.data;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading developer details:', error);

        Swal.fire('Error', 'Failed to load developer details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-developers']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getStatusText(status: boolean): string {
    return status ? 'SUPER_ADMIN.COMMON.ACTIVE' : 'SUPER_ADMIN.COMMON.INACTIVE';
  }
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'SUSPEND': 'تعليق',
        'ACTIVATE': 'تنشيط',
        'SUSPEND_DEVELOPER_ACCOUNT': 'هل تريد تعليق حساب هذا المطور ؟',
        'ACTIVATE_DEVELOPER_ACCOUNT': 'هل تريد تفعيل حساب هذا المطور ؟',
        'ARE_YOU_SURE': 'هل انت متأكد ؟',
        'YES': 'نعم',
        'CANCEL': 'الغاء',
        'SUCCESS': 'تم بنجاح',
        'Error': 'خطأ',
        'DEVELOPER_ACCOUNT_HAS_BEEN': 'حساب المطور تم ',
        'ERROR_TOGGLING_STATUS': 'خطأ عند تغيير الحالة',
        'ERROR_TOGGLING_STATUS_UPDATING': 'خطأ عند تغيير الحالة ، حاول مجدداً',
      },
      'en': {
        'SUSPEND': 'Suspend',
        'ACTIVATE': 'Activate',
        'SUSPEND_DEVELOPER_ACCOUNT': 'Suspend This Developer Account ?',
        'ACTIVATE_DEVELOPER_ACCOUNT': 'Activate This Developer Account ?',
        'ARE_YOU_SURE': 'Are You Sure?',
        'YES': 'Yes',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success',
        'Error': 'Error',
        'DEVELOPER_ACCOUNT_HAS_BEEN': 'Developer Account Has Been',
        'ERROR_TOGGLING_STATUS': 'Error Toggling Status: ',
        'ERROR_TOGGLING_STATUS_UPDATING': 'Failed to update developer status. Please try again.',
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
  toggleDeveloperStatus(): void {
    if (!this.developer) return;

    const action = this.developer.isActive ? this.getTranslatedText('SUSPEND') : this.getTranslatedText('ACTIVATE');
    const message = this.developer.isActive ? this.getTranslatedText('SUSPEND_DEVELOPER_ACCOUNT') : this.getTranslatedText('ACTIVATE_DEVELOPER_ACCOUNT');

    Swal.fire({
      title: this.getTranslatedText('ARE_YOU_SURE'),
      text: `${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      cancelButtonText: this.getTranslatedText('CANCEL'),
      confirmButtonText: this.getTranslatedText('YES') + `, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.developersService.toggleDeveloperStatus(this.developer.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.developer.isActive = !this.developer.isActive;
            Swal.fire(
              this.getTranslatedText('SUCCESS'),
              `${this.getTranslatedText('DEVELOPER_ACCOUNT_HAS_BEEN')} ${this.developer.isActive ? this.getTranslatedText('ACTIVATE') : this.getTranslatedText('SUSPEND')}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error(this.getTranslatedText('ERROR_TOGGLING_STATUS'), error);
            Swal.fire(this.getTranslatedText('ERROR'), this.getTranslatedText('ERROR_TOGGLING_STATUS_UPDATING'), 'error');
          }
        });
      }
    });
  }
}

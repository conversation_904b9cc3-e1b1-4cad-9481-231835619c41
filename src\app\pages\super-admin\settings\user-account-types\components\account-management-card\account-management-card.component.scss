.card {
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);

    .symbol-label {
      transform: scale(1.05);
    }
  }

  &:active {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
}

// RTL and Arabic Language Support
[dir="rtl"] {
  .card-body {
    text-align: center;

    .text-center {
      h3 {
        font-family: 'Noto <PERSON> Arabic', sans-serif;
        font-size: 1.6rem;
        font-weight: 700;
        line-height: 1.4;
        margin-bottom: 1rem;
        text-align: center;
      }

      p {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        text-align: center;
        margin-bottom: 1.5rem;
      }
    }

    .symbol {
      margin-bottom: 1.5rem;
    }
  }
}

// RTL Support for Project Pie Chart
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-title {
    text-align: right;
  }
  
  .d-flex.fw-semibold.align-items-center {
    flex-direction: row-reverse;
    
    :host-context(html[dir=rtl]),
    :host-context(html[lang="ar"]){
      .d-flex.fw-semibold.align-items-center{
          flex-direction: row;
      }
    }
    .text-gray-500 {
      text-align: right;
      margin-right: 0 !important;
      margin-left: 1rem !important;
    }

    .bullet {
      margin-right: 0 !important;
      margin-left: 0.75rem !important;
    }
  }
}

// Enhanced styling for Arabic
:host-context(html[lang="ar"]) {
  .fs-6 {
    font-size: 0.9rem !important;
  }
}

// Enhanced Arabic Layout for Brokers Dashboard
.container-fluid {
  max-width: 100%;
  padding: 0 1rem;

  // Arabic RTL improvements
  &[style*="direction: rtl"] {
    .row {
      margin-left: 0;
      margin-right: 0;

      .col-lg-3,
      .col-md-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        margin-bottom: 1rem;
      }
    }

    // Header card improvements
    .card-body {
      .d-flex.align-items-center.justify-content-between {
        gap: 1rem !important;
        flex-wrap: wrap;

        .order-1 {
          flex-shrink: 0;
        }

        .order-2 {
          flex: 1;
          min-width: 0;

          .d-flex.align-items-center {
            gap: 1rem !important;

            .symbol {
              flex-shrink: 0;
              margin-left: 0 !important;
              margin-right: 1rem !important;
            }

            .flex-grow-1 {
              text-align: right !important;

              h1 {
                font-family: 'Noto <PERSON>', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 800 !important;
                margin-bottom: 0.5rem !important;
                line-height: 1.3 !important;
              }

              p {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-size: 1rem !important;
                color: #6b7280 !important;
                margin-bottom: 0 !important;
              }
            }
          }
        }
      }
    }

    // Statistics cards improvements
    .card {
      border-radius: 12px !important;
      border: 1px solid #e5e7eb !important;

      .card-body {
        padding: 1.5rem !important;

        .d-flex.align-items-center {
          gap: 1rem !important;

          .symbol {
            margin-left: 0 !important;
            margin-right: 1rem !important;
            flex-shrink: 0;

            &.symbol-45px {
              width: 50px !important;
              height: 50px !important;
            }

            .symbol-label {
              border-radius: 10px !important;
            }
          }

          div[class*="text-end"] {
            text-align: right !important;
            flex: 1;

            .fs-2 {
              font-family: 'Noto Kufi Arabic', sans-serif !important;
              font-size: 2rem !important;
              font-weight: 900 !important;
              color: #1f2937 !important;
              margin-bottom: 0.5rem !important;
            }

            .fs-7 {
              font-family: 'Hacen Liner Screen', sans-serif !important;
              font-size: 0.95rem !important;
              color: #6b7280 !important;
              font-weight: 600 !important;
            }
          }
        }
      }
    }
  }
}

// General improvements
.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

.card {
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
    border-color: rgba(34, 197, 94, 0.2);
  }
}

.progress {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.table {
  th {
    border-top: none;
    font-weight: 600;
    color: #5E6278;
    font-size: 0.875rem;
    padding: 1rem 0.75rem;
  }

  td {
    border-top: 1px solid #EEF0F8;
    vertical-align: middle;
    padding: 1rem 0.75rem;
  }
}

.bg-light-primary,
.bg-light-success,
.bg-light-warning,
.bg-light-info,
.bg-light-danger {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.rating {
  .fa-star,
  .fa-star-half-alt {
    font-size: 0.875rem;
    margin-right: 2px;
  }
}

.symbol {
  .symbol-label {
    border-radius: 8px;
  }
}

.progress-bar {
  transition: width 0.6s ease;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

// RTL Table Support
.table-rtl {
  direction: rtl;
  text-align: right;

  th, td {
    text-align: right !important;
    padding-right: 1rem;
    padding-left: 0.5rem;
  }

  th:first-child,
  td:first-child {
    padding-right: 1.5rem;
  }

  th:last-child,
  td:last-child {
    padding-left: 1.5rem;
  }
}

.table-ltr {
  direction: ltr;
  text-align: left;

  th, td {
    text-align: left !important;
  }
}

// RTL Card Support
[style*="direction: rtl"] {
  .card-header {
    .d-flex {
      justify-content: flex-end !important;
    }

    .symbol {
      margin-left: 1rem;
      margin-right: 0;
    }
  }

  .table-responsive {
    overflow-x: auto;
    direction: rtl;
  }

  .card-title {
    text-align: right;
  }
}

// Force RTL alignment for Arabic
.justify-content-end-rtl {
  justify-content: flex-end !important;
}

.justify-content-start-ltr {
  justify-content: flex-start !important;
}

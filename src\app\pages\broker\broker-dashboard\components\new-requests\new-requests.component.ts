import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { BrokerService } from '../../../services/broker.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-new-requests',
  templateUrl: './new-requests.component.html',
  styleUrl: './new-requests.component.scss',
})
export class NewRequestsComponent implements OnInit {

  userId :any ;
  latestRequests: any[] = [];
  latestRequestsCount: number = 0;
  loading = false;
  errorMessage = '';
  orderBy: string;
  orderDir: string;

  constructor(protected cd: ChangeDetectorRef, private brokerService: BrokerService, private translate: TranslateService) {
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.userId = user?.id;
    this.fetchLatestRequests();
  }

  fetchLatestRequests(): void {
    this.loading = true;
    this.brokerService.getLatestRequests(this.userId).subscribe({
      next: (requests :any) => {
        this.latestRequests = requests.data;
        this.latestRequestsCount = requests.count;
        this.cd.detectChanges();
        console.log(this.latestRequests);
        console.log(this.latestRequestsCount);
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load requests';
        console.error(error);
        this.loading = false;
      }
    });
  }

  getTranslatedText(key: string): string {
    const translations: { [key: string]: { ar: string; en: string } } = {
      'RECENT_REQUESTS': {
        ar: 'الطلبات الحديثة',
        en: 'Recent Requests'
      },
      'YOU_HAVE': {
        ar: 'لديك',
        en: 'You have'
      },
      'NEW_REQUESTS': {
        ar: 'طلبات جديدة',
        en: 'new requests'
      },
      'VIEW_ALL': {
        ar: 'عرض الكل',
        en: 'View all'
      },
      'REQUEST': {
        ar: 'الطلب',
        en: 'Request'
      },
      'OWNER_NAME': {
        ar: 'اسم المالك',
        en: 'Owner Name'
      },
      'ROLE': {
        ar: 'الدور',
        en: 'Role'
      },
      'DATE': {
        ar: 'التاريخ',
        en: 'Date'
      },
      'STATUS': {
        ar: 'الحالة',
        en: 'Status'
      }
    };

    const currentLang = this.translate.currentLang || 'en';
    return translations[key]?.[currentLang as 'ar' | 'en'] || key;
  }

}

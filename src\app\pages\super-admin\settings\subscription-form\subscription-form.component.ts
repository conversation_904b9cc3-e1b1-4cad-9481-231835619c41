import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { SubscriptionService } from 'src/app/pages/broker/services/subscription.service';
import { TranslationService } from 'src/app/modules/i18n';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-subscription-form',
  templateUrl: './subscription-form.component.html',
  styleUrls: ['./subscription-form.component.scss']
})
export class SubscriptionFormComponent implements OnInit {

  subscriptionForm: FormGroup;
  isEditMode = false;
  subscriptionId: number | null = null;
  selectedImage: File | null = null;
  imagePreview: string | null = null;
  image: any;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private subscriptionService : SubscriptionService,
    public translationService: TranslationService,
    private translateService: TranslateService
  ) {
    this.subscriptionForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(255)]],
      description: [''],
      price: [0, [Validators.required, Validators.min(0)]],
      maxSpecializationScopes: [0, [Validators.required, Validators.min(0)]],
      maxSpecializations: [0, [Validators.required, Validators.min(0)]],
      maxLocations: [0, [Validators.required, Validators.min(0)]],
      maxAdvertisements: [0, [Validators.required, Validators.min(0)]],
      maxOperations: [0, [Validators.required, Validators.min(0)]],
      specialAdvertisementsCount: [0, [Validators.required, Validators.min(0)]],
      image: [null, [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.subscriptionId = +params['id'];
        this.loadSubscription(this.subscriptionId);
        // Remove required validator for image in edit mode
        this.subscriptionForm.get('image')?.clearValidators();
        this.subscriptionForm.get('image')?.updateValueAndValidity();
      }
    });
  }

  loadSubscription(id: number): void {
    this.subscriptionService.getById(id).subscribe({
      next: (response) => {
        console.log(response);
        this.image = response.data.image;
        this.subscriptionForm.patchValue(response.data);
      },
      error: (err) => {
        console.error('Failed to load subscription:', err);
      }
    });
  }

  onImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedImage = file;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);

      this.subscriptionForm.patchValue({ image: file });
    }
  }

  onSubmit(): void {
    if (this.subscriptionForm.valid) {
      const formData = new FormData();

      // Append all form fields
      Object.keys(this.subscriptionForm.value).forEach(key => {
        if (key !== 'image') {
          formData.append(key, this.subscriptionForm.value[key]);
        }
      });

      // Append image if selected
      if (this.selectedImage) {
        formData.append('image', this.selectedImage);
      }

      console.log('Form submitted:', this.subscriptionForm.value);

       if (this.isEditMode) {
        // Call update API
        this.subscriptionService.update(this.subscriptionId!, formData).subscribe({
          next: (response) => {
            this.router.navigate(['/super-admin/subscriptions']);
          },
          error: (err) => {
            console.error('Update failed:', err);
          }
        });
      } else {
      // Call create API
        this.subscriptionService.create(formData).subscribe({
          next: (response) => {
            this.router.navigate(['/super-admin/subscriptions']);
          },
          error: (err) => {
            console.error('Create failed:', err);
          }
        });
      }

    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/super-admin/subscriptions']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.subscriptionForm.controls).forEach(key => {
      const control = this.subscriptionForm.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.subscriptionForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.subscriptionForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return this.translateService.instant('SUBSCRIPTION.REQUIRED_FIELD');
      }
      if (field.errors['min']) {
        return this.translateService.instant('SUBSCRIPTION.MIN_VALUE_ERROR');
      }
      if (field.errors['maxlength']) {
        return this.translateService.instant('SUBSCRIPTION.MAX_LENGTH_ERROR');
      }
    }
    return '';
  }
}

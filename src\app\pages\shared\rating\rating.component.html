<div class="rating-container" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'"
  [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <h2 class="rating-title"
    [style.font-family]="translationService.isRTL() ? 'Noto Ku<PERSON> Arabic, sans-serif' : 'inherit'">
    {{ getTranslatedText('FEEDBACK_FORM') }}
  </h2>

  <div class="rating-form" [class.rtl-form]="translationService.getCurrentLanguage() === 'ar'">
    <div class="rating-row" [class.rtl-row]="translationService.getCurrentLanguage() === 'ar'"
      [class.flex-row-reverse]="translationService.isRTL()">

      <div class="stars-container" [class.rtl-stars]="translationService.getCurrentLanguage() === 'ar'">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('responseSpeed', star)"
          (click)="setRating('responseSpeed', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label"
        [style.font-family]="translationService.isRTL() ? 'Markazi Text, sans-serif' : 'inherit'"
        [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">
        {{ getTranslatedRatingLabel('responseSpeed') }}
      </label>
    </div>

    <div class="rating-row" [class.rtl-row]="translationService.getCurrentLanguage() === 'ar'"
      [class.flex-row-reverse]="translationService.isRTL()">

      <div class="stars-container" [class.rtl-stars]="translationService.getCurrentLanguage() === 'ar'">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('communicationStyle', star)"
          (click)="setRating('communicationStyle', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label"
        [style.font-family]="translationService.isRTL() ? 'Markazi Text, sans-serif' : 'inherit'"
        [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">
        {{ getTranslatedRatingLabel('communicationStyle') }}
      </label>
    </div>

    <div class="rating-row" [class.rtl-row]="translationService.getCurrentLanguage() === 'ar'"
      [class.flex-row-reverse]="translationService.isRTL()">

      <div class="stars-container" [class.rtl-stars]="translationService.getCurrentLanguage() === 'ar'">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('requestUnderstanding', star)"
          (click)="setRating('requestUnderstanding', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label"
        [style.font-family]="translationService.isRTL() ? 'Markazi Text, sans-serif' : 'inherit'"
        [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">
        {{ getTranslatedRatingLabel('requestUnderstanding') }}
      </label>
    </div>

    <div class="rating-row" [class.rtl-row]="translationService.getCurrentLanguage() === 'ar'"
      [class.flex-row-reverse]="translationService.isRTL()">

      <div class="stars-container" [class.rtl-stars]="translationService.getCurrentLanguage() === 'ar'">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('projectExecution', star)"
          (click)="setRating('projectExecution', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label"
        [style.font-family]="translationService.isRTL() ? 'Markazi Text, sans-serif' : 'inherit'"
        [class.rtl-label]="translationService.getCurrentLanguage() === 'ar'">
        {{ getTranslatedRatingLabel('projectExecution') }}
      </label>
    </div>

    <button *ngIf="showSubmitButton" class="submit-button"
      [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'"
      [style.font-family]="translationService.isRTL() ? 'Markazi Text, sans-serif' : 'inherit'" (click)="submitRating()"
      [disabled]="!isFormValid()">
      {{ getTranslatedText('SUBMIT_RATING') }}
    </button>


  </div>
</div>

<div class="container-fluid brokers-dashboard" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <!-- Header -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center w-100 justify-content-between">

            <!-- المحتوى الرئيسي -->
            <div class="d-flex align-items-center flex-grow-1" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()">
                <div class="symbol-label bg-light-primary">
                  <i class="ki-duotone ki-user-tick fs-3 text-primary">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                  </i>
                </div>
              </div>
              <div class="flex-grow-1" [class.text-start]="!translationService.isRTL()"
                [class.text-end]="translationService.isRTL()">
                <h1 class="text-gray-900 fw-bold mb-1 fs-2"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.TITLE' | translate }}
                </h1>
                <p class="text-muted mb-0 fs-6"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.DESCRIPTION' | translate }}
                </p>
              </div>
            </div>

            <!-- زر التحديث -->
            <div class="flex-shrink-0">
              <button class="btn btn-success btn-sm" (click)="refresh()"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <i class="ki-duotone ki-arrows-circle fs-5" [class.me-2]="!translationService.isRTL()"
                  [class.ms-2]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.REFRESH_DATA' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-50px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-people text-primary fs-3">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                  <span class="path5"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalBrokers) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.ALL_BROKERS' | translate }}
              </div>
              <div class="fs-8 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ totalComplaints | arabicNumbers }} {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.COMPLAINTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-50px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-calendar-tick fs-3 text-warning">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                  <span class="path5"></span>
                  <span class="path6"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(expiredSubscriptions) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.EXPIRED_SUBSCRIPTIONS' | translate }}
              </div>
              <div class="fs-8 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ formatNumber(expiredWithInMonthSubscriptions) }} {{
                'SUPER_ADMIN.DASHBOARD.BROKERS.EXPIRE_WITHIN_MONTH' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-50px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-home fs-3 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalUnits) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.TOTAL_UNITS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-50px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-check-circle text-success fs-3">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalSoldUnits) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SOLD_UNITS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Broker Performance -->
  <div class="row mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <div class="col-12">
      <div class="card border-0 shadow-sm" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-chart-simple fs-3 text-primary">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-3 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.MONTHLY_REQUESTS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.MONTH' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SPECIALIZATION' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.UNIT_TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let request of requests">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ request.month }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ getTranslatedSpecializationScope(request.category) }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ getTranslatedUnitType(request.unitType) }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-primary fs-7">{{ request.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Specialization Stats and Monthly Performance -->
  <div class="row g-4 mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <!-- company broker -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-office-bag fs-3 text-success">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.COMPANY_BROKERS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SPECIALIZATION' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SUMMARY' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let broker of totalCompanyBrokers">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ getTranslatedSpecializationScope(broker.specialization) }}

                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-success fs-7">{{ broker.summary }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!--Freelance Brokers-->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-profile-user fs-3 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.FREELANCE_BROKERS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SPECIALIZATION' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SUMMARY' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let broker of totalFreeLanceBrokers">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ getTranslatedSpecializationScope(broker.specialization) }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-info fs-7">{{ broker.summary }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-4 mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <!-- New Subscriptions -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-star text-warning fs-3">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.NEW_SUBSCRIPTIONS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.DATE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.SUBSCRIPTIONS' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.BROKERS.VALUE' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let stats of newSubscriptions">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ stats.date }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ stats.numberOfSubscriptions }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-warning fs-7">{{ stats.value }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
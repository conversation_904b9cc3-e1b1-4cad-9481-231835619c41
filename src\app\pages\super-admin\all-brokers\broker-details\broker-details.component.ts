import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BrokersService } from '../../services/brokers.service';
import { TranslationService } from '../../../../modules/i18n';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-broker-details',
  templateUrl: './broker-details.component.html',
  styleUrls: ['./broker-details.component.scss']
})
export class BrokerDetailsComponent implements OnInit {
  broker: any;

  brokerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private brokersService: BrokersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.brokerId = params['brokerId'];
      if (this.brokerId) {
        this.loadBrokerDetails();
      } else {
        this.router.navigate(['/super-admin/all-brokers']);
      }
    });
  }

  loadBrokerDetails(): void {
    this.brokersService.getBrokerById(this.brokerId).subscribe({
      next: (response) => {
        console.log('Broker details:', response);
        this.broker = response.data;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading broker details:', error);
        Swal.fire('Error', 'Failed to load broker details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-brokers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-brokers']);
  }

  getInitials(name: any): string {
    if (!name) return '';
    return name.split(' ').map((n: any) => n.charAt(0)).join('').toUpperCase();
  }
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'SUSPEND': 'تعليق',
        'ACTIVATE': 'تنشيط',
        'SUSPEND_BROKER_ACCOUNT': 'هل تريد تعليق حساب هذا الوسيط ؟',
        'ACTIVATE_BROKER_ACCOUNT': 'هل تريد تفعيل حساب هذا الوسيط ؟',
        'ARE_YOU_SURE': 'هل انت متأكد ؟',
        'YES': 'نعم',
        'CANCEL': 'الغاء',
        'SUCCESS': 'تم بنجاح',
        'Error': 'خطأ',
        'BROKER_ACCOUNT_HAS_BEEN': 'حساب الوسيط تم ',
        'ERROR_TOGGLING_STATUS': 'خطأ عند تغيير الحالة',
        'ERROR_TOGGLING_STATUS_UPDATING': 'خطأ عند تغيير الحالة ، حاول مجدداً',
      },
      'en': {
        'SUSPEND': 'Suspend',
        'ACTIVATE': 'Activate',
        'SUSPEND_BROKER_ACCOUNT': 'Suspend This Broker Account ?',
        'ACTIVATE_BROKER_ACCOUNT': 'Activate This Broker Account ?',
        'ARE_YOU_SURE': 'Are You Sure?',
        'YES': 'Yes',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success',
        'Error': 'Error',
        'BROKER_ACCOUNT_HAS_BEEN': 'Broker Account Has Been',
        'ERROR_TOGGLING_STATUS': 'Error Toggling Status: ',
        'ERROR_TOGGLING_STATUS_UPDATING': 'Failed to update Broker status. Please try again.',
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }


  toggleBrokerStatus(): void {
    if (!this.broker) return;

    const action = this.broker.isActive ? this.getTranslatedText('SUSPEND') : this.getTranslatedText('ACTIVATE');
    const message = this.broker.isActive ? this.getTranslatedText('SUSPEND_BROKER_ACCOUNT') : this.getTranslatedText('ACTIVATE_BROKER_ACCOUNT');

    Swal.fire({
      title: this.getTranslatedText('ARE_YOU_SURE'),
      text: `${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      cancelButtonText: this.getTranslatedText('CANCEL'),
      confirmButtonText: this.getTranslatedText('YES') + `, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.brokersService.toggleBrokerStatus(this.broker.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.broker.isActive = !this.broker.isActive;
            Swal.fire(
              this.getTranslatedText('SUCCESS'),
              `${this.getTranslatedText('BROKER_ACCOUNT_HAS_BEEN')} ${this.broker.isActive ? this.getTranslatedText('ACTIVATE') : this.getTranslatedText('SUSPEND')}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error(this.getTranslatedText('ERROR_TOGGLING_STATUS'), error);
            Swal.fire(this.getTranslatedText('ERROR'), this.getTranslatedText('ERROR_TOGGLING_STATUS_UPDATING'), 'error');
          }
        });
      }
    });
  }

  getStatusText(status: any): string {
    if (typeof status === 'boolean') {
      return status ? 'SUPER_ADMIN.COMMON.ACTIVE' : 'SUPER_ADMIN.COMMON.INACTIVE';
    }
    // Handle string status
    return status === 'active' ? 'SUPER_ADMIN.COMMON.ACTIVE' : 'SUPER_ADMIN.COMMON.INACTIVE';
  }

  getStatusClass(status: any): string {
    if (typeof status === 'boolean') {
      return status ? 'badge-light-success' : 'badge-light-danger';
    }
    // Handle string status
    return status === 'active' ? 'badge-light-success' : 'badge-light-danger';
  }
}

.card-shadow {
  box-shadow: 0 10px 30px rgba(253, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: linear-gradient(60deg, #fff, #cfe2ff);
  border-radius: 16px;
  border: none;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(253, 0, 0, 0.12);
  }
}

// Base responsive styles
.broker-pages {
  padding: 0 15px;

  @media (min-width: 768px) {
    padding: 0 30px;
  }

  @media (min-width: 1200px) {
    padding: 0 50px;
  }
}

// Card responsive adjustments
.card {
  .card-body {
    .symbol {
      .h-65px {
        width: 50px;
        height: 50px;

        @media (min-width: 768px) {
          width: 65px;
          height: 65px;
        }
      }
    }

    .fs-1 {
      font-size: 1.5rem !important;

      @media (min-width: 768px) {
        font-size: 2rem !important;
      }

      @media (min-width: 1200px) {
        font-size: 2.5rem !important;
      }
    }

    .fs-3 {
      font-size: 1.2rem !important;

      @media (min-width: 768px) {
        font-size: 1.5rem !important;
      }

      @media (min-width: 1200px) {
        font-size: 1.75rem !important;
      }
    }
  }
}

// Statistics boxes responsive design
.d-flex.flex-center.flex-wrap.mb-5 {
  gap: 0.5rem;

  .border.border-dashed.rounded {
    flex: 1 1 calc(50% - 0.25rem);
    min-width: 120px;
    padding: 0.75rem 0.5rem;
    text-align: center;
    border-radius: 8px;

    @media (min-width: 576px) {
      flex: 1 1 calc(50% - 0.5rem);
      padding: 1rem 0.75rem;
    }

    @media (min-width: 768px) {
      flex: 1 1 calc(25% - 0.75rem);
      padding: 1rem;
    }

    @media (min-width: 1200px) {
      padding: 1.25rem 1rem;
    }

    .fs-4 {
      font-size: 1rem !important;
      margin-bottom: 0.25rem;

      @media (min-width: 768px) {
        font-size: 1.1rem !important;
      }

      @media (min-width: 1200px) {
        font-size: 1.25rem !important;
      }
    }

    .fw-semibold {
      font-size: 0.75rem !important;
      line-height: 1.2;

      @media (min-width: 768px) {
        font-size: 0.8rem !important;
      }

      @media (min-width: 1200px) {
        font-size: 0.875rem !important;
      }
    }
  }
}

// Button responsive design
.btn {
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;

  @media (min-width: 768px) {
    font-size: 1rem;
    padding: 1rem 2rem;
  }

  .fa-solid {
    margin-right: 0.5rem;
    font-size: 0.875rem;

    @media (min-width: 768px) {
      font-size: 1rem;
    }
  }
}

// RTL Support for Subscription
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .broker-pages {
    direction: rtl;
    text-align: right;
  }

  .card {
    .card-body {
      text-align: center;

      .fs-1, .fs-3 {
        font-family: 'Noto Kufi Arabic', sans-serif;
        text-align: center;
      }

      .fw-semibold.text-gray-500.mb-6 {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: center;
      }

      .btn {
        font-family: 'Noto Kufi Arabic', sans-serif;

        .fa-solid {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  // Statistics boxes RTL adjustments
  .d-flex.flex-center.flex-wrap.mb-5 {
    direction: ltr; // Keep statistics in LTR for better readability

    .border.border-dashed.rounded {
      text-align: center;

      .fs-4 {
        font-family: 'Noto Kufi Arabic', sans-serif;
      }

      .fw-semibold {
        font-family: 'Hacen Liner Screen St', sans-serif;
      }
    }
  }
}

// Mobile-first responsive design
@media (max-width: 575.98px) {
  .broker-pages {
    padding: 0 10px;
  }

  .card {
    .card-body {
      padding: 1rem 0.75rem !important;
    }
  }

  .d-flex.flex-center.flex-wrap.mb-5 {
    .border.border-dashed.rounded {
      flex: 1 1 100% !important;
      margin-bottom: 0.75rem !important;
      padding: 1rem !important;
    }
  }

  .btn {
    width: 100%;
    padding: 1rem !important;
    font-size: 1rem !important;
  }
}

// Small devices (landscape phones, 576px and up)
@media (min-width: 576px) and (max-width: 767.98px) {
  .d-flex.flex-center.flex-wrap.mb-5 {
    .border.border-dashed.rounded {
      flex: 1 1 calc(50% - 0.5rem) !important;
    }
  }
}

// Medium devices (tablets, 768px and up)
@media (min-width: 768px) and (max-width: 991.98px) {
  .d-flex.flex-center.flex-wrap.mb-5 {
    .border.border-dashed.rounded {
      flex: 1 1 calc(50% - 0.5rem) !important;
    }
  }
}

// Large devices (desktops, 992px and up)
@media (min-width: 992px) {
  .d-flex.flex-center.flex-wrap.mb-5 {
    .border.border-dashed.rounded {
      flex: 1 1 calc(25% - 0.75rem) !important;
    }
  }
}

// Extra large devices (large desktops, 1200px and up)
@media (min-width: 1200px) {
  .broker-pages {
    padding: 0 60px;
  }

  .card {
    .card-body {
      padding: 2.5rem 3rem !important;
    }
  }
}

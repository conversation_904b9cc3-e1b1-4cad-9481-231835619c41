import { BrokerService } from './../../services/broker.service';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AccountTypeMapper } from '../../account-type-mapper';
import { AuthenticationService } from 'src/app/pages/authentication';
import { PermissionService } from 'src/app/pages/shared/services/permission.service';
import { TranslationService } from 'src/app/modules/i18n';

import { PropertyTranslationService } from 'src/app/shared/services/property-translation.service';

@Component({
  selector: 'app-broker-title',
  templateUrl: './broker-title.component.html',
  styleUrl: './broker-title.component.scss',
})

export class BrokerTitleComponent implements OnInit {
  @Input() showCreateButton: boolean = true;
  user: any = {};
  currentLang: string = 'en';

  constructor(
    protected cd: ChangeDetectorRef,
    private brokerService: BrokerService,
    private authenticationService:AuthenticationService,
    public permissionService:PermissionService,
    private translationService: TranslationService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  ngOnInit(): void {
    // let currentUser = localStorage.getItem('currentUser');
    // let userId = currentUser ? JSON.parse(currentUser).id : null;
    // this.getUser(userId);
    this.user = this.authenticationService.getSessionUser();

    // Initialize current language
    this.currentLang = this.translationService.getCurrentLanguage();

    // Subscribe to language changes
    this.translationService.currentLanguage$.subscribe(lang => {
      this.currentLang = lang;
      this.cd.detectChanges();
    });
  }

  getUser(id: any) {
    this.brokerService.getById(id).subscribe({
      next: (response: any) => {
        console.log(response);
        this.user = response?.data ?? {};
        this.cd.detectChanges();
      },
      error: (error: any) => {
        Swal.fire(error.error.message, '', 'error');
      },
    });
  }

  capitalizeWords(text: string | null): string {
    if (!text) return '';
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  }

  getAccountTypeBadge(type: string): string {
    return AccountTypeMapper.getAccountTypeBadge(type);
  }

  hasPermission(permission: string){
    return this.permissionService.hasPermission(permission);
  }

  // Account type translations
  accountTypeMap: { [key: string]: { en: string; ar: string } } = {
    'golden_account': {
      en: 'Golden Account',
      ar: 'حساب ذهبي'
    },
    'premium_account': {
      en: 'Premium Account',
      ar: 'حساب مميز'
    },
    'standard_account': {
      en: 'Standard Account',
      ar: 'حساب عادي'
    },
    'basic_account': {
      en: 'Basic Account',
      ar: 'حساب أساسي'
    },
    'silver_account': {
      en: 'Silver Account',
      ar: 'حساب فضي'
    },
    'bronze_account': {
      en: 'Bronze Account',
      ar: 'حساب برونزي'
    },
    'vip_account': {
      en: 'VIP Account',
      ar: 'حساب كبار الشخصيات'
    },
    'professional_account': {
      en: 'Professional Account',
      ar: 'حساب محترف'
    }
  };

  getTranslatedAccountType(accountType: string): string {
    if (!accountType) return '';

    const currentLang = this.currentLang as 'en' | 'ar';
    const isArabic = currentLang === 'ar';

    // Convert to lowercase with underscores for consistent matching
    const normalizedKey = accountType.toLowerCase().replace(/\s+/g, '_');

    // Check our local translation map first
    if (this.accountTypeMap[normalizedKey]) {
      return isArabic ? this.accountTypeMap[normalizedKey].ar : this.accountTypeMap[normalizedKey].en;
    }

    // Fallback to PropertyTranslationService
    let translated = this.propertyTranslationService.translatePropertyType(accountType, currentLang, 100);

    // If translation is the same as original, try with different cases
    if (translated === accountType) {
      // Try with spaces replaced by underscores
      const withUnderscores = accountType.replace(/\s+/g, '_').toLowerCase();
      translated = this.propertyTranslationService.translatePropertyType(withUnderscores, currentLang, 100);

      // If still no translation, try uppercase
      if (translated === withUnderscores) {
        const upperCase = accountType.toUpperCase();
        translated = this.propertyTranslationService.translatePropertyType(upperCase, currentLang, 100);
      }
    }

    // If still no translation found, return capitalized original
    if (translated === accountType || translated === normalizedKey) {
      return this.capitalizeWords(accountType);
    }

    return translated;
  }

  // Specialization display map with translations
  specializationDisplayMap: { [key: string]: { en: string; ar: string } } = {
    'purchasing_sell_residential_outside_compound': {
      en: 'Apartments - duplexes - penthouses - roof - basements - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - روف - بدروم - استوديو'
    },
    'purchasing_sell_national_housing_projects_outside_compound': {
      en: 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
      ar: 'الإسكان القومي - الإسكان المختلط - إسكان الشباب - جنة مصر - دار مصر - سكن مصر'
    },
    'purchasing_sell_administrative_commercial_units_outside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'purchasing_sell_industrial_and_warehousing_outside_compound': {
      en: 'Factories - Warehouses - Industrial Lands - Warehouse Lands',
      ar: 'مصانع - مخازن - أراضي صناعية - أراضي مخازن'
    },
    'purchasing_sell_lands_and_ready_projects_outside_compound': {
      en: 'Administrative & Commercial Lands - Commercial Administrative Malls',
      ar: 'أراضي إدارية وتجارية - مولات إدارية تجارية'
    },
    'purchasing_sell_villas_and_buildings_outside_compound': {
      en: 'Villas - Full Buildings - Residential Lands - Concrete Structure',
      ar: 'فيلات - مباني كاملة - أراضي سكنية - هيكل خرساني'
    },
    'purchasing_sell_residential_inside_compound': {
      en: 'Apartments - duplexes - penthouses - i villa - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - آي فيلا - استوديو'
    },
    'purchasing_sell_villas_inside_compound': {
      en: 'Villas - Standalone - town house - twin house',
      ar: 'فيلات - منفصلة - تاون هاوس - توين هاوس'
    },
    'purchasing_sell_administrative_commercial_units_inside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'rent_residential_inside_compound': {
      en: 'Apartments - duplexes - penthouses - i villa - villas - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - آي فيلا - فيلات - استوديو'
    },
    'rent_hotel_Units_inside_compound': {
      en: 'Hotel Units',
      ar: 'وحدات فندقية'
    },
    'rent_administrative_commercial_units_inside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات'
    },
    'rent_residential_outside_compound': {
      en: 'Apartments - duplexes - penthouses - roof - basements - villas - studio',
      ar: 'شقق - دوبلكس - بنتهاوس - روف - بدروم - فيلات - استوديو'
    },
    'rent_national_housing_projects_compound': {
      en: 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
      ar: 'الإسكان القومي - الإسكان المختلط - إسكان الشباب - جنة مصر - دار مصر - سكن مصر'
    },
    'rent_administrative_commercial_units_outside_compound': {
      en: 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',
      ar: 'وحدات إدارية - تجارية - صيدليات - عيادات - محلات - مباني كاملة'
    },
    'rent_industrial_and_warehousing_outside_compound': {
      en: 'Factories - Warehouses',
      ar: 'مصانع - مخازن'
    },
    'rent_hotel_units_outside_compound': {
      en: 'Hotel Units',
      ar: 'وحدات فندقية'
    }
  };

  // Translate specialization using local map
  getTranslatedSpecialization(specialization: string): string {
    if (!specialization) return '';

    const currentLang = this.currentLang as 'en' | 'ar';
    const isArabic = currentLang === 'ar';

    // Convert to lowercase with underscores for consistent matching
    const normalizedKey = specialization.toLowerCase().replace(/\s+/g, '_');

    // Check our local translation map first
    if (this.specializationDisplayMap[normalizedKey]) {
      return isArabic ? this.specializationDisplayMap[normalizedKey].ar : this.specializationDisplayMap[normalizedKey].en;
    }

    // Fallback to PropertyTranslationService
    let translated = this.propertyTranslationService.translatePropertyType(specialization, currentLang, 100);

    // If translation is the same as original, try with different cases
    if (translated === specialization) {
      // Try with spaces replaced by underscores
      const withUnderscores = specialization.replace(/\s+/g, '_').toLowerCase();
      translated = this.propertyTranslationService.translatePropertyType(withUnderscores, currentLang, 100);

      // If still no translation, try uppercase
      if (translated === withUnderscores) {
        const upperCase = specialization.toUpperCase();
        translated = this.propertyTranslationService.translatePropertyType(upperCase, currentLang, 100);
      }
    }

    // If still no translation found, return capitalized original
    if (translated === specialization || translated === normalizedKey) {
      return this.capitalizeWords(specialization);
    }

    return translated;
  }
}

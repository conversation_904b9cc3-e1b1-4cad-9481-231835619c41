<!-- Settings Header using Metronic classes -->
<div class="row mb-8" [class.rtl-layout]="translationService.isRTL()">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex align-items-center justify-content-between"
        [class.flex-row-reverse]="translationService.isRTL()">
        <div class="card-title-wrapper">
          <h3 class="card-title mb-0"
            [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
            [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
            [style.justify-content]="translationService.isRTL() ? 'flex-start!important' : 'inherit'">
            {{ 'SUPER_ADMIN.SETTINGS.TITLE' | translate }}
          </h3>
        </div>
        <div class="card-toolbar">
          <button class="btn btn-primary"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
            <i class="fas fa-cog" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()"></i>
            {{ 'SUPER_ADMIN.SETTINGS.ADVANCED_SETTINGS' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Admin Feature Cards using Metronic spacing -->
<div class="row justify-content-center g-6 g-xl-9" [class.rtl-layout]="translationService.isRTL()">
  <div class="col-lg-4 col-md-6" *ngFor="let feature of adminFeatures">
    <app-admin-card [faIcon]="feature.faIcon" [title]="feature.titleKey | translate"
      [description]="feature.descriptionKey | translate" [backgroundColor]="feature.backgroundColor"
      [iconColor]="feature.iconColor" [routePath]="feature.routePath" (cardClick)="onCardClick($event)">
    </app-admin-card>
  </div>
</div>
<div class="card card-custom card-stretch mb-5 bg-hover-light-primary">
  <div class="card-header border-0 pt-5 pb-5 d-flex align-items-center justify-content-between">
    <div class="d-flex flex-column flex-grow-1">
      <div class="d-flex align-items-center mb-2">
        <!-- Font Awesome Icon -->
        <ng-container *ngIf="isFontAwesomeIcon()">
          <i class="fa-regular fa-{{ icon.name }} text-dark-blue fs-3 me-2"></i>
        </ng-container>

        <!-- Keen Icon -->
        <ng-container *ngIf="isKeenIcon()">
          <app-keenicon [name]="getKeenIconName()" [type]="getKeenIconType()"
            class="fs-3 fw-semibold text-dark-blue me-2"></app-keenicon>
        </ng-container>

        <!-- Custom SVG Icon -->
        <ng-container *ngIf="isSvgIcon()">
          <span [innerHTML]="getSafeSvgContent()" class="fs-3 me-2"></span>
        </ng-container>

        <span class="card-label fw-bolder fs-3 text-dark-blue">
          {{ title }}
        </span>
      </div>

      <span class="text-muted fw-bold fs-7">
        {{ subTitle }}
      </span>
    </div>
    <div class="card-toolbar">
      <a routerLink="{{ buttonLink }}" class="btn btn-sm btn-light-dark-blue">
        {{ buttonTitle }}
        <i class="fa-solid fa-{{ buttonIcon }} fs-7"></i>
      </a>
    </div>
  </div>
</div>
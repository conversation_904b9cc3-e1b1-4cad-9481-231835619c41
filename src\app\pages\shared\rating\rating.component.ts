import { Component, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import Swal from 'sweetalert2';
import { BrokerService } from '../../broker/services/broker.service';
import { TranslationService } from 'src/app/modules/i18n';

export interface RatingData {
  responseSpeed: number;
  communicationStyle: number;
  requestUnderstanding: number;
  projectExecution: number;
}

@Component({
  selector: 'app-rating',
  templateUrl: './rating.component.html',
  styleUrls: ['./rating.component.scss']
})
export class RatingComponent {
  @Input() title: string = 'Feedback Form';
  @Input() showSubmitButton: boolean = true;
  @Input() brokerId: number;
  @Output() ratingSubmitted = new EventEmitter<RatingData>();

  userId: number;

  ratings: RatingData = {
    responseSpeed: 0,
    communicationStyle: 0,
    requestUnderstanding: 0,
    projectExecution: 0
  };

  ratingLabels = {
    responseSpeed: 'Response Speed',
    communicationStyle: 'Communication Style',
    requestUnderstanding: 'Request Understanding',
    projectExecution: 'Project Execution'
  };

  constructor(
    protected cd: ChangeDetectorRef,
    protected brokerService: BrokerService,
    public translationService: TranslationService
  ) {
    this.userId = JSON.parse(localStorage.getItem('currentUser')!).id;
  }

  setRating(category: keyof RatingData, rating: number): void {
    this.ratings[category] = rating;
  }

  getStars(): number[] {
    return [1, 2, 3, 4, 5];
  }

  isStarFilled(category: keyof RatingData, starNumber: number): boolean {
    return this.ratings[category] >= starNumber;
  }

  submitRating(): void {
    const totalRating = this.calculateTotalRating();
    this.ratingSubmitted.emit(this.ratings);

    const payload = {
      ratedId: this.brokerId,
      rate: totalRating
    };

    this.brokerService.rateBroker(this.userId, payload).subscribe({
      next: (response) => {
        Swal.fire(this.getTranslatedText('RATING_SUBMITTED'), '', 'success');
      },
      error: (error) => {
        Swal.fire(this.getTranslatedText('FAILED_TO_SUBMIT_RATING'), '', 'error');
        console.error(error);
      }
    });

  }

  calculateTotalRating(): number {
    const ratings = [
      this.ratings.responseSpeed,
      this.ratings.communicationStyle,
      this.ratings.requestUnderstanding,
      this.ratings.projectExecution
    ];

    const sum = ratings.reduce((total, rating) => total + rating, 0);
    const average = sum / ratings.length;
    return Math.round(average * 100) / 100;
  }

  isFormValid(): boolean {
    return Object.values(this.ratings).every(rating => rating > 0);
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'FEEDBACK_FORM': 'نموذج التقييم',
        'RESPONSE_SPEED': 'سرعة الاستجابة',
        'COMMUNICATION_STYLE': 'أسلوب التواصل',
        'REQUEST_UNDERSTANDING': 'فهم الطلب',
        'PROJECT_EXECUTION': 'تنفيذ المشروع',
        'SUBMIT_RATING': 'إرسال التقييم',
        'RATING_SUBMITTED': 'تم إرسال التقييم بنجاح!',
        'FAILED_TO_SUBMIT_RATING': 'فشل في إرسال التقييم.'
      },
      'en': {
        'FEEDBACK_FORM': 'Feedback Form',
        'RESPONSE_SPEED': 'Response Speed',
        'COMMUNICATION_STYLE': 'Communication Style',
        'REQUEST_UNDERSTANDING': 'Request Understanding',
        'PROJECT_EXECUTION': 'Project Execution',
        'SUBMIT_RATING': 'Submit Rating',
        'RATING_SUBMITTED': 'Rating submitted successfully!',
        'FAILED_TO_SUBMIT_RATING': 'Failed to submit rating.'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || translations['en'][key] || key;
  }

  // Helper method to get translated rating label
  getTranslatedRatingLabel(key: keyof RatingData): string {
    const labelKeys = {
      responseSpeed: 'RESPONSE_SPEED',
      communicationStyle: 'COMMUNICATION_STYLE',
      requestUnderstanding: 'REQUEST_UNDERSTANDING',
      projectExecution: 'PROJECT_EXECUTION'
    };

    return this.getTranslatedText(labelKeys[key]);
  }
}

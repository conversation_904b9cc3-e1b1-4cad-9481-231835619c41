// Required field indicator
.required {
  color: #dc3545; // Bootstrap danger color
  font-weight: bold;
  margin-left: 2px;
}

// RTL Support
:host-context(.rtl) {
  .required {
    margin-left: 0;
    margin-right: 2px;
  }

  .broker-registration-stepper {
    direction: rtl;
    text-align: right;
  }

  .form-group {
    text-align: right !important;

    .form-label {
      justify-content: flex-start !important;
      text-align: right;
      direction: rtl;

      // عكس Bootstrap margins في RTL
      .me-2 {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }

      .ms-1 {
        margin-left: 0 !important;
        margin-right: 0.25rem !important;
      }
    }

    label {
      justify-content: flex-start !important;
      text-align: right;
      direction: rtl;

      // عكس Bootstrap margins في RTL
      .me-2 {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }

      .ms-1 {
        margin-left: 0 !important;
        margin-right: 0.25rem !important;
      }
    }
  }

  .form-check {
    text-align: right;
    direction: rtl;

    .form-check-input {
      margin-left: 0.5rem;
      margin-right: 0;
    }

    .form-check-label {
      text-align: right;
    }
  }
}

// Alert styles
.alert {
  padding: 10px 14px;
  margin-bottom: 12px;
  margin-top: 8px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 13px;
  text-align: center;
  direction: ltr;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  line-height: 1.4;
  max-width: 100%;

  &.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }
}

// Variables
$primary-color: #232176;
$success-color: #28a745;
$blue-color: #4c63d2;
$text-color: #333;
$muted-color: #6c757d;
$border-color: #ddd;
$bg-light: #f8f9fa;

// Mixins
@mixin transition($property: all, $duration: 0.3s) {
  transition: $property $duration ease;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-1px);
  }
}

@mixin button-base {
  border: none;
  border-radius: 8px;
  cursor: pointer;
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

@mixin form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid $border-color;
  border-radius: 6px;
  font-size: 14px;
  @include transition();
  background-color: #fff;

  &:focus {
    outline: none;
    border-color: $success-color;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
  }

  &.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  }
}

@mixin card-hover {
  @include transition();
  cursor: pointer;

  &:hover {
    border-color: $blue-color;
    background: #f0f4ff;
    @include hover-lift();
    box-shadow: 0 2px 8px rgba(76, 99, 210, 0.15);
  }
}

@mixin section-description {
  color: $success-color;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.client-registration-stepper {
  width: 100%;
  direction: ltr;
  text-align: left;

  // Ensure validation messages are left-aligned, alerts are centered
  .invalid-feedback {
    text-align: left !important;
    direction: ltr !important;
  }

  .alert {
    text-align: center !important;
    direction: ltr !important;
  }

  .stepper-header {
    margin-bottom: 10px;
    text-align: center;

    .stepper-title {
      color: $text-color;
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 15px;
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: #e9ecef;
      border-radius: 3px;
      margin-bottom: 10px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, $success-color 0%, #20c997 100%);
        border-radius: 3px;
        @include transition(width);
      }
    }

    .d-flex {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;

      .progress-text {
        color: #666;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .back-to-previous {
        background: none;
        border: none;
        color: #007bff;
        font-size: 0.85rem;
        cursor: pointer;
        padding: 3px 0;
        @include transition(color);

        &:hover {
          color: #0056b3;
          text-decoration: underline;
        }
      }
    }
  }

  .stepper-form {
    .step-content {
      min-height: 50px;
      margin-bottom: 5px;
      text-align: center;
      padding: 2px 5px;

      // Compact Step 7 (Account Details)
      &:has(.form-group:nth-child(n+4)) {
        .form-group {
          margin-bottom: 10px;
        }

        .alert {
          margin-top: 6px;
          margin-bottom: 10px;
          padding: 8px 12px;
          font-size: 12px;
        }
      }

      &.success-step {
        min-height: 50px;
        padding: 5px;
      }

      &.compact-step {
        min-height: 15px;
        padding: 3px 8px;
        margin-bottom: 8px;

        .step-title {
          margin-bottom: 4px;
          font-size: 13px;
        }

        .form-group {
          margin-bottom: 3px;
        }

        .btn-verification {
          margin: 2px auto 1px;
          padding: 10px 16px;
          font-size: 13px;
          min-height: 40px;
        }

        .skip-button {
          margin: 1px auto 1px;
          padding: 2px 4px;
          font-size: 10px;
        }

        .help-text {
          margin-top: 1px;
          font-size: 9px;
        }

        // Step 5 - Cities and Areas
        .areas-section:not(:has(.specialization-grid)) {
          .form-group {
            margin-bottom: 22px;
          }

          .btn-verification {
            margin: 35px auto 20px;
            padding: 12px 16px;
          }
        }

        // Step 6 - Specializations Collapse
        .specialization-collapse {
          width: 130%;
          margin-left: -15%;
          padding: 4px;

          .specialization-item {
            margin-bottom: 4px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: $bg-light;

            .specialization-header {
              padding: 4px 8px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              @include transition(background-color);

              &:hover:not(.disabled) {
                background-color: #e9ecef;
              }

              &.disabled {
                opacity: 0.5;
                pointer-events: none;
              }

              .scope-checkbox-label {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
                flex: 1;

                .scope-checkbox {
                  width: 16px;
                  height: 16px;
                  accent-color: $success-color;
                }

                .specialization-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: $success-color;
                }
              }

              .collapse-icon {
                font-size: 12px;
                color: $muted-color;
                @include transition(transform, color);
                cursor: pointer;
                padding: 4px;

                &:hover {
                  color: $success-color;
                }
              }
            }

            .specialization-content {
              padding: 4px 8px;
              border-top: 1px solid #e9ecef;
              background: white;

              .specialization-checkboxes {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 4px;

                .checkbox-label {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  padding: 2px;
                  font-size: 14px;
                  line-height: 1.2;

                  input[type="checkbox"] {
                    width: 14px;
                    height: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .step-title {
      color: $primary-color;
      font-size: 0.9rem;
      font-weight: 600;
      margin-bottom: 5px;
      text-align: center;
      padding-bottom: 3px;


      .compact-step & {
        font-size: 0.85rem;
        margin-bottom: 3px;
        padding-bottom: 2px;
      }
    }

    .form-group {
      margin-bottom: 12px;

      label {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
        font-weight: 500;
        color: $text-color;
        font-size: 0.9rem;
        text-align: left;

        i {
          color: $success-color;
          font-size: 1rem;
        }
      }

      .form-control {
        @include form-control;
        margin-bottom: 5px;

        &::placeholder {
          color: #999;
        }
      }

      .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 12px;
        margin-top: 3px;
        margin-bottom: 2px;
        font-weight: 500;
        text-align: left;
        direction: ltr;
      }

      .form-check {
        display: flex;
        align-items: center;
        gap: 8px;
        text-align: left;

        .form-check-input {
          margin-top: 3px;
          width: 18px;
          height: 18px;
          cursor: pointer;

          &.is-invalid {
            border-color: #dc3545;
          }
        }

        .form-check-label {
          cursor: pointer;
          font-size: 14px;
          line-height: 1.4;
          color: $text-color;
        }
      }
    }

    // Verification Code Styles
    .verification-code-section {
      margin: 15px 0;
      text-align: center;
      padding: 0 5px;

      .verification-inputs {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 8px;

        .verification-input {
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 1.5rem;
          font-weight: bold;
          border: 2px solid $border-color;
          border-radius: 8px;
          background-color: #fff;
          color: $text-color;
          @include transition();

          &:focus {
            outline: none;
            border-color: $success-color;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
          }

          &.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
          }

          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
          }

          &[type=number] {
            -moz-appearance: textfield;
            appearance: textfield;
          }
        }
      }
    }

    .countdown-section {
      text-align: center;
      margin-bottom: 8px;
    }



    .countdown-text {
      color: $muted-color;
      font-size: 13px;

      .countdown-timer {
        color: #007bff;
        font-weight: bold;
      }
    }

    // Common Sections
    .documents-section,
    .areas-section,
    .broker-type-section {
      text-align: center;

      .documents-description,
      .areas-description,
      .broker-type-description {
        @include section-description;
      }
    }

    // Upload Cards
    .upload-card-container {
      margin-bottom: 6px;

      .card {
        border: 2px dashed #e9ecef;
        border-radius: 6px;
        background: $bg-light;
        @include card-hover;

        .card-body {
          padding: 8px 12px;
          text-align: center;

          .upload-icon i {
            font-size: 1.2rem;
            color: $blue-color;
            margin-bottom: 4px;
          }

          .upload-text {
            display: block;
            color: $text-color;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 3px;

            .badge {
              font-size: 10px;
              padding: 2px 6px;
            }
          }

          .upload-subtitle {
            color: $muted-color;
            font-size: 10px;
          }
        }
      }
    }

    .skip-button {
      @include button-base;
      background: none;
      color: #007bff;
      font-size: 14px;
      font-weight: 500;
      margin: 12px auto 0;
      width: fit-content;
      text-decoration: none;
      gap: 10px;

      &:hover {
        color: #0056b3;
      }

      &:focus {
        outline: none;
        box-shadow: none;
      }

      i {
        font-size: 14px;
        margin-left: 4px;
      }
    }

    // Dropdown Styling
    .dropdown {
      position: relative;

      .dropdown-toggle {
        text-align: left;
        background-color: white;
        
        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
          border-color: $success-color;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .dropdown-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background-color: white;
        margin-top: 2px;

        &.show {
          display: block;
        }

        .dropdown-item {
          padding: 8px 16px;
          @include transition(background-color);
          cursor: pointer;

          &:hover {
            background-color: rgba(40, 167, 69, 0.1);
            color: $success-color;
          }
        }

        // Custom scrollbar
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 3px;
          
          &:hover {
            background: #555;
          }
        }
      }
    }

    .form-check {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-top: 12px;

      .form-check-input {
        width: 18px;
        height: 18px;
        cursor: pointer;
        margin-top: 3px;
      }

      .form-check-label {
        font-size: 14px;
        color: $text-color;
        cursor: pointer;
        line-height: 1.4;
        text-align: left;
      }
    }

    // Success Step
    .success-content {
      text-align: center;
      padding: 20px;

      .success-icon i {
        font-size: 4rem;
        color: $success-color;
        margin-bottom: 20px;
      }

      .success-title {
        color: $text-color;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 15px;
      }

      .success-message {
        color: #666;
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 20px;

        .dashboard-link {
          color: #007bff;
          text-decoration: none;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            color: #0056b3;
            text-decoration: underline;
          }

          &:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
          }
        }
      }

      .success-image {
        max-width: 200px;
        height: auto;
        margin: 20px 0;
      }

      .btn-success-action {
        @include button-base;
        background: linear-gradient(135deg, $success-color 0%, #20c997 100%);
        color: white;
        padding: 12px 24px;
        font-size: 14px;
        margin: 15px auto;
        display: block;
        width: fit-content;

        &:hover {
          @include hover-lift;
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
      }

      .info-link {
        color: #007bff;
        font-size: 0.9rem;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    // Broker Type & Areas Selection
    .broker-type-options {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-bottom: 20px;

      .broker-type-card {
        flex: 1;
        max-width: 200px;
        padding: 20px 15px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        background: $bg-light;
        @include card-hover;
        text-align: center;

        &.selected {
          border-color: $blue-color;
          background: #f0f4ff;
          box-shadow: 0 4px 12px rgba(76, 99, 210, 0.2);

          .broker-type-icon i,
          .broker-type-title {
            color: $blue-color;
          }
        }

        .broker-type-icon i {
          font-size: 2rem;
          color: $muted-color;
          margin-bottom: 10px;
          @include transition(color);
        }

        .broker-type-title {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
          margin-bottom: 8px;
          @include transition(color);
        }

        .broker-type-desc {
          font-size: 12px;
          color: $muted-color;
          line-height: 1.4;
        }
      }
    }


  }

  // Buttons & Help
  .btn-verification {
    @include button-base;
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 20px;
    margin: 25px auto 15px;
    background: linear-gradient(135deg, $blue-color 0%, #3b4db8 100%);
    color: white;
    text-align: center;

    &:hover:not(:disabled) {
      @include hover-lift;
      box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    &.loading {
      position: relative;
      pointer-events: none;

      .spinner-border {
        width: 1rem;
        height: 1rem;
        border-width: 0.125em;
      }
    }

    .spinner-border {
      display: inline-block;
      width: 1rem;
      height: 1rem;
      vertical-align: text-bottom;
      border: 0.125em solid currentColor;
      border-right-color: transparent;
      border-radius: 50%;
      animation: spinner-border 0.75s linear infinite;
    }
  }

  @keyframes spinner-border {
    to {
      transform: rotate(360deg);
    }
  }

  .help-text {
    text-align: center;
    margin-top: 8px;
    font-size: 12px;
    color: $muted-color;

    .contact-link {
      color: #007bff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // Specialization Grid Styling
  .specialization-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 15px 0;
    padding: 25px;
    border: 1px solid #e9ecef;
    border-radius: 10px;

    .specialization-card {
      background: $bg-light;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      @include transition();
      min-height: 120px;

      &:hover {
        border-color: $success-color;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
      }

      .specialization-title {
        font-size: 16px;
        font-weight: 600;
        color: $success-color;
        margin-bottom: 12px;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
      }

      .specialization-checkboxes {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;

        .checkbox-label {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          padding: 6px;
          @include transition(color);
          font-size: 14px;
          white-space: nowrap;

          &:hover {
            color: $success-color;
            background-color: rgba(40, 167, 69, 0.05);
            border-radius: 4px;
          }

          input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: $success-color;
            flex-shrink: 0;
          }

          .checkbox-text {
            font-size: 14px;
            color: #495057;
          }
        }
      }
    }
  }
}

.scrollable-select {
  option {
    max-height: 200px;
  }
}

// Custom scrollable select styling
select.scrollable-select {
  max-height: 200px;
  overflow-y: auto;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
    
    &:hover {
      background: #555;
    }
  }
}

// Enhanced Specialization Section
.specialization-section {
  margin: 20px 0;
  
  .specialization-collapse {
    width: 100%;
    margin: 0;
    padding: 0;
    
    .specialization-item {
      margin-bottom: 16px;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: $success-color;
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.15);
        transform: translateY(-2px);
      }
      
      &.selected {
        border-color: $success-color;
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.2);
      }
      
      .specialization-header {
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-bottom: 1px solid #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover:not(.disabled) {
          background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
        }
        
        &.disabled {
          opacity: 0.6;
          pointer-events: none;
          background: #f8f9fa;
        }
        
        .scope-checkbox-label {
          display: flex;
          align-items: center;
          gap: 12px;
          cursor: pointer;
          flex: 1;
          font-weight: 600;
          
          .scope-checkbox {
            width: 20px;
            height: 20px;
            accent-color: $success-color;
            cursor: pointer;
            border-radius: 4px;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
            
            &:checked {
              background-color: $success-color;
              border-color: $success-color;
            }
            
            &:focus {
              outline: none;
              box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
            }
          }
          
          .specialization-name {
            font-size: 16px;
            font-weight: 600;
            color: $success-color;
            text-align: right;
            flex: 1;
            transition: all 0.3s ease;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            overflow-wrap: break-word;
          }
        }
        
        .collapse-icon {
          font-size: 16px;
          color: $success-color;
          transition: all 0.3s ease;
          cursor: pointer;
          padding: 8px;
          border-radius: 50%;
          
          &:hover {
            background-color: rgba(40, 167, 69, 0.1);
            transform: scale(1.1);
          }
          
          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }
        }
      }
      
      .specialization-content {
        padding: 20px;
        background: white;
        border-top: none;
        animation: slideDown 0.3s ease;
        
        .specialization-checkboxes {
          display: grid;
          grid-template-columns: 1fr;
          gap: 12px;
          
          .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            line-height: 1.5;
            
            &:hover {
              border-color: $success-color;
              background: rgba(40, 167, 69, 0.05);
              transform: translateX(-4px);
            }
            
            input[type="checkbox"] {
              width: 18px;
              height: 18px;
              accent-color: $success-color;
              cursor: pointer;
              margin-top: 2px;
              flex-shrink: 0;
              border-radius: 4px;
              
              &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
              }
            }
            
            .checkbox-text {
              color: #495057;
              font-weight: 500;
              text-align: right;
              flex: 1;
            }
            
            &:has(input:checked) {
              border-color: $success-color;
              background: rgba(40, 167, 69, 0.1);
              
              .checkbox-text {
                color: $success-color;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}

// Animation for content expansion
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

// RTL Support for specializations
:host-context(.rtl) {
  .specialization-section {
    .specialization-collapse {
      .specialization-item {
        .specialization-header {
          .scope-checkbox-label {
            .specialization-name {
              text-align: right;
            }
          }
        }
        
        .specialization-content {
          .specialization-checkboxes {
            .checkbox-label {
              text-align: right;
              
              &:hover {
                transform: translateX(4px);
              }
              
              .checkbox-text {
                text-align: right;
              }
            }
          }
        }
      }
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .specialization-section {
    .specialization-collapse {
      .specialization-item {
        .specialization-header {
          padding: 12px 16px;
          
          .scope-checkbox-label {
            gap: 8px;
            
            .specialization-name {
              font-size: 14px;
            }
          }
        }
        
        .specialization-content {
          padding: 16px;
          
          .specialization-checkboxes {
            gap: 8px;
            
            .checkbox-label {
              padding: 10px 12px;
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

// Specialization Dropdown Section
.specialization-dropdown-section {
  .specialization-dropdown-item {
    margin-bottom: 16px;
    
    &:nth-child(4),
    &:nth-child(5) {
      .dropdown {
        .specialization-dropdown-menu {
          max-height: 140px;
        }
      }
    }
    
    .dropdown {
      position: relative;
      
      .specialization-dropdown-toggle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        transition: all 0.3s ease;
        cursor: pointer;
        min-height: 60px;
        
        &:hover {
          border-color: $success-color;
          background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
          transform: translateY(-2px);
        }
        
        &.selected {
          border-color: $success-color;
          background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
          box-shadow: 0 4px 16px rgba(40, 167, 69, 0.2);
        }
        
        &.has-selections {
          border-color: $success-color;
          background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
          
          .dropdown-header-content {
            .specialization-name {
              color: $success-color;
              font-weight: 700;
            }
          }
          
          .dropdown-arrow {
            color: $success-color;
          }
        }
        
        &:focus {
          outline: none;
          border-color: $success-color;
          box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
        }
        
        .dropdown-header-content {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;
          
          .scope-checkbox {
            width: 20px;
            height: 20px;
            accent-color: $success-color;
            cursor: pointer;
            border-radius: 4px;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
            flex-shrink: 0;
            
            &:checked {
              background-color: $success-color;
              border-color: $success-color;
            }
            
            &:focus {
              outline: none;
              box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
            }
          }
          
          .specialization-name {
            font-size: 16px;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            overflow-wrap: break-word;
            font-weight: 600;
            color: $success-color;
            text-align: right;
            flex: 1;
            transition: all 0.3s ease;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            overflow-wrap: break-word;
          }
        }
        
        .dropdown-arrow {
          color: $success-color;
          font-size: 14px;
          transition: transform 0.3s ease;
          
          &.rotated {
            transform: rotate(180deg);
          }
        }
      }
      
      .specialization-dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        border: 0px solid transparent;
        border-top: none;
        border-radius: 0 0 12px 12px;
        background: white;
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.15);
        margin-top: -2px;
        max-height: 250px;
        overflow-y: auto;
        display: none;
        
        &.show {
          display: block;
          animation: dropdownSlideDown 0.3s ease;
        }
        
        .specialization-options {
          padding: 16px;
          
          .specialization-option {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 16px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            &:hover {
              border-color: $success-color;
              background: rgba(40, 167, 69, 0.05);
              transform: translateX(-4px);
            }
            
            input[type="checkbox"] {
              width: 18px;
              height: 18px;
              accent-color: $success-color;
              cursor: pointer;
              margin-top: 2px;
              flex-shrink: 0;
              border-radius: 4px;
              
              &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
              }
            }
            
            .option-text {
              color: #495057;
              font-weight: 500;
              text-align: right;
              flex: 1;
              line-height: 1.3;
              font-size: 13px;
              word-wrap: break-word;
              word-break: break-word;
              white-space: normal;
              overflow-wrap: break-word;
            }
            
            &:has(input:checked) {
              border-color: $success-color;
              background: rgba(40, 167, 69, 0.1);
              
              .option-text {
                color: $success-color;
                font-weight: 600;
              }
            }
          }
        }
        
        // Custom scrollbar
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: $success-color;
          border-radius: 3px;
          
          &:hover {
            background: darken($success-color, 10%);
          }
        }
      }
    }
  }
}

// Animation for dropdown
@keyframes dropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// RTL Support
:host-context(.rtl) {
  .specialization-dropdown-section {
    .specialization-dropdown-item {
      .dropdown {
        .specialization-dropdown-toggle {
          .dropdown-header-content {
            .specialization-name {
              text-align: right;
            }
          }
        }
        
        .specialization-dropdown-menu {
          .specialization-options {
            .specialization-option {
              text-align: right;
              
              &:hover {
                transform: translateX(4px);
              }
              
              .option-text {
                text-align: right;
              }
            }
          }
        }
      }
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .specialization-dropdown-section {
    .specialization-dropdown-item {
      .dropdown {
        .specialization-dropdown-toggle {
          padding: 12px 16px;
          min-height: 50px;
          
          .dropdown-header-content {
            gap: 8px;
            
            .specialization-name {
              font-size: 14px;
            }
          }
        }
        
        .specialization-dropdown-menu {
          .specialization-options {
            padding: 12px;
            
            .specialization-option {
              padding: 10px 12px;
              margin-bottom: 6px;
              
              .option-text {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}
:host-context(html[dir=rtl]),
:host-context(html[lang="ar"]),
:host-context(html[dir=ltr]),
:host-context(html[lang="en"])
{
  .broker-registration-stepper {
    direction: ltr;
    text-align: left;
  }
}
// Password input with eye icon
.password-input-container {
  position: relative;

  .form-control {
    padding-right: 50px; // Make space for the icon
  }

  .password-toggle-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
    z-index: 10;
    transition: color 0.2s ease;
    width:fit-content;

    &:hover {
      color: #495057;
    }
  }


:host-context(html[dir=rtl]),
:host-context(html[lang="ar"]){
    .password-toggle-icon {
      left: 15px ;
      text-align: left !important;
    }
  }
  :host-context(html[dir=ltr]),
  :host-context(html[lang="en"]){
    .password-toggle-icon {
      right: 15px ;
      text-align: right !important;
    }
  }
}

<!-- <PERSON>er -->
<div class="broker-pages">
  <div class="page-header bg-white rounded-3 shadow-sm mb-4 mb-md-6 p-3 p-sm-4 p-md-6">
    <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center justify-content-between gap-3">
      <div class="d-flex align-items-center w-100 w-md-auto">
        <div class="symbol symbol-40px symbol-sm-45px symbol-md-50px me-3 me-md-4 flex-shrink-0">
          <div class="symbol-label bg-light-primary">
            <i class="fas fa-bullhorn text-primary" style="font-size: 1.5rem;"></i>
          </div>
        </div>
        <div class="flex-grow-1">
          <h1 class="text-dark fw-bold mb-1 fs-4 fs-sm-3 fs-md-2">{{ 'BROKER.MY_ADVERTISEMENTS.TITLE' | translate }}
          </h1>
          <p
            class="text-muted mb-0 fs-7 fs-sm-6 d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-1 gap-sm-2">
            <span>{{ 'BROKER.MY_ADVERTISEMENTS.SUBTITLE' | translate }}</span>
            <span class="badge badge-light-primary fs-8 fs-sm-7">
              {{ this.page.totalElements || "0" }} {{ 'BROKER.MY_ADVERTISEMENTS.TOTAL' | translate }}
            </span>
          </p>
        </div>
      </div>

      <div class="d-flex align-items-center ">
        <button class="btn btn-primary btn-sm d-flex align-items-center" (click)="createNewAdvertisement()">
          <i class="fas fa-plus me-2"></i>
          <span class="d-none d-sm-inline">{{ 'BROKER.MY_ADVERTISEMENTS.ADD_NEW' | translate }}</span>
          <span class="d-inline d-sm-none">{{ 'BROKER.MY_ADVERTISEMENTS.ADD' | translate }}</span>
        </button>
      </div>
    </div>
  </div>

  <div class="tab-pane fade show active" id="kt_tab_pane_1">
    <!-- Empty State - No Advertisements -->
    <div *ngIf="rows.length === 0"
      class="empty-state-container d-flex flex-column align-items-center justify-content-center py-5 py-md-8 px-3">
      <div class="empty-state-content text-center">
        <!-- Icon -->
        <div class="empty-state-icon mb-4 mb-md-6">
          <div class="symbol symbol-80px symbol-md-100px mx-auto">
            <div class="symbol-label bg-light-primary">
              <i class="fas fa-bullhorn text-primary" style="font-size: 2.5rem;"></i>
            </div>
          </div>
        </div>

        <!-- Title and Description -->
        <div class="empty-state-text mb-4 mb-md-6">
          <h3 class="text-dark fw-bold mb-3 fs-5 fs-md-4">{{ 'BROKER.MY_ADVERTISEMENTS.NO_ADVERTISEMENTS_FOUND' |
            translate }}</h3>
          <p class="text-muted fs-7 fs-md-6 mb-0 mx-auto px-2" style="max-width: 400px; line-height: 1.5;">
            {{ 'BROKER.MY_ADVERTISEMENTS.NO_ADVERTISEMENTS_MESSAGE' | translate }}
          </p>
        </div>

        <!-- Action Button -->
        <div class="empty-state-action mb-4 mb-md-0">
          <button class="btn btn-primary btn-sm btn-md-lg d-flex align-items-center mx-auto"
            (click)="createNewAdvertisement()">
            <i class="fas fa-plus me-2"></i>
            <span class="d-none d-sm-inline">{{ 'BROKER.MY_ADVERTISEMENTS.CREATE_NEW_ADVERTISEMENT' | translate
              }}</span>
            <span class="d-inline d-sm-none">{{ 'BROKER.MY_ADVERTISEMENTS.CREATE' | translate }}</span>
          </button>
        </div>

        <!-- Decorative Elements -->
        <div class="empty-state-decoration mt-6 mt-md-8 d-none d-sm-block">
          <div class="d-flex justify-content-center gap-3 gap-md-4">
            <div class="decoration-item">
              <div class="symbol symbol-35px symbol-md-40px">
                <div class="symbol-label bg-light-success">
                  <i class="fas fa-home text-success"></i>
                </div>
              </div>
            </div>
            <div class="decoration-item">
              <div class="symbol symbol-35px symbol-md-40px">
                <div class="symbol-label bg-light-warning">
                  <i class="fas fa-key text-warning"></i>
                </div>
              </div>
            </div>
            <div class="decoration-item">
              <div class="symbol symbol-35px symbol-md-40px">
                <div class="symbol-label bg-light-info">
                  <i class="fas fa-chart-line text-info"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Advertisements Grid -->
    <div *ngIf="rows.length > 0" class="row g-3 g-md-4">
      <!-- Advertisement card for each post -->
      <div class="col-xxl-3 col-xl-4 col-lg-5 col-md-6 col-sm-6 col-12" *ngFor="let post of rows; let i = index">
        <div
          class="card h-100 border-0 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
          (click)="viewPropertyDetails(post)" role="button" tabindex="0">
          <!-- Image slider section -->
          <div class="position-relative">
            <div class="media-container cursor-pointer" (click)="openMediaModal(mediaModal, post, $event)"
              id="mediaContainer_{{ post.id }}">
              <img [src]="getImageFromGallery(post)" class="img-fluid rounded-top card-image"
                alt="{{ post.type }} image" />

              <!-- Video play icon overlay -->
              <div *ngIf="isCurrentItemVideo(post)"
                class="video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-30">
                <i class="bi bi-play-circle-fill text-white fs-2"></i>
              </div>

              <!-- Gallery icon overlay -->
              <div *ngIf="!isCurrentItemVideo(post)"
                class="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-20">
                <i class="bi bi-images text-white fs-3"></i>
              </div>
            </div>

            <!-- Image navigation controls -->
            <div *ngIf="hasMultipleImages(post)"
              class="image-navigation position-absolute top-50 start-0 w-100 d-flex justify-content-between px-1 px-sm-2"
              (click)="$event.stopPropagation()">
              <button class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue nav-btn"
                (click)="prevImage(post, $event)" aria-label="Previous image">
                <i class="bi bi-chevron-left"></i>
              </button>
              <button class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue nav-btn"
                (click)="nextImage(post, $event)" aria-label="Next image">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>

            <!-- Image counter -->
            <div *ngIf="hasMultipleImages(post)"
              class="image-counter position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-top-start fs-8 fs-sm-7"
              (click)="$event.stopPropagation()">
              {{ getCurrentImageIndex(post) + 1 }}/{{ getAllImagesFromGallery(post).length }}
            </div>
          </div>

          <!-- Card content -->
          <div class="card-body p-3 p-sm-4">
            <h5 class="fs-6 fs-sm-5 fw-bold text-dark mb-2 mb-sm-3 text-truncate cursor-pointer text-hover-dark-blue">
              {{ getTranslatedPropertyType(post.type) }}
            </h5>

            <!-- Property details -->
            <div class="d-flex flex-column flex-sm-row gap-2 gap-sm-3 mb-3 mb-sm-4">
              <div class="border border-gray-200 rounded p-2 p-sm-3 flex-grow-1">
                <div class="fs-7 fs-sm-6 fw-semibold text-dark">{{ getLocalizedAreaName(post.area).slice(0, 15) }}</div>
                <div class="fs-8 fs-sm-7 text-muted">{{ 'BROKER.MY_ADVERTISEMENTS.AREA' | translate }}</div>
              </div>
              <div class="border border-gray-200 rounded p-2 p-sm-3 flex-grow-1">
                <div class="fs-7 fs-sm-6 fw-semibold text-dark">{{ getLocalizedCityName(post.city).slice(0, 15) }}</div>
                <div class="fs-8 fs-sm-7 text-muted">{{ 'BROKER.MY_ADVERTISEMENTS.CITY' | translate }}</div>
              </div>
            </div>

            <!-- View button -->
            <a class="btn btn-sm btn-light-dark-blue fw-semibold w-100 d-flex align-items-center justify-content-center"
              (click)="viewPropertyDetails(post)" role="button" aria-label="View property details">
              <i class="fa fa-eye me-2"></i>
              <span>{{ 'BROKER.MY_ADVERTISEMENTS.VIEW' | translate }}</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination - Only show when there are advertisements -->
    <div *ngIf="rows.length > 0" class="mt-5">
      <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
        (pageChange)="onPageChange($event)">
      </app-pagination>
    </div>
  </div>

  <!-- Media Modal -->
  <ng-template #mediaModal let-modal>
    <div class="modal-header p-3 p-md-4">
      <h4 class="modal-title fs-6 fs-md-5">
        {{ selectedMediaType === "video" ? ('BROKER.MY_ADVERTISEMENTS.VIDEO' | translate) :
        ('BROKER.MY_ADVERTISEMENTS.IMAGE' | translate) }}
        <span *ngIf="hasMultipleModalMedia()" class="text-muted ms-2 fs-7 fs-md-6">
          ({{ currentModalIndex + 1 }}/{{ modalMediaItems.length }})
        </span>
      </h4>
      <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
    </div>
    <div class="modal-body position-relative p-2 p-md-3">
      <div class="text-center">
        <!-- Video player -->
        <div *ngIf="selectedMediaType === 'video' && selectedMediaUrl" class="video-container">
          <!-- Native video player -->
          <video [src]="selectedMediaUrl" controls autoplay class="w-100 modal-media" controlsList="nodownload"
            preload="auto"></video>

          <!-- Fallback message if video doesn't play -->
          <div class="mt-2 mt-md-3 text-muted small">
            <a [href]="selectedMediaUrl" target="_blank" class="text-decoration-none">
              <i class="bi bi-box-arrow-up-right me-1"></i>
              {{ 'BROKER.MY_ADVERTISEMENTS.OPEN_IN_NEW_TAB' | translate }}
            </a>
          </div>
        </div>

        <!-- Image viewer -->
        <img *ngIf="selectedMediaType === 'image' && selectedMediaUrl" [src]="selectedMediaUrl"
          class="img-fluid modal-media" alt="Property image" />
      </div>

      <!-- Navigation buttons for multiple media items - Desktop -->
      <div *ngIf="hasMultipleModalMedia()" class="modal-navigation d-none d-sm-block">
        <!-- Previous button -->
        <button class="btn btn-primary btn-icon position-absolute top-50 start-0 translate-middle-y ms-2 ms-md-3"
          (click)="prevModalMedia()" style="z-index: 1050">
          <i class="bi bi-chevron-left"></i>
        </button>

        <!-- Next button -->
        <button class="btn btn-primary btn-icon position-absolute top-50 end-0 translate-middle-y me-2 me-md-3"
          (click)="nextModalMedia()" style="z-index: 1050">
          <i class="bi bi-chevron-right"></i>
        </button>
      </div>

      <!-- Navigation buttons for multiple media items - Mobile -->
      <div *ngIf="hasMultipleModalMedia()" class="d-flex d-sm-none justify-content-between mt-3">
        <button class="btn btn-sm btn-primary" (click)="prevModalMedia()">
          <i class="bi bi-chevron-left me-1"></i>
          {{ 'BROKER.MY_ADVERTISEMENTS.PREVIOUS' | translate }}
        </button>
        <span class="align-self-center fw-bold fs-7">{{ currentModalIndex + 1 }} / {{ modalMediaItems.length }}</span>
        <button class="btn btn-sm btn-primary" (click)="nextModalMedia()">
          {{ 'BROKER.MY_ADVERTISEMENTS.NEXT' | translate }}
          <i class="bi bi-chevron-right ms-1"></i>
        </button>
      </div>
    </div>

    <!-- Modal footer with navigation dots - Desktop only -->
    <div *ngIf="hasMultipleModalMedia()" class="modal-footer justify-content-center d-none d-sm-flex">
      <div class="d-flex gap-2">
        <button *ngFor="let item of modalMediaItems; let i = index" class="btn btn-sm rounded-circle p-1"
          [class.btn-primary]="i === currentModalIndex" [class.btn-light]="i !== currentModalIndex"
          (click)="currentModalIndex = i; updateModalMedia()" style="width: 12px; height: 12px"></button>
      </div>
    </div>
  </ng-template>
</div>
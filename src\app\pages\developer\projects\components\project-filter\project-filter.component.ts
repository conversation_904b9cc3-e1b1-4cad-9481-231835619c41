import { ChangeDetectorRef, Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-project-filter',
  templateUrl: './project-filter.component.html',
  styleUrl: './project-filter.component.scss'
})
export class ProjectFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    managementTeam: '',
    projectExecuter: '',
  };

  constructor(
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {}

  apply() {
    this.filtersApplied.emit(this.filter);
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'MANAGEMENT_TEAM': 'فريق الإدارة',
        'PROJECT_EXECUTER': 'منفذ المشروع',
        'ENTER_MANAGEMENT_TEAM': 'أدخل فريق الإدارة',
        'ENTER_PROJECT_EXECUTER': 'أدخل اسم منفذ المشروع',
        'APPLY': 'تطبيق'
      },
      'en': {
        'MANAGEMENT_TEAM': 'Management Team',
        'PROJECT_EXECUTER': 'Project Executer',
        'ENTER_MANAGEMENT_TEAM': 'Enter management team',
        'ENTER_PROJECT_EXECUTER': 'Enter project executer name',
        'APPLY': 'Apply'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

}

// ===== RESPONSIVE DESIGN FOR BROKER DASHBOARD =====

// Main responsive layout
.broker-pages {
  padding: 0 15px;

  @media (min-width: 768px) {
    padding: 0 20px;
  }

  @media (min-width: 1024px) {
    padding: 0 25px;

    // Better spacing for large screens
    .row {
      margin-left: -10px;
      margin-right: -10px;

      > [class*="col-"] {
        padding-left: 10px;
        padding-right: 10px;
      }
    }

    // Optimize card spacing
    .card {
      margin-bottom: 1.5rem;
    }

    app-dashboard-card,
    app-new-request-card {
      .card {
        margin-bottom: 1.5rem;
      }
    }
  }

  @media (min-width: 1200px) {
    padding: 0 30px;

    .row {
      margin-left: -15px;
      margin-right: -15px;

      > [class*="col-"] {
        padding-left: 15px;
        padding-right: 15px;
      }
    }
  }
}

// ===== COMPREHENSIVE RESPONSIVE DESIGN =====

// Mobile first approach - all cards
.card {
  .card-header {
    // Default mobile layout
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
    padding: 1rem !important;

    .card-title {
      width: 100% !important;

      .card-label {
        font-size: 1rem !important;
        line-height: 1.4 !important;
        word-wrap: break-word !important;
      }

      .text-muted {
        font-size: 0.8rem !important;
        margin-top: 0.5rem !important;
      }
    }

    .card-toolbar {
      width: 100% !important;
      display: flex !important;
      justify-content: center !important;

      .btn {
        font-size: 0.85rem !important;
        padding: 0.6rem 1.2rem !important;
        white-space: nowrap !important;
        width: auto !important;
        min-width: 120px !important;
      }
    }
  }
}

// Small screens (576px+) - side by side layout
@media (min-width: 576px) {
  .card {
    .card-header {
      flex-direction: row !important;
      align-items: center !important;
      justify-content: space-between !important;
      padding: 1.25rem !important;

      .card-title {
        flex: 1 !important;
        margin-right: 1rem !important;

        .card-label {
          font-size: 1.1rem !important;
        }

        .text-muted {
          font-size: 0.85rem !important;
        }
      }

      .card-toolbar {
        width: auto !important;
        justify-content: flex-end !important;
        flex-shrink: 0 !important;

        .btn {
          font-size: 0.9rem !important;
          padding: 0.6rem 1.2rem !important;
          min-width: auto !important;
        }
      }
    }
  }
}

// Medium screens (768px+) - tablet optimization
@media (min-width: 768px) {
  .card {
    .card-header {
      padding: 1.5rem !important;

      .card-title {
        .card-label {
          font-size: 1.2rem !important;
        }

        .text-muted {
          font-size: 0.9rem !important;
        }
      }

      .card-toolbar {
        .btn {
          font-size: 0.9rem !important;
          padding: 0.7rem 1.4rem !important;
        }
      }
    }
  }
}

// Large screens (1024px+) - desktop optimization
@media (min-width: 1024px) {
  .card {
    .card-header {
      padding: 1.5rem 1.5rem 1.25rem 1.5rem !important;

      .card-title {
        .card-label {
          font-size: 1rem !important;
        }

        .text-muted {
          font-size: 0.8rem !important;
        }
      }

      .card-toolbar {
        .btn {
          font-size: 0.85rem !important;
          padding: 0.5rem 1rem !important;
        }
      }
    }
  }
}

// ===== MAIN DASHBOARD RESPONSIVE FIXES =====

// Mobile screens (up to 576px)
@media (max-width: 576px) {
  .broker-pages {
    padding: 0 10px !important;

    .row {
      margin-left: -5px !important;
      margin-right: -5px !important;

      > [class*="col-"] {
        padding-left: 5px !important;
        padding-right: 5px !important;
        margin-bottom: 1rem !important;
      }
    }

    // Statistics card mobile layout
    .card {
      .row.g-0 {
        .col-md-6 {
          margin-bottom: 1rem !important;

          &:last-child {
            margin-bottom: 0 !important;
          }

          // Force center alignment for card headers
          .card-header {
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            text-align: center !important;
            padding: 1rem !important;

            .card-title {
              text-align: center !important;
              width: 100% !important;

              .card-label {
                text-align: center !important;
                justify-content: center !important;

                &.d-flex.align-items-center {
                  justify-content: center !important;
                  text-align: center !important;

                  svg {
                    margin-right: 0.5rem !important;
                  }
                }
              }

              .text-muted {
                text-align: center !important;
              }
            }

            .card-toolbar {
              width: 100% !important;
              justify-content: center !important;
              margin-top: 1rem !important;
              margin-left: 0 !important;
            }
          }
        }
      }
    }

    // New requests section
    .row.mt-5 {
      margin-top: 1.5rem !important;
    }
  }
}

// Extra small screens (≤425px) - Force center everything
@media (max-width: 425px) {
  .broker-pages {
    padding: 0 8px !important;

    .card {
      .row.g-0 {
        .col-md-6 {
          .card-header {
            padding: 0.75rem !important;

            .card-title {
              .card-label {
                font-size: 0.9rem !important;

                &.d-flex.align-items-center {
                  flex-direction: column !important;
                  gap: 0.5rem !important;

                  svg {
                    margin-right: 0 !important;
                    margin-bottom: 0.25rem !important;
                  }
                }
              }

              .text-muted {
                font-size: 0.75rem !important;
              }
            }

            .card-toolbar {
              .btn {
                font-size: 0.8rem !important;
                padding: 0.4rem 0.8rem !important;
              }
            }
          }
        }
      }
    }
  }
}

// Small screens (576px to 768px)
@media (min-width: 576px) and (max-width: 768px) {
  .broker-pages {
    padding: 0 15px !important;

    .row {
      margin-left: -7.5px !important;
      margin-right: -7.5px !important;

      > [class*="col-"] {
        padding-left: 7.5px !important;
        padding-right: 7.5px !important;
      }
    }

    // Statistics card tablet layout
    .card {
      .row.g-0 {
        .col-md-6 {
          margin-bottom: 0.75rem !important;

          &:last-child {
            margin-bottom: 0 !important;
          }
        }
      }
    }
  }
}

// Medium screens (768px to 1024px) - Tablet
@media (min-width: 768px) and (max-width: 1024px) {
  .broker-pages {
    padding: 0 20px !important;

    .row {
      margin-left: -10px !important;
      margin-right: -10px !important;

      > [class*="col-"] {
        padding-left: 10px !important;
        padding-right: 10px !important;
      }
    }

    // Better spacing for tablet
    .col-md-6 {
      &:first-child {
        // Left column spacing
        .row {
          margin-left: -5px !important;
          margin-right: -5px !important;

          .col-md-6 {
            padding-left: 5px !important;
            padding-right: 5px !important;
          }
        }
      }
    }

    // Statistics card tablet optimization
    .card {
      .row.g-0 {
        .col-md-6 {
          padding: 0.5rem !important;
        }
      }
    }
  }
}

// Filter section responsive
.card-header {
  .card-toolbar {
    @media (max-width: 425px) {
      .btn-icon {
        padding: 0.5rem !important;
        font-size: 0.9rem !important;
      }
    }
  }
}

// RTL Support for Broker Dashboard
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .card {
    .card-header {
      text-align: right;

      @media (max-width: 425px) {
        text-align: center !important;
      }

      .card-title {
        text-align: right;

        @media (max-width: 425px) {
          text-align: center !important;
        }

        .card-label {
          text-align: right;
          font-family: 'Noto Kufi Arabic', sans-serif !important;

          @media (max-width: 425px) {
            text-align: center !important;
          }
        }

        .text-muted {
          font-family: 'Markazi Text', sans-serif !important;

          @media (max-width: 425px) {
            text-align: center !important;
          }
        }
      }

      .card-toolbar {
        margin-left: 0;
        margin-right: auto;

        @media (max-width: 425px) {
          margin: 0 auto !important;
        }

        .btn {
          font-family: 'Markazi Text', sans-serif !important;
        }
      }
    }

    .card-body {
      text-align: right;

      @media (max-width: 425px) {
        text-align: center !important;
      }
    }
  }

  .row {
    .col-md-6 {
      text-align: right;

      @media (max-width: 425px) {
        text-align: center !important;
      }
    }
  }

  .btn {
    .fa-solid {
      margin-left: 0.5rem;
      margin-right: 0;

      // Prevent icon flipping in Arabic
      &.fa-angles-right,
      &.fa-angle-right,
      &.fa-arrow-right,
      &.fa-chevron-right {
        transform: none !important;
        -webkit-transform: none !important;
      }
    }
  }

  // Global fix for all icons in Arabic
  i, svg {
    &[class*="angle"],
    &[class*="arrow"],
    &[class*="chevron"] {
      transform: none !important;
      -webkit-transform: none !important;
    }
  }

  .text-muted {
    text-align: right;

    @media (max-width: 425px) {
      text-align: center !important;
    }
  }

  .fw-bold {
    text-align: right;

    @media (max-width: 425px) {
      text-align: center !important;
    }
  }
}

// ===== FIX STATISTICS CARD FLEX WRAP =====

// Fix the main statistics card layout
.card {
  .card-header {
    flex-wrap: nowrap !important;

    // Ensure title takes available space
    .card-title {
      flex: 1 !important;
      min-width: 0 !important;
    }

    // Keep toolbar fixed size
    .card-toolbar {
      flex-shrink: 0 !important;
    }
  }

  // Fix the two-column layout inside statistics card
  .row.g-0 {
    .col-md-6 {
      .card-header {
        flex-wrap: nowrap !important;

        .card-title {
          flex: 1 !important;
          min-width: 0 !important;
        }

        .card-toolbar {
          flex-shrink: 0 !important;
          margin-left: 1rem !important;
        }
      }
    }
  }
}

// Arabic specific fixes for statistics card
:host-context(html[lang="ar"]) {
  .card {
    .card-header {
      flex-wrap: nowrap !important;

      .card-title {
        .card-label {
          &.d-flex.align-items-center {
            flex-direction: row-reverse !important;

            // Hide only the statistics SVG in Arabic
            svg {
              display: none !important;
            }
          }
        }
      }

      .card-toolbar {
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }
    }
  }

  // Mobile screens - Arabic center layout
  @media (max-width: 576px) {
    .card {
      .row.g-0 {
        .col-md-6 {
          .card-header {
            flex-direction: column !important;
            align-items: center !important;
            text-align: center !important;

            .card-title {
              text-align: center !important;

              .card-label {
                text-align: center !important;
                justify-content: center !important;

                &.d-flex.align-items-center {
                  justify-content: center !important;
                  text-align: center !important;
                  flex-direction: column !important;

                  svg {
                    display: none !important;
                  }
                }
              }

              .text-muted {
                text-align: center !important;
              }
            }

            .card-toolbar {
              margin-left: 0 !important;
              margin-right: 0 !important;
              justify-content: center !important;
            }
          }
        }
      }
    }
  }

  // Extra small screens - Arabic center layout
  @media (max-width: 425px) {
    .card {
      .row.g-0 {
        .col-md-6 {
          .card-header {
            .card-title {
              .card-label {
                &.d-flex.align-items-center {
                  flex-direction: column !important;
                  gap: 0.5rem !important;

                  svg {
                    display: none !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

    .row.g-0 {
      .col-md-6 {
        .card-header {
          flex-wrap: nowrap !important;

          .card-title {
            .card-label {
              &.d-flex.align-items-center {
                flex-direction: row-reverse !important;

                svg {
                  margin-right: 0 !important;
                  margin-left: 0.5rem !important;
                }
              }
            }
          }

          .card-toolbar {
            margin-left: 0 !important;
            margin-right: 1rem !important;
          }
        }
      }
    }
  }

// ===== LARGE SCREENS OPTIMIZATION (1024px+) =====

@media (min-width: 1024px) and (max-width: 1199px) {
  .broker-pages {
    // Force full width layout for 1024px screens
    .row {
      &:first-child {
        // Main dashboard row
        .col-12.col-lg-6 {
          // Force both columns to take full width
          flex: 0 0 100% !important;
          max-width: 100% !important;
          margin-bottom: 2rem !important;

          &:first-child {
            // Dashboard cards section - full width
            app-new-request-card,
            app-dashboard-card {
              .card {
                margin-bottom: 1.25rem;

                .card-header {
                  padding: 1.5rem 1.5rem 1.25rem 1.5rem;

                  .card-title {
                    .card-label {
                      font-size: 1rem !important;
                    }

                    .text-muted {
                      font-size: 0.8rem !important;
                    }
                  }

                  .card-toolbar {
                    .btn {
                      padding: 0.5rem 1rem;
                      font-size: 0.85rem;
                    }
                  }
                }
              }
            }

            // Nested row for developers and maps
            .row {
              margin-left: -0.75rem;
              margin-right: -0.75rem;

              .col-md-6 {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
              }
            }
          }

          &:last-child {
            // Statistics and charts section - full width
            .card {
              .row.g-0 {
                .col-md-6 {
                  &:first-child {
                    // Statistics section
                    .card-header {
                      padding: 1.25rem 1rem 0.75rem 1rem;
                    }

                    .card-body {
                      padding: 0.75rem 1rem 1rem 1rem;
                    }
                  }

                  &:last-child {
                    // Filter and chart section
                    .card-header {
                      padding: 1.25rem 1rem 0.75rem 1rem;

                      .card-toolbar {
                        position: relative;

                        app-specializations-filter {
                          // Ensure proper positioning
                          position: absolute;
                          top: 100%;
                          right: 0;
                          z-index: 1050;
                        }
                      }
                    }

                    .card-body {
                      padding: 0.75rem 1rem 1.25rem 1rem;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // New requests section - full width
    .row.mt-5 {
      margin-top: 2rem !important;

      .col-12 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
      }

      app-new-requests {
        .card {
          .card-header {
            padding: 1.5rem 1.5rem 1.25rem 1.5rem;
          }

          .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;

            .table {
              th {
                padding: 1rem 0.75rem;
                font-size: 0.9rem;
              }

              td {
                padding: 1rem 0.75rem;
                font-size: 0.85rem;
              }
            }
          }
        }
      }
    }
  }
}

// Extra large screens (1200px+) - restore side by side layout
@media (min-width: 1200px) {
  .broker-pages {
    .row {
      &:first-child {
        .col-12.col-lg-6 {
          // Restore normal column behavior for large screens
          flex: 0 0 50% !important;
          max-width: 50% !important;
          margin-bottom: 0 !important;

          &:first-child {
            padding-right: 1rem;

            app-new-request-card,
            app-dashboard-card {
              .card {
                margin-bottom: 1.25rem;

                .card-header {
                  padding: 1.5rem 1.5rem 1.25rem 1.5rem;
                }
              }
            }

            // Nested row for developers and maps
            .row {
              margin-left: -0.5rem;
              margin-right: -0.5rem;

              .col-md-6 {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
              }
            }
          }

          &:last-child {
            padding-left: 1rem;

            .card {
              .row.g-0 {
                .col-md-6 {
                  &:first-child {
                    .card-header {
                      padding: 1.25rem 1rem 0.75rem 1rem;
                    }

                    .card-body {
                      padding: 0.75rem 1rem 1rem 1rem;
                    }
                  }

                  &:last-child {
                    .card-header {
                      padding: 1.25rem 1rem 0.75rem 1rem;

                      .card-toolbar {
                        position: relative;

                        app-specializations-filter {
                          position: absolute;
                          top: 100%;
                          right: 0;
                          z-index: 1050;
                        }
                      }
                    }

                    .card-body {
                      padding: 0.75rem 1rem 1.25rem 1rem;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .row.mt-5 {
      margin-top: 2rem !important;

      app-new-requests {
        .card {
          .card-header {
            padding: 1.5rem 1.5rem 1.25rem 1.5rem;
          }

          .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;

            .table {
              th {
                padding: 1rem 0.75rem;
                font-size: 0.9rem;
              }

              td {
                padding: 1rem 0.75rem;
                font-size: 0.85rem;
              }
            }
          }
        }
      }
    }
  }
}

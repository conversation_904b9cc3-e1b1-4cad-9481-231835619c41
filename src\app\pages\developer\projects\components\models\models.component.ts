import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { ProjectsService } from '../../../services/projects.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { TranslationService } from 'src/app/modules/i18n';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-models',
  templateUrl: './models.component.html',
  styleUrl: './models.component.scss',
})
export class ModelsComponent extends BaseGridComponent implements OnInit {
  user: any  ;

  projectId: number | null = null;

  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private projectsService: ProjectsService,
    private route: ActivatedRoute,
    private router: Router,
    public translationService: TranslationService
  ) {
    super(cd);
    // this.setService(ProjectsService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit(): void {
    super.ngOnInit();

    // Get user from localStorage
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.route.queryParams.subscribe((params) => {
      this.projectId = params['projectId'] ? +params['projectId'] : null;
      console.log('Project ID:', this.projectId);
      console.log('User role:', this.user?.role);
      this.page.filters = { ...this.page.filters, projectId: this.projectId };
      this.reloadTable(this.page);
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout
    this.searchTimeout = setTimeout(() => {
      this.page.filters = { ...this.page.filters, searchCode: value.trim() };
      console.log(this.page.filters);
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = { ...this.page.filters, ...filters };

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;
    this.loading = true;
    await this.projectsService.getAllModels(this.page).subscribe(
      (response: any) => {
        console.log(response.data);
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        // Update empty card visibility
        this.updateEmptyCardVisibility();

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.rows = [];
        this.updateEmptyCardVisibility();
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    );
  }

  updateEmptyCardVisibility() {
    this.showEmptyCard = this.rows.length === 0;
  }

  downloadModel() {
    this.projectsService.downloadModel(this.projectId).subscribe({
      next: (blob: Blob) => {
        saveAs(blob, 'models-template.xlsx');
      },
      error: (err) => {
        console.error('Download error:', err);
        Swal.fire(
          'Error',
          'Failed to download template. Please try again.',
          'error'
        );
      },
    });
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      console.log('File selected:', file.name);
      this.handleFileUpload(file);
    }
  }

  handleFileUpload(file: File) {
    console.log('Uploading file:', file.name);
    this.projectsService.uploadModel(file, this.projectId).subscribe({
      next: (response) => {
        Swal.fire('Success', 'Models uploaded successfully!', 'success').then(
          () => {
            this.showEmptyCard = false;
            this.page.filters = {
              ...this.page.filters,
              projectId: this.projectId,
            };
            this.reloadTable(this.page);
          }
        );
      },
      error: (error) => {
        Swal.fire(
          'Error',
          'Failed to upload models. Please try again.',
          'error'
        );
      },
    });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'MODELS': 'النماذج',
        'SEARCH_BY_CODE': 'البحث بالكود..',
        'FILTER': 'تصفية',
        'DOWNLOAD_EXCEL': 'تحميل إكسل',
        'UPLOAD_MODEL_EXCEL_SHEET': 'رفع ملف إكسل النماذج',
        'UPLOAD_MODELS': 'رفع النماذج',
        'MODEL_CODE': 'كود النموذج',
        'NO_OF_ROOMS': 'عدد الغرف',
        'NO_OF_BATHROOMS': 'عدد الحمامات',
        'ACTIONS': 'الإجراءات',
        'VIEW_UNITS': 'عرض الوحدات',
        'NO_MODELS_FOUND': 'لا توجد نماذج',
        'LOADING': 'جاري التحميل...',
        'DATE' : 'التاريخ',
        'NUMBER_OF_UNITS': 'عدد الوحدات',
        'UNITS': 'الوحدات',
        'AREA': 'المنطقة',
        'ROOMS' : 'عدد الغرف',
        'BATHROOMS' : 'عدد الحمامات',
        'UNIT_DETAILS' : 'عرض تفاصيل الوحدة',
        'LANDING_AREA' : 'مساحة الارض'
      },
      'en': {
        'MODELS': 'Models',
        'SEARCH_BY_CODE': 'Search By Code..',
        'FILTER': 'Filter',
        'DOWNLOAD_EXCEL': 'Download Excel',
        'UPLOAD_MODEL_EXCEL_SHEET': 'Upload Model Excel Sheet',
        'UPLOAD_MODELS': 'Upload Models',
        'MODEL_CODE': 'Model Code',
        'NO_OF_ROOMS': 'No of Rooms',
        'NO_OF_BATHROOMS': 'No of Bathrooms',
        'ACTIONS': 'Actions',
        'VIEW_UNITS': 'View Units',
        'NO_MODELS_FOUND': 'No models found',
        'LOADING': 'Loading...',
        'DATE' : 'Date',
        'NUMBER_OF_UNITS': 'Number of Units',
        'UNITS': 'Units',
        'AREA': 'Area',
        'ROOMS' : 'Number of Rooms',
        'BATHROOMS' : 'Number of Bathrooms',
        'UNIT_DETAILS' : 'Unit Details',
        'LANDING_AREA' : 'Landing Area'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  orderBy: string = '';
  orderDir: string = 'asc';

  sortData(column: string) {
    if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

    this.rows.sort((a, b) => {
      const valA = a[column];
      const valB = b[column];
      
      if (this.orderDir === 'asc') {
        return valA > valB ? 1 : -1;
      } else {
        return valA < valB ? 1 : -1;
      }
    });
  }

  getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }
}

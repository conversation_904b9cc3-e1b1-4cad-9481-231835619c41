// All Developers component styles
.table {
  th {
    font-weight: 600;
    color: #0D47A1;
    border-bottom: 2px solid #f1f1f1;
  }

  td {
    vertical-align: middle;
    border-bottom: 1px solid #f1f1f1;
  }
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-header {
    .d-flex {
      flex-direction: row-reverse;
    }
  }

  .table {
    th, td {
      text-align: right;
    }

    .form-check {
      text-align: center;
    }
  }

  .d-flex {
    &.justify-content-between {
      flex-direction: row-reverse;
    }
  }
}

// Custom margin for page title
.page-title-custom {
  margin-top: 5px !important;
}

// Enhanced Arabic RTL support
:host-context(html[lang="ar"]) {
  // Custom title styling
  .page-title-custom {
    margin-top: 5px !important;
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    color: #0d47a1 !important;
  }

  // Override global CSS that affects flex elements
  .d-flex.justify-content-between {
    justify-content: space-between !important;
    text-align: right !important;
    // direction: rtl !important;

    &.align-items-center {
      justify-content: space-between !important;
    }

    .d-flex.my-4 {
      justify-content: flex-start !important;
      text-align: right !important;
      direction: rtl !important;

      &:first-child {
        justify-content: flex-start !important;
      }

      &:nth-child(2) {
        justify-content: center !important;
      }

      &:last-child {
        justify-content: flex-end !important;
      }
    }

    .d-flex.align-items-center {
      justify-content: flex-end !important;
      gap: 1rem !important;
    }
  }
  .card {
    direction: rtl !important;
    text-align: right !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e5e7eb !important;
  }

  .card-header {
    direction: rtl !important;
    padding: 1.5rem !important;

    .d-flex.justify-content-between {
      flex-direction: row-reverse !important;
      gap: 1rem !important;
      align-items: center !important;

      h3 {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        font-size: 1.8rem !important;
        font-weight: 800 !important;
        color: #1e3a8a !important;
        margin: 0 !important;
      }

      .d-flex.align-items-center {
        gap: 1rem !important;

        .position-relative {
          .form-control {
            font-family: 'Hacen Liner Screen', sans-serif !important;
            text-align: right !important;
            direction: rtl !important;
            border-radius: 25px !important;
            padding: 0.75rem 3rem 0.75rem 1rem !important;
            border: 2px solid #e5e7eb !important;
            background: #f8fafc !important;

            &:focus {
              border-color: #1e3a8a !important;
              box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
              background: white !important;
            }

            &::placeholder {
              color: #9ca3af !important;
              font-family: 'Hacen Liner Screen', sans-serif !important;
            }
          }

          .position-absolute {
            right: 1rem !important;
            left: auto !important;
            color: #6b7280 !important;
          }
        }

        .btn {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          font-weight: 600 !important;
          border-radius: 8px !important;
          padding: 0.6rem 1.2rem !important;
          transition: all 0.3s ease !important;

          &.btn-dark-blue {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
            border: none !important;
            color: white !important;

            &:hover {
              background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
              transform: translateY(-1px) !important;
              box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
            }
          }

          .fa-solid {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
          }
        }
      }
    }
  }

  .table-responsive {
    direction: rtl !important;
    border-radius: 12px !important;
    overflow: auto !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;


    .table {
      margin-bottom: 0 !important;
      direction: rtl !important;

      thead {
        tr {
          // background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;

          th {
            text-align: center !important;
            font-family: 'Noto Kufi Arabic', sans-serif !important;
            font-size: 1rem !important;
            font-weight: 700 !important;
            color: #1e3a8a !important;
            padding: 1.2rem 0.75rem !important;
            border-bottom: 2px solid #cbd5e1 !important;
            vertical-align: middle !important;
            white-space: nowrap !important;

            &:first-child {
              text-align: right !important;
              padding-right: 1.5rem !important;
              border-top-right-radius: 12px !important;
            }

            &:last-child {
              text-align: left !important;
              padding-left: 1.5rem !important;
              border-top-left-radius: 12px !important;
            }
          }
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid #f1f5f9 !important;
          transition: all 0.2s ease !important;

          &:hover {
            background: rgba(30, 58, 138, 0.03) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
          }

          &:last-child {
            border-bottom: none !important;
          }

          td {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
            padding: 1.2rem 0.75rem !important;
            vertical-align: middle !important;
            border-bottom: none !important;

            &:first-child {
              text-align: right !important;
              padding-right: 1.5rem !important;
            }

            &:last-child {
              text-align: left !important;
                padding-right: 3.5rem !important;
            }

            // Developer info styling
            .d-flex.align-items-center {
              justify-content: flex-start !important;
              gap: 1rem !important;

              .symbol {
                flex-shrink: 0 !important;
                margin-right: 0 !important;
                margin-left: 1rem !important;

                img {
                  border-radius: 8px !important;
                  border: 2px solid #e5e7eb !important;
                  object-fit: cover !important;
                }

                .symbol-label {
                  border-radius: 8px !important;
                  background: rgba(30, 58, 138, 0.1) !important;
                  color: #1e3a8a !important;
                  font-family: 'Noto Kufi Arabic', sans-serif !important;
                  font-weight: 700 !important;
                }
              }

              .d-flex.justify-content-start.flex-column {
                text-align: right !important;
                flex: 1 !important;

                a {
                  font-family: 'Noto Kufi Arabic', sans-serif !important;
                  font-size: 1rem !important;
                  font-weight: 600 !important;
                  color: #1f2937 !important;
                  text-decoration: none !important;
                  margin-bottom: 0.25rem !important;

                  &:hover {
                    color: #1e3a8a !important;
                  }
                }

                .text-muted {
                  font-family: 'Hacen Liner Screen', sans-serif !important;
                  font-size: 0.85rem !important;
                  color: #6b7280 !important;
                }
              }
            }

            // Badge styling
            .badge {
              font-family: 'Hacen Liner Screen', sans-serif !important;
              font-size: 0.8rem !important;
              font-weight: 600 !important;
              padding: 0.4rem 0.8rem !important;
              border-radius: 6px !important;
            }

            // Action buttons
            .btn {
              border-radius: 6px !important;
              transition: all 0.2s ease !important;
              font-family: 'Hacen Liner Screen', sans-serif !important;

              &:hover {
                transform: scale(1.1) !important;
              }
            }
          }
        }
      }
    }
  }

  // Pagination improvements
  .d-flex.justify-content-center {
    margin-top: 1.5rem !important;
    margin-bottom: 1rem !important;
  }
}

.rtl-table {
  .table {
    th:first-child {
      border-radius: 0 0.375rem 0.375rem 0;
    }

    th:last-child {
      border-radius: 0.375rem 0 0 0.375rem;
    }
  }
}

// Arabic Fonts Support
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.arabic-font {
  font-family: 'Noto Kufi Arabic', sans-serif;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif;
  line-height: 1.8;
}

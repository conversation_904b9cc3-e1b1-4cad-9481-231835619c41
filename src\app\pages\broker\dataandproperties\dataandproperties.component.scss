// Simple filter styling
.form-select {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
}

.form-label {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.btn-light-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  transition: all 0.15s ease-in-out;

  &:hover {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
    color: #dc3545;
  }
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.15s ease-in-out;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    background: #6c757d;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  i {
    font-size: 1rem;
  }
}

.btn-outline-danger {
  border: 2px solid #dc3545;
  color: #ffffff;
  background: rgba(47, 175, 151, 0.414);
  transition: all 0.15s ease-in-out;
  border-radius: 0.5rem;

  &:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
  }

  i {
    font-size: 1rem;
  }
}

// Card styling
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;

  .card-body {
    padding: 1.5rem;
  }
}

// Text colors
.text-dark-blue {
  color: #2d3748 !important;
}


// Responsive adjustments for filter buttons
@media (max-width: 1200px) {
  .d-flex.gap-2.flex-wrap {
    .btn {
      font-size: 0.8rem !important;
      padding: 0.4rem 0.6rem !important;
      min-width: 80px !important;
      max-width: 120px !important;
    }
  }
}

@media (max-width: 992px) {
  .d-flex.gap-2.flex-wrap {
    .btn {
      font-size: 0.75rem !important;
      padding: 0.375rem 0.5rem !important;
      min-width: 70px !important;
      max-width: 100px !important;
    }
  }
}

@media (max-width: 768px) {
  .filter-buttons {
    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 0.75rem;
    }
  }

  .d-flex.gap-2.flex-wrap {
    flex-direction: column !important;

    .btn {
      font-size: 0.8rem !important;
      padding: 0.5rem 1rem !important;
      min-width: 100% !important;
      max-width: 100% !important;
      margin-bottom: 0.5rem;
    }
  }
}

/* Data and Properties Page RTL Support */

// RTL Support for Data and Properties Page
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {

  // Header section
  .d-flex.flex-column.flex-lg-row {
    &.justify-content-between {
      flex-direction: row-reverse;

      .flex-shrink-0 {
        text-align: right;

        h1 {
          text-align: right;
        }

        p {
          // text-align: right;
        }
      }
    }
  }

  // Search section
  .position-relative {
    .form-control {
      padding-right: 3rem;
      padding-left: 0.75rem;
      text-align: right;
    }
  }

  // Action buttons
  .d-flex.flex-column.flex-sm-row {
    flex-direction: row-reverse;

    .btn {
      i {
        margin-right: 0;
        margin-left: 0.5rem;
      }
    }
  }

  // Filter section
  .row.g-3.align-items-end {
    text-align: right;

    .form-label {
      text-align: right;
    }

    .form-select {
      text-align: right;
    }

    .d-flex.h-100.align-items-end.gap-2 {
      flex-direction: row-reverse;

      .btn {
        i {
          margin-right: 0;
          margin-left: 0.5rem;
        }
      }
    }
  }

  // Loading state
  .d-flex.justify-content-center {
    .spinner-border {
      margin: 0 auto;
    }
  }
}

// Enhanced styling for Arabic
:host-context(html[lang="ar"]) {
  .btn {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 1rem;

    &.btn-sm {
      font-size: 0.85rem;
      padding: 0.375rem 0.75rem;
    }
  }

  .form-control {
    font-size: 0.9rem;
  }

  .form-label {
    font-size: 0.9rem;
    font-weight: 600;
  }

  .form-select {
    font-size: 0.9rem;
  }

  h1 {
    font-size: 1.8rem;
    font-weight: 700;
  }

  p {
    font-size: 0.95rem;
  }
}

// Responsive adjustments for Arabic
@media (max-width: 768px) {
  :host-context(html[lang="ar"]) {
    .btn {
      font-size: 0.8rem;
      padding: 0.375rem 0.75rem;

      &.btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }
    }

    h1 {
      font-size: 1.5rem;
    }

    .form-control,
    .form-select {
      font-size: 0.85rem;
    }
  }
}

// Fix for medium screens (768px - 1199px)
@media (max-width: 1199px) and (min-width: 768px) {
  .d-flex.flex-column.flex-xl-row {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1.5rem !important;

    .flex-shrink-0,
    .flex-grow-1 {
      order: unset !important;
      max-width: 100% !important;
      margin: 0 !important;
    }

    // Title section
    .flex-shrink-0:first-child {
      text-align: center !important;
      margin-bottom: 1rem;
    }

    // Search section
    .flex-grow-1 {
      max-width: 100% !important;
      margin: 0 !important;
    }

    // Buttons section
    .flex-shrink-0:last-child {
      .d-flex.flex-column.flex-sm-row {
        justify-content: center !important;
        gap: 0.75rem !important;
        flex-wrap: wrap !important;

        .btn {
          min-width: 120px;
          justify-content: center;
          flex: 0 0 auto;
        }
      }
    }
  }
}

// Fix for 524px responsive breakpoint
@media (max-width: 576px) {
  .d-flex.flex-column.flex-xl-row {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;

    .flex-shrink-0,
    .flex-grow-1 {
      order: unset !important;
      max-width: 100% !important;
      margin: 0 !important;
    }

    // Title section
    .flex-shrink-0:first-child {
      text-align: center !important;
      margin-bottom: 1rem;
    }

    // Search section
    .flex-grow-1 {
      max-width: 100% !important;
      margin: 0 !important;
    }

    // Buttons section
    .flex-shrink-0:last-child {
      .d-flex.flex-column.flex-sm-row {
        flex-direction: column !important;
        gap: 0.5rem !important;

        .btn {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}

// Fix for dropdown menu in small screens
@media (max-width: 320px) {
  .dropdown-menu.show.p-3.shadow-lg.border-0.rounded-3 {
    min-width: 240px !important;
  }
}

// Additional fix for very small screens
@media (max-width: 480px) {
  .d-flex.flex-column.flex-sm-row {
    flex-direction: column !important;

    .btn {
      width: 100% !important;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {

  // Sample users data
  users = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Super Admin',
      status: 'Active',
      lastLogin: '2024-01-15 10:30 AM',
      avatar: 'assets/media/avatars/300-1.jpg'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Admin',
      status: 'Active',
      lastLogin: '2024-01-14 02:15 PM',
      avatar: 'assets/media/avatars/300-2.jpg'
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Broker',
      status: 'Inactive',
      lastLogin: '2024-01-10 09:45 AM',
      avatar: 'assets/media/avatars/300-3.jpg'
    },
    {
      id: 4,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'Developer',
      status: 'Active',
      lastLogin: '2024-01-15 11:20 AM',
      avatar: 'assets/media/avatars/300-4.jpg'
    },
    {
      id: 5,
      name: 'Khaled Ibrahim',
      email: '<EMAIL>',
      role: 'Client',
      status: 'Active',
      lastLogin: '2024-01-13 04:30 PM',
      avatar: 'assets/media/avatars/300-5.jpg'
    }
  ];

  constructor(private router: Router, public translationService: TranslationService) { }

  ngOnInit(): void {
  }

  addUser(): void {
    // Navigate to add user form or open modal
    console.log('Add new user');
  }

  editUser(id: number): void {
    console.log('Edit user:', id);
  }

  deleteUser(id: number): void {
    const confirmMessage = this.translationService.isRTL() ?
      'هل أنت متأكد من حذف هذا المستخدم؟' :
      'Are you sure you want to delete this user?';

    if (confirm(confirmMessage)) {
      this.users = this.users.filter(user => user.id !== id);
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'badge-light-success';
      case 'inactive':
        return 'badge-light-danger';
      case 'pending':
        return 'badge-light-warning';
      default:
        return 'badge-light-secondary';
    }
  }

  getRoleBadgeClass(role: string): string {
    switch (role.toLowerCase()) {
      case 'super admin':
        return 'badge-light-danger';
      case 'admin':
        return 'badge-light-primary';
      case 'broker':
        return 'badge-light-success';
      case 'developer':
        return 'badge-light-info';
      case 'client':
        return 'badge-light-secondary';
      default:
        return 'badge-light-secondary';
    }
  }

  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}

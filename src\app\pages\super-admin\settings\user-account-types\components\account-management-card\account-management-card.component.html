<div class="card-body p-10 d-flex flex-column flex-center text-center" (click)="onCardClick()">
  <div class="mb-7">
    <div class="symbol symbol-circle symbol-100px">
      <div class="symbol-label bg-light-dark-blue">
        <i *ngIf="isFontAwesome" [class]="faIcon + ' fs-3x text-dark-blue'"></i>
      </div>
    </div>
  </div>

  <div class="text-center" [style.text-align]="translationService.isRTL() ? 'right' : 'center'">
    <h3 class="fs-2 fw-bold text-gray-900 mb-4"
      [style.font-family]="translationService.isRTL() ? 'Noto Ku<PERSON> Arabic, sans-serif' : 'inherit'"
      [style.font-size]="translationService.isRTL() ? '1.6rem' : 'inherit'">
      {{ title | translate }}
    </h3>
    <p class="fs-6 fw-semibold text-gray-600 mb-6"
      [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
      [style.font-size]="translationService.isRTL() ? '1rem' : 'inherit'">
      {{ description | translate }}
    </p>
  </div>
</div>
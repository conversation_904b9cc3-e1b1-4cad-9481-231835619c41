import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UsersService } from '../../services/users.service';
import { TranslationService } from '../../../../modules/i18n';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss']
})
export class UserDetailsComponent implements OnInit {
  user: any = null;

  userId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private usersService: UsersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.userId = params['userId'];
      if (this.userId) {
        this.loadUserDetails();
      } else {
        this.router.navigate(['/super-admin/all-users']);
      }
    });
  }

  loadUserDetails(): void {
    this.usersService.getUserById(this.userId).subscribe({
      next: (response) => {
        console.log('User details:', response);
        this.user = response.data;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading user details:', error);
        Swal.fire('Error', 'Failed to load user details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-users']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-users']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }

  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'SUSPEND': 'تعليق',
        'ACTIVATE': 'تنشيط',
        'SUSPEND_USER_ACCOUNT': 'هل تريد تعليق حساب هذا المستخدم ؟',
        'ACTIVATE_USER_ACCOUNT': 'هل تريد تفعيل حساب هذا المستخدم ؟',
        'ARE_YOU_SURE': 'هل انت متأكد ؟',
        'YES': 'نعم',
        'CANCEL': 'الغاء',
        'SUCCESS': 'تم بنجاح',
        'Error': 'خطأ',
        'USER_ACCOUNT_HAS_BEEN': 'حساب المستخدم تم ',
        'ERROR_TOGGLING_STATUS': 'خطأ عند تغيير الحالة',
        'ERROR_TOGGLING_STATUS_UPDATING': 'خطأ عند تغيير الحالة ، حاول مجدداً',
      },
      'en': {
        'SUSPEND': 'Suspend',
        'ACTIVATE': 'Activate',
        'SUSPEND_USER_ACCOUNT': 'Suspend This User Account ?',
        'ACTIVATE_USER_ACCOUNT': 'Activate This User Account ?',
        'ARE_YOU_SURE': 'Are You Sure?',
        'YES': 'Yes',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success',
        'Error': 'Error',
        'USER_ACCOUNT_HAS_BEEN': 'User Account Has Been',
        'ERROR_TOGGLING_STATUS': 'Error Toggling Status: ',
        'ERROR_TOGGLING_STATUS_UPDATING': 'Failed to update User status. Please try again.',
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  toggleUserStatus(): void {
    if (!this.user) return;

    const action = this.user.isActive ? this.getTranslatedText('SUSPEND') : this.getTranslatedText('ACTIVATE');
    const message = this.user.isActive ? this.getTranslatedText('SUSPEND_USER_ACCOUNT') : this.getTranslatedText('ACTIVATE_USER_ACCOUNT');

    Swal.fire({
      title: this.getTranslatedText('ARE_YOU_SURE'),
      text: `${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      cancelButtonText: this.getTranslatedText('CANCEL'),
      confirmButtonText: this.getTranslatedText('YES') + `, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.usersService.toggleUserStatus(this.user.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.user.isActive = !this.user.isActive;
            Swal.fire(
              this.getTranslatedText('SUCCESS'),
              `${this.getTranslatedText('USER_ACCOUNT_HAS_BEEN')} ${this.user.isActive ? this.getTranslatedText('ACTIVATE') : this.getTranslatedText('SUSPEND')}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error(this.getTranslatedText('ERROR_TOGGLING_STATUS'), error);
            Swal.fire(this.getTranslatedText('ERROR'), this.getTranslatedText('ERROR_TOGGLING_STATUS_UPDATING'), 'error');
          }
        });
      }
    });
  }

  getStatusText(status: boolean): string {
    return status ? 'SUPER_ADMIN.COMMON.ACTIVE' : 'SUPER_ADMIN.COMMON.INACTIVE';
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getVerificationText(verified: boolean): string {
    return verified ? 'SUPER_ADMIN.ALL_USERS.VERIFIED' : 'SUPER_ADMIN.ALL_USERS.NOT_VERIFIED';
  }

  getVerificationClass(verified: boolean): string {
    return verified ? 'badge-light-success' : 'badge-light-warning';
  }

  getUserTypeClass(role: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return 'badge-light-danger';
      case 'developer':
        return 'badge-light-primary';
      case 'broker':
        return 'badge-light-info';
      case 'client':
        return 'badge-light-success';
      default:
        return 'badge-light-secondary';
    }
  }

  getUserRoleText(role: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return 'مدير';
      case 'developer':
        return 'مطور';
      case 'broker':
        return 'وسيط';
      case 'client':
        return 'عميل';
      default:
        return role || 'N/A';
    }
  }
}

// ===== FILTER BUTTON STYLING =====

// General filter button styling for all languages - Small from start
app-filter-drop-down {
  .dropdown {
    .btn {
      font-size: 0.75rem !important;
      padding: 0.3rem 0.6rem !important;

      i {
        font-size: 0.7rem !important;
        margin-right: 0.2rem !important;
      }
    }
  }
}

// Extra small screens (375px and below) - Even smaller filter button
@media screen and (max-width: 375px) {
  app-filter-drop-down {
    .dropdown {
      .btn {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;

        i {
          font-size: 0.65rem !important;
          margin-right: 0.15rem !important;
        }
      }
    }
  }
}

// Small screens filter button for English - smaller than Arabic
@media screen and (max-width: 767px) {
  :host-context(html[lang="en"]) {
    app-filter-drop-down {
      .dropdown {
        .btn {
          font-size: 0.65rem !important;
          padding: 0.2rem 0.4rem !important;

          i {
            font-size: 0.6rem !important;
            margin-right: 0.1rem !important;
          }
        }
      }
    }
  }
}

// Medium small screens filter button for English (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  :host-context(html[lang="en"]) {
    app-filter-drop-down {
      .dropdown {
        .btn {
          font-size: 0.7rem !important;
          padding: 0.25rem 0.5rem !important;

          i {
            font-size: 0.65rem !important;
            margin-right: 0.12rem !important;
          }
        }
      }
    }
  }
}

// Specific styling for received requests page filter button
.received-requests-page {
  app-filter-drop-down {
    .dropdown {
      .btn {
        font-size: 0.75rem !important;
        padding: 0.3rem 0.6rem !important;

        i {
          font-size: 0.7rem !important;
          margin-right: 0.2rem !important;
        }
      }
    }
  }

  // Even smaller on mobile devices
  @media screen and (max-width: 768px) {
    app-filter-drop-down {
      .dropdown {
        .btn {
          font-size: 0.7rem !important;
          padding: 0.25rem 0.5rem !important;

          i {
            font-size: 0.65rem !important;
            margin-right: 0.15rem !important;
          }
        }
      }
    }
  }
}

// ===== MEDIUM AND LARGE SCREEN LAYOUT =====

// Medium and Large screens layout styling (Tablet and Desktop)
@media screen and (min-width: 768px) {
  .d-md-flex {
    &.justify-content-between {
      .flex-grow-1 {
        // Ensure search stays centered
        max-width: 400px;
        margin: 0 1.5rem;
      }
    }
  }

  // RTL adjustments for medium and large screens
  :host-context(html[lang="ar"]) {
    .d-md-flex {
      &.justify-content-between {
        flex-direction: row-reverse;

        .flex-grow-1 {
          margin: 0 1.5rem;
        }
      }
    }
  }
}

// Specific adjustments for tablets (768px - 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .d-md-flex {
    &.justify-content-between {
      .flex-grow-1 {
        max-width: 300px;
        margin: 0 1rem;
      }
    }
  }

  // RTL adjustments for tablets
  :host-context(html[lang="ar"]) {
    .d-md-flex {
      &.justify-content-between {
        .flex-grow-1 {
          margin: 0 1rem;
        }
      }
    }
  }
}

// ===== TAB BUTTONS STYLING =====

// General tab buttons styling - larger for big screens
.nav-stretch {
  .nav-item {
    .nav-link {
      font-size: 1rem !important;
      padding: 0.6rem 1.2rem !important;

      .badge {
        font-size: 0.8rem !important;
        padding: 0.3rem 0.5rem !important;
      }
    }
  }
}

// Extra large screens (≥1200px) - Even bigger font
@media screen and (min-width: 1200px) {
  .nav-stretch {
    .nav-item {
      .nav-link {
        font-size: 1.1rem !important;
        padding: 0.7rem 1.4rem !important;

        .badge {
          font-size: 0.85rem !important;
          padding: 0.35rem 0.55rem !important;
        }
      }
    }
  }
}

// Large screens (992px - 1199px) - Bigger font
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .nav-stretch {
    .nav-item {
      .nav-link {
        font-size: 1.05rem !important;
        padding: 0.65rem 1.3rem !important;

        .badge {
          font-size: 0.82rem !important;
          padding: 0.32rem 0.52rem !important;
        }
      }
    }
  }
}

// Medium screens (768px - 991px) - Medium font
@media screen and (min-width: 768px) and (max-width: 991px) {
  .nav-stretch {
    .nav-item {
      .nav-link {
        font-size: 0.95rem !important;
        padding: 0.55rem 1.1rem !important;

        .badge {
          font-size: 0.78rem !important;
          padding: 0.28rem 0.48rem !important;
        }
      }
    }
  }
}

// Small screens (≤424px) - Same style as 425px screen and centered
@media screen and (max-width: 424px) {
  .d-flex.h-50px.mb-2 {
    height: auto !important;
    justify-content: center !important;

    .nav.nav-stretch {
      justify-content: center !important;
      flex-wrap: nowrap !important;
      gap: 0.5rem !important;

      .nav-item {
        .nav-link.btn {
          font-size: 0.8rem !important;
          padding: 0.5rem 0.75rem !important;
          margin: 0 !important;
          white-space: nowrap !important;
          min-width: auto !important;
          height: 2.2rem !important;
          line-height: 1.3 !important;
          border-radius: 0.3rem !important;
          text-align: center !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          // Remove all margins
          &.me-2, &.me-6, &.ms-2, &.ms-6 {
            margin: 0 !important;
          }

          // Badge styling
          .badge {
            font-size: 0.7rem !important;
            padding: 0.1rem 0.1rem !important;
            margin-left: 0.1rem !important;
            margin-right: 0 !important;
            min-width: auto !important;
            height: auto !important;
            line-height: 1.1 !important;

            &.ms-2, &.me-2 {
              margin-left: 0.2rem !important;
              margin-right: 0 !important;
            }
          }
        }
      }
    }
  }
}

// ===== RTL SUPPORT FOR TABS =====

// RTL adjustments for tab buttons
:host-context(html[lang="ar"]) {
  .nav-stretch {
    .nav-item {
      .nav-link {
        font-family: 'Markazi Text', sans-serif !important;
        text-align: center !important;

        .badge {
          font-family: 'Markazi Text', sans-serif !important;
        }
      }
    }
  }
}

// ===== DROPDOWN POSITIONING FOR ARABIC =====

// Arabic (RTL) - dropdown opens to the left
:host-context(html[lang="ar"]) {
  app-filter-drop-down {
    .dropdown {
      .dropdown-menu {
        left: auto !important;
        right: 0 !important;
        transform: none !important;
      }
    }
  }
}

// English (LTR) - dropdown opens to the right (default)
:host-context(html[lang="en"]) {
  app-filter-drop-down {
    .dropdown {
      .dropdown-menu {
        left: 0 !important;
        right: auto !important;
        transform: none !important;
      }
    }
  }
}

// RTL Support for Requests Page
.rtl-layout {
  direction: rtl;
  text-align: right;

  .rtl-header {
    // flex-direction: row-reverse;

    .rtl-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      font-weight: bold;
      margin-right: 0;
      margin-left: 0.25rem;
    }
  }

  .rtl-search {
    .rtl-input {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;

      &::placeholder {
        text-align: right;
        font-family: 'Hacen Liner Screen St', sans-serif;
      }
    }
  }

  .rtl-tabs {
    .rtl-nav {
      flex-direction: row-reverse;

      .rtl-tab-link {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: center;

        .badge {
          font-family: 'Hacen Liner Screen St', sans-serif;
        }
      }
    }
  }

  // General RTL styles
  .card-body {
    text-align: right;
  }
}

// Override card-body padding for table containers
.card-body {
  // Remove excessive padding that affects tables
  &.pb-0.pt-3 {
    padding-left: 1 !important;
    padding-right: 1 !important;
    padding-bottom: 0 !important;
    padding-top: 1rem !important;
  }

  // Ensure table responsiveness
  .table-responsive {
    margin: 0 !important;
    padding: 0 !important;

    .table {
      margin-bottom: 0 !important;

      // Table header styling
      thead {
        tr {
          th {
            padding: 0.75rem 1rem !important;

            &.ps-4 {
              padding-left: 1rem !important;
            }

            &.pe-4 {
              padding-right: 1rem !important;
            }
          }
        }
      }

      // Table body styling
      tbody {
        tr {
          td {
            padding: 0.75rem 1rem !important;
          }
        }
      }
    }
  }

  // Empty state styling
  .rtl-empty-state {
    padding: 2rem 1rem !important;
    margin: 0 !important;
  }

  // Responsive table adjustments
  @media screen and (max-width: 768px) {
    &.pb-0.pt-3 {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .table-responsive {
      .table {
        font-size: 0.85rem !important;

        thead {
          tr {
            th {
              padding: 0.5rem 0.75rem !important;
              font-size: 0.8rem !important;
            }
          }
        }

        tbody {
          tr {
            td {
              padding: 0.5rem 0.75rem !important;
              font-size: 0.85rem !important;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 425px) {
    &.pb-0.pt-3 {
      padding-left: 0.25rem !important;
      padding-right: 0.25rem !important;
    }

    .table-responsive {
      .table {
        font-size: 0.75rem !important;

        thead {
          tr {
            th {
              padding: 0.4rem 0.5rem !important;
              font-size: 0.7rem !important;
            }
          }
        }

        tbody {
          tr {
            td {
              padding: 0.4rem 0.5rem !important;
              font-size: 0.75rem !important;
            }
          }
        }
      }
    }

    .rtl-empty-state {
      padding: 1.5rem 0.5rem !important;

      .text-muted {
        font-size: 0.9rem !important;

        .fa-solid {
          font-size: 2rem !important;
        }
      }
    }
  }
}

  .d-flex {
    &.justify-content-between {
      // flex-direction: row-reverse;
    }
  }

  // Filter dropdown RTL support
  app-filter-drop-down {
    direction: rtl;
  }


// Arabic font support
:host-context(html[lang="ar"]) {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .nav-link {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}

// Responsive layout for medium-small screens (420px - 500px) - Center everything
@media screen and (min-width: 420px) and (max-width: 500px) {
  .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 1rem !important;

    // Title section - centered
    .d-flex.my-6 {
      width: 100% !important;
      justify-content: center !important;
      margin: 0.5rem 0 !important;

      h1 {
        font-size: 1.4rem !important;
        margin: 0 !important;
        text-align: center !important;
      }
    }

    // Search section - centered
    .d-flex.my-4:has(form) {
      width: 100% !important;
      justify-content: center !important;
      margin: 0.5rem 0 !important;

      form {
        width: 100% !important;
        max-width: 300px !important;
        margin-bottom: 0 !important;

        input {
          font-size: 0.9rem !important;
          padding: 0.6rem 1rem !important;
          text-align: center !important;
        }
      }
    }

    // Filter section - centered with smaller button
    .d-flex.flex-column.my-4:has(app-filter-drop-down) {
      width: 100% !important;
      justify-content: center !important;
      margin: 0.5rem 0 !important;

      app-filter-drop-down {
        display: flex !important;
        justify-content: center !important;

        .dropdown {
          .btn {
            font-size: 0.85rem !important;
            padding: 0.5rem 1rem !important;
            white-space: nowrap !important;

            i {
              font-size: 0.8rem !important;
              margin-right: 0.3rem !important;
            }
          }
        }
      }
    }
  }
}

// Responsive tabs for medium-small screens (425px - 496px) - Center and slightly smaller
@media screen and (min-width: 425px) and (max-width: 496px) {
  .d-flex.h-50px.mb-2 {
    height: auto !important;
    // margin-bottom: 1rem !important;
    justify-content: center !important;

    .nav.nav-stretch {
      justify-content: center !important;
      flex-wrap: nowrap !important;
      gap: 0.5rem !important;

      .nav-item {
        .nav-link.btn {
          font-size: 0.8rem !important;
          padding: 0.5rem 0.75rem !important;
          margin: 0 !important;
          white-space: nowrap !important;
          min-width: auto !important;
          height: 2.2rem !important;
          line-height: 1.3 !important;
          border-radius: 0.3rem !important;
          text-align: center !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          // Remove all margins
          &.me-2, &.me-6, &.ms-2, &.ms-6 {
            margin: 0 !important;
          }

          // Badge styling
          .badge {
            font-size: 0.7rem !important;
            padding: 0.1rem 0.1rem !important;
            margin-left: 0.1rem !important;
            margin-right: 0 !important;
            min-width: auto !important;
            height: auto !important;
            line-height: 1.1 !important;

            &.ms-2, &.me-2 {
              margin-left: 0.2rem !important;
              margin-right: 0 !important;
            }
          }
        }
      }
    }
  }
}

// Responsive layout for header elements (425px and below) - 3 items in one line
@media screen and (max-width: 425px) {
  .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    flex-wrap: nowrap !important;
    align-items: center !important;
    gap: 0.4rem !important;

    // Title section - compact
    .d-flex.my-6 {
      flex: 0 0 auto !important;
      margin: 0 !important;

      h1 {
        font-size: 0.9rem !important;
        margin: 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 70px !important;
        line-height: 1.2 !important;
      }
    }

    // Search section - flexible
    .d-flex.my-4:has(form) {
      flex: 1 1 auto !important;
      margin: 0 !important;
      min-width: 0 !important;

      form {
        width: 100% !important;
        max-width: none !important;
        margin-bottom: 0 !important;

        input {
          font-size: 0.75rem !important;
          padding: 0.3rem 0.6rem !important;
          height: 1.8rem !important;
          min-width: 0 !important;
        }
      }
    }

    // Filter section - compact
    .d-flex.flex-column.my-4:has(app-filter-drop-down) {
      flex: 0 0 auto !important;
      margin: 0 !important;

      app-filter-drop-down {
        .dropdown {
          .btn {
            font-size: 0.55rem !important;
            padding: 0.2rem 0.3rem !important;
            white-space: nowrap !important;
            min-width: auto !important;
            height: 1.5rem !important;
            line-height: 1.1 !important;
            border-radius: 0.25rem !important;

            i {
              font-size: 0.55rem !important;
              margin-right: 0.0rem !important;
            }
          }
        }
      }
    }
  }
}

// Even smaller screens (375px and below)
@media screen and (max-width: 375px) {
  .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    gap: 0.25rem !important;

    .d-flex.my-6 {
      h1 {
        font-size: 0.8rem !important;
        max-width: 60px !important;
      }
    }

    .d-flex.my-4:has(form) {
      form {
        input {
          font-size: 0.7rem !important;
          padding: 0.3rem 0.5rem !important;
          height: 1.6rem !important;
        }
      }
    }

    .d-flex.flex-column.my-4:has(app-filter-drop-down) {
      app-filter-drop-down {
        .dropdown {
          .btn {
            font-size: 0.5rem !important;
            padding: 0.15rem 0.25rem !important;
            height: 1.3rem !important;
            line-height: 1 !important;
            border-radius: 0.2rem !important;

            i {
              font-size: 0.5rem !important;
              margin-right: 0.08rem !important;
            }
          }
        }
      }
    }
  }
}

// RTL support for medium-small screens (420px - 500px)
:host-context(html[lang="ar"]) {
  @media screen and (min-width: 420px) and (max-width: 500px) {
    .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
      direction: rtl !important;

      .d-flex.my-6 {
        h1 {
          text-align: center !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }
      }

      .d-flex.my-4:has(form) {
        form {
          input {
            text-align: center !important;
            font-family: 'Hacen Liner Screen St', sans-serif !important;
            direction: rtl !important;
          }
        }
      }

      .d-flex.flex-column.my-4:has(app-filter-drop-down) {
        app-filter-drop-down {
          .dropdown {
            .btn {
              font-family: 'Hacen Liner Screen St', sans-serif !important;
              text-align: center !important;
              direction: rtl !important;

              i {
                margin-left: 0.3rem !important;
                margin-right: 0 !important;
              }
            }
          }
        }
      }
    }
  }

  // RTL support for tabs in medium-small screens (425px - 496px)
  @media screen and (min-width: 425px) and (max-width: 496px) {
    .d-flex.h-50px.mb-2 {
      direction: rtl !important;

      .nav.nav-stretch {
        direction: rtl !important;
        margin-left: 10%;


        .nav-item {
          .nav-link.btn {
            font-family: 'Hacen Liner Screen St', sans-serif !important;
            text-align: center !important;
            direction: rtl !important;

            .badge {
              font-family: 'Hacen Liner Screen St', sans-serif !important;
              margin-right: 0.3rem !important;
              margin-left: 0 !important;

              &.ms-2, &.me-2 {
                margin-right: 0.3rem !important;
                margin-left: 0 !important;
              }
            }
          }
        }
      }
    }
  }

  // RTL support for tabs in small screens (≤424px)
  @media screen and (max-width: 424px) {
    .d-flex.h-50px.mb-2 {
      direction: rtl !important;

      .nav.nav-stretch {
        direction: rtl !important;

        .nav-item {
          .nav-link.btn {
            font-family: 'Hacen Liner Screen St', sans-serif !important;
            text-align: center !important;
            direction: rtl !important;

            .badge {
              font-family: 'Hacen Liner Screen St', sans-serif !important;
              margin-right: 0.3rem !important;
              margin-left: 0 !important;

              &.ms-2, &.me-2 {
                margin-right: 0.3rem !important;
                margin-left: 0 !important;
              }
            }
          }
        }
      }
    }
  }

  // RTL support for small screens layout
  @media screen and (max-width: 425px) {
    .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
      direction: rtl !important;

      .d-flex.my-6 {
        h1 {
          text-align: right !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }
      }

      .d-flex.my-4:has(form) {
        form {
          input {
            text-align: right !important;
            font-family: 'Hacen Liner Screen St', sans-serif !important;
            direction: rtl !important;
          }
        }
      }

      .d-flex.flex-column.my-4:has(app-filter-drop-down) {
        app-filter-drop-down {
          .dropdown {
            .btn {
              font-family: 'Hacen Liner Screen St', sans-serif !important;
              text-align: center !important;
              direction: rtl !important;

              i {
                margin-left: 0.1rem !important;
                margin-right: 0 !important;
              }
            }
          }
        }
      }
    }
  }
}

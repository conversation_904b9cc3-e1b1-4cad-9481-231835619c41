// CRITICAL: Remove margin-left from user name on mobile - HIGHEST PRIORITY
@media screen and (max-width: 576px) {
  // Force remove margin-left with maximum specificity
  span.text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
  span.text-gray-800.fs-2.fw-bolder,
  .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
  .text-gray-800.fs-2.fw-bolder {
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: center !important;
    width: 100% !important;
    display: block !important;
  }

  // Override any Bootstrap margin classes
  .me-1 {
    margin-right: 0 !important;
  }

  .ms-2 {
    margin-left: 0 !important;
  }
}

// CRITICAL: Fix upgrade button for small screens (390px and below)
@media screen and (max-width: 390px) {
  .d-flex.my-4.align-self-center {
    width: 100% !important;
    justify-content: center !important;
    margin: 0.5rem 0 1rem 0 !important;
    padding: 0 1rem !important;

    .btn {
      width: 100% !important;
      max-width: calc(100% - 1rem) !important;
      margin: 0 auto !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
    }
  }
}

// RTL Support
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .d-flex {
    &.flex-wrap.flex-sm-nowrap {
      flex-direction: row-reverse;

      .me-7 {
        margin-right: 0 !important;
        // margin-left: 1.5rem !important;
        order: 2;
      }

      .flex-grow-1 {
        order: 1;
      }
    }

    &.justify-content-between {
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: flex-start !important;

      .d-flex.flex-column {
        text-align: right !important;
        flex: 1;
        order: 1;
      }

      .d-flex.my-4 {
        order: 2;
        margin-right: 0 !important;
        // margin-left: 1rem !important;
        align-self: flex-start !important;
        flex-shrink: 0;
      }
    }
  }

  .me-3, .me-6, .me-2 {
    margin-right: 0 !important;
    margin-left: 0.75rem !important;
  }

  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  .border-dashed {
    text-align: center;

    .d-flex.align-items-center.justify-content-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6.text-gray-500.text-center {
      text-align: center !important;
    }
  }

  .fw-bold {
    text-align: center;
  }

  // إصلاح ترتيب الكاردات في العربية
  .d-flex.flex-wrap {
    &:not(.fw-bold) {
      justify-content: flex-start !important;

      .border.border-gray-300.border-dashed {
        margin-right: 0 !important;
        margin-left: 1.5rem !important;

        &:first-child {
          margin-left: 0 !important;
        }
      }
    }
  }

  // إصلاح أيقونة الكاميرا في العربية
  .btn-icon,
  .btn-circle {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    i {
      margin: 0 !important;
    }
  }

  // إصلاح موضع الكاميرا
  .position-absolute {
    &.bottom-0 {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }

  // محاذاة النصوص في العربية
  .fs-2.fw-bolder {
    text-align: center !important;
  }

  .border-dashed {
    .d-flex.align-items-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6 {
      text-align: center !important;
    }
  }

  // تحسين التخطيط العام
  .flex-grow-1 {
    .d-flex.justify-content-between {
      flex-direction: row !important;

      .d-flex.flex-column {
        text-align: right !important;
      }
    }
  }

  .d-flex.align-items-center.mb-2 {
    justify-content: flex-start !important;
    text-align: right !important;
    direction: rtl !important;
    width: 100% !important;
    gap: 0.5rem;

    .text-gray-800.fs-2.fw-bolder {
      // text-align: left !important;
      margin-right: 0 !important;
      margin-left: 2rem !important;
      order: 1;
    }

    .badge {
      margin-left: 0 !important;
      margin-right: 0 !important;
      order: 2;
      align-self: center;
    }
  }

  // محاذاة معلومات الاتصال
  .d-flex.flex-wrap {
    // justify-content: flex-start !important;
    // text-align: right !important;
    // direction: rtl !important;

    a {
      direction: rtl !important;
      text-align: right !important;
      margin-right: 0 !important;
      margin-left: 1.25rem !important;

      app-keenicon {
        margin-right: 0 !important;
        margin-left: 0.25rem !important;
      }
    }
  }

  .d-flex.my-4 {
    margin-right: auto !important;
    margin-left: 0 !important;
  }

  // إزالة المساحة الزائدة
  .pe-8 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .me-7 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }

  // إصلاح عام للمحاذاة في العربية
  .card-body {
    .d-flex.flex-wrap.flex-sm-nowrap.mb-3 {
      align-items: flex-start !important;
    }
  }

  // إصلاح موضع زر ترقية الخطة
  .d-flex.my-4.align-self-center {
    align-self: flex-start !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;

    .btn {
      white-space: nowrap;
    }
  }

  // إصلاح محاذاة الأيقونات مع النص
  app-keenicon {
    &.me-1 {
      margin-right: 0 !important;
      margin-left: 0.25rem !important;
    }
  }

  // إصلاح محاذاة البادج
  .badge {
    &.ms-2 {
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
    }

    // تحسين تصميم البادج
    &.user-badge {
      font-size: 0.75rem !important;
      padding: 0.4rem 0.8rem !important;
      border-radius: 8px !important;
      font-weight: 600 !important;
      white-space: nowrap;
      display: inline-flex;
      align-items: center;

      // تحسين ألوان البادج للحساب المجاني
      &.badge-light-primary {
        background-color: rgba(34, 197, 94, 0.1) !important;
        color: #22c55e !important;
        border: 1px solid rgba(34, 197, 94, 0.2);
      }
    }
  }
}

// Enhanced Arabic Profile Header Design
html[lang="ar"] {
  .card {
    border-radius: 16px !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e1e3ea !important;
    overflow: hidden !important;

    .card-body {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
      padding: 2.5rem !important;

      // Profile image section
      .symbol {
        &.symbol-100px,
        &.symbol-lg-160px {
          border: 4px solid #ffffff !important;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;

          img {
            border-radius: 50% !important;
          }
        }
      }

      // Name section enhancement
      .name-section {
        background: rgba(255, 255, 255, 0.8) !important;
        padding: 1rem 1.5rem !important;
        border-radius: 12px !important;
        margin-bottom: 1.5rem !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;

        .user-name {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          font-size: 1.8rem !important;
          font-weight: 800 !important;
          color: #2d3748 !important;
          text-align: center !important;
          margin: 0 !important;
          line-height: 1.4 !important;
        }

        .user-badge {
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
          color: white !important;
          font-family: 'Hacen Liner Screen', sans-serif !important;
          font-size: 0.85rem !important;
          font-weight: 600 !important;
          padding: 0.5rem 1rem !important;
          border-radius: 20px !important;
          border: none !important;
          box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3) !important;
          margin-top: 0.5rem !important;
          display: inline-block !important;
        }
      }

      // Contact info enhancement
      .d-flex.flex-wrap.fw-bold.fs-6 {
        background: rgba(255, 255, 255, 0.6) !important;
        padding: 1.5rem !important;
        border-radius: 12px !important;
        margin-bottom: 1.5rem !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;

        a {
          background: rgba(255, 255, 255, 0.8) !important;
          padding: 0.75rem 1rem !important;
          border-radius: 8px !important;
          margin-bottom: 0.75rem !important;
          margin-left: 0 !important;
          margin-right: 1rem !important;
          border: 1px solid rgba(0, 0, 0, 0.05) !important;
          transition: all 0.3s ease !important;
          text-decoration: none !important;
          color: #4a5568 !important;
          font-family: 'Hacen Liner Screen', sans-serif !important;
          font-size: 0.95rem !important;

          &:hover {
            background: rgba(34, 197, 94, 0.1) !important;
            border-color: rgba(34, 197, 94, 0.3) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2) !important;
          }

          app-keenicon {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;

            .svg-icon {
              color: #22c55e !important;
            }
          }
        }
      }

      // Stats cards enhancement
      .border-dashed {
        background: rgba(255, 255, 255, 0.8) !important;
        border: 2px dashed rgba(34, 197, 94, 0.3) !important;
        border-radius: 12px !important;
        padding: 1.5rem !important;
        transition: all 0.3s ease !important;

        &:hover {
          background: rgba(34, 197, 94, 0.05) !important;
          border-color: rgba(34, 197, 94, 0.5) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 24px rgba(34, 197, 94, 0.15) !important;
        }

        .fw-bold.fs-6 {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          font-size: 1.1rem !important;
          color: #2d3748 !important;
          margin-bottom: 0.5rem !important;
        }

        .text-gray-500 {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          color: #6b7280 !important;
          font-size: 0.9rem !important;
        }
      }
    }
  }

  // Camera button enhancement
  .btn-icon.btn-circle {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
    border: 3px solid #ffffff !important;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4) !important;

    i {
      color: white !important;
    }

    &:hover {
      transform: scale(1.1) !important;
      box-shadow: 0 6px 16px rgba(34, 197, 94, 0.5) !important;
    }
  }
}

// Medium screens (1024px and below) - 4 much smaller stats in a row
@media screen and (max-width: 1024px) {
  .d-flex.flex-wrap.flex-stack {
    .d-flex.flex-column.flex-grow-1.pe-8 {
      padding-right: 0.5rem !important;

      .d-flex.flex-wrap {
        justify-content: center !important;
        gap: 0.3rem !important;

        .border.border-gray-300.border-dashed.rounded {
          flex: 0 0 calc(25% - 0.225rem) !important;
          min-width: calc(25% - 0.225rem) !important;
          max-width: calc(25% - 0.225rem) !important;
          margin-right: 0 !important;
          margin-bottom: 0.3rem !important;
          padding: 0.4rem 0.25rem !important;
          min-height: auto !important;

          .d-flex.align-items-center.justify-content-center {
            margin-bottom: 0.1rem !important;
          }

          .fs-2 {
            font-size: 0.95rem !important;
            margin-bottom: 0.1rem !important;
            line-height: 1.1 !important;
          }

          .fs-6 {
            font-size: 0.6rem !important;
            line-height: 1 !important;
            padding: 0 0.1rem !important;
          }
        }
      }
    }
  }
}

// Medium screens (1162px and below) - 4 stats in a row with smaller size
@media screen and (max-width: 1162px) {
  .d-flex.flex-wrap.flex-stack {
    .d-flex.flex-column.flex-grow-1.pe-8 {
      padding-right: 1rem !important;

      .d-flex.flex-wrap {
        justify-content: center !important;
        gap: 0.5rem !important;

        .border.border-gray-300.border-dashed.rounded {
          flex: 0 0 calc(25% - 0.375rem) !important;
          min-width: calc(25% - 0.375rem) !important;
          max-width: calc(25% - 0.375rem) !important;
          margin-right: 0 !important;
          margin-bottom: 0.75rem !important;
          padding: 0.75rem 0.5rem !important;

          .fs-2 {
            font-size: 1.25rem !important;
            margin-bottom: 0.25rem !important;
          }

          .fs-6 {
            font-size: 0.75rem !important;
            line-height: 1.2 !important;
          }
        }
      }
    }
  }
}

// Tablet screens (768px to 1161px) - Keep 4 in a row but adjust spacing
@media screen and (min-width: 577px) and (max-width: 1161px) {
  .d-flex.flex-wrap.flex-stack {
    .d-flex.flex-column.flex-grow-1.pe-8 {
      .d-flex.flex-wrap {
        .border.border-gray-300.border-dashed.rounded {
          flex: 0 0 calc(25% - 0.5rem) !important;
          min-width: calc(25% - 0.5rem) !important;
          max-width: calc(25% - 0.5rem) !important;
          margin-right: 0 !important;
          padding: 1rem 0.75rem !important;

          .fs-2 {
            font-size: 1.5rem !important;
          }

          .fs-6 {
            font-size: 0.8rem !important;
          }
        }
      }
    }
  }
}

// Mobile Responsive Design - Center everything from 576px with high specificity
@media screen and (max-width: 576px) {
  // Force override all existing styles
  .card-body * {
    box-sizing: border-box !important;
  }

  // Remove margin-left from user name in mobile from 576px and below
  .text-gray-800.fs-2.fw-bolder {
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: center !important;
    width: 100% !important;
    display: block !important;
  }
  .card {
    margin-bottom: 1rem !important;
    border-radius: 12px !important;
  }

  .card-body {
    padding: 1rem !important;
    text-align: center !important;
  }

  // Main flex container - Center everything with highest specificity
  .card-body .d-flex.flex-wrap.flex-sm-nowrap.mb-3 {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    justify-content: center !important;
    width: 100% !important;
    display: flex !important;
  }

  // Force center all flex containers
  .card-body .d-flex {
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  // Specific targeting for the main content area
  .card-body .flex-grow-1 {
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  // Target the main wrapper div
  .card-body .flex-grow-1 > .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    text-align: center !important;
  }

  // Target the content column
  .card-body .flex-grow-1 .d-flex.flex-column.flex-grow-1 {
    width: 100% !important;
    align-items: center !important;
    text-align: center !important;
    order: 1 !important;
  }

  // Target contact info section specifically - Reduce bottom margin
  .card-body .d-flex.flex-wrap.fw-bold.fs-6.mb-4.pe-2 {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    text-align: center !important;
    padding-right: 0 !important;
    gap: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  // Target each contact item
  .card-body .d-flex.flex-wrap.fw-bold.fs-6.mb-4.pe-2 > a {
    width: 100% !important;
    max-width: 300px !important;
    justify-content: center !important;
    text-align: center !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    margin-bottom: 0.75rem !important;
    padding: 0.75rem !important;
    border-radius: 8px !important;
    background: rgba(248, 249, 250, 0.8) !important;
    border: 1px solid #e9ecef !important;
  }

  // Target upgrade button - Center from 576px with reduced margin
  .card-body .d-flex.my-4.align-self-center {
    width: 100% !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0.75rem 0 1.5rem 0 !important;

    .btn {
      width: 100% !important;
      max-width: 280px !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
      align-items: center !important;
    }
  }

  // Target stats section - 2 items per row
  .card-body .d-flex.flex-wrap.flex-stack {
    width: 100% !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    order: 3 !important;
    margin-top: 1.5rem !important;
  }

  // Stats container
  .card-body .d-flex.flex-column.flex-grow-1.pe-8 {
    width: 100% !important;
    padding-right: 0 !important;
    align-items: center !important;
  }

  // Stats wrapper - 2 per row
  .card-body .d-flex.flex-wrap:has(.border-dashed) {
    justify-content: center !important;
    gap: 0.75rem !important;
    width: 100% !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }

  // Individual stat cards - 2 per row
  .card-body .border.border-gray-300.border-dashed.rounded {
    flex: 0 0 calc(50% - 0.375rem) !important;
    min-width: calc(50% - 0.375rem) !important;
    max-width: calc(50% - 0.375rem) !important;
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important;
    padding: 0.75rem 0.5rem !important;
    text-align: center !important;

    .fs-2 {
      font-size: 1.25rem !important;
      margin-bottom: 0.25rem !important;
    }

    .fs-6 {
      font-size: 0.75rem !important;
      line-height: 1.2 !important;
    }
  }

  // Profile image section - Centered
  .me-7 {
    margin-right: 0 !important;
    margin-left: 0 !important;
    margin-bottom: 1.5rem !important;
    order: 1 !important;
    display: flex !important;
    justify-content: center !important;
    width: 100% !important;
  }

  .symbol {
    &.symbol-100px,
    &.symbol-lg-160px {
      width: 120px !important;
      height: 120px !important;
      margin: 0 auto !important;
    }
  }

  // Content section - Centered
  .flex-grow-1 {
    width: 100% !important;
    order: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  // Name and badge section - Centered
  .d-flex.justify-content-between {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    justify-content: center !important;
    width: 100% !important;
  }

  .d-flex.flex-column.flex-grow-1 {
    width: 100% !important;
    text-align: center !important;
    align-items: center !important;
    justify-content: center !important;
  }

  // Name section - Perfectly centered
  .name-section {
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
    flex-wrap: wrap !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
  }

  .user-name {
    font-size: 1.5rem !important;
    text-align: center !important;
    margin-bottom: 0.5rem !important;
    width: 100% !important;
    display: block !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: center !important;
    justify-self: center !important;
    align-self: center !important;
  }

  // Specific targeting for the user name span
  .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name {
    text-align: center !important;
    display: block !important;
    width: 100% !important;
    margin: 0 auto !important;
    margin-bottom: 0.5rem !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    justify-content: center !important;
  }

  // Remove margin from me-1 class on mobile
  .me-1 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  // Center the name section container
  .d-flex.align-items-center.mb-2.name-section {
    justify-content: center !important;
    text-align: center !important;
    width: 100% !important;
    flex-direction: column !important;
    align-items: center !important;
  }

  // Center all content sections
  .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    width: 100% !important;
  }

  .d-flex.flex-column.flex-grow-1 {
    width: 100% !important;
    align-items: center !important;
    text-align: center !important;
  }

  // Center contact info section
  .d-flex.flex-wrap.fw-bold.fs-6.mb-4.pe-2 {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    width: 100% !important;
    padding-right: 0 !important;
    gap: 0.75rem !important;
    margin-bottom: 1.5rem !important;
  }

  // Center upgrade button section
  .d-flex.my-4.align-self-center {
    width: 100% !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    margin: 1rem 0 !important;
    order: 3 !important;
  }

  // Center stats section
  .d-flex.flex-wrap.flex-stack {
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    width: 100% !important;
    margin-top: 1.5rem !important;
  }

  .d-flex.flex-column.flex-grow-1.pe-8 {
    width: 100% !important;
    align-items: center !important;
    text-align: center !important;
    padding-right: 0 !important;
  }

  // Order elements vertically
  .flex-grow-1 {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;

    // Order 1: Name and badge section
    .d-flex.justify-content-between {
      order: 1 !important;
      width: 100% !important;
      margin-bottom: 1.5rem !important;
    }

    // Order 2: Contact info section
    .d-flex.flex-column.flex-grow-1 {
      order: 2 !important;
      width: 100% !important;
    }

    // Order 3: Upgrade button
    .d-flex.my-4 {
      order: 3 !important;
      width: 100% !important;
    }

    // Order 4: Stats section
    .d-flex.flex-wrap.flex-stack {
      order: 4 !important;
      width: 100% !important;
    }
  }

  .user-badge {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 0.5rem !important;
    display: block !important;
    text-align: center !important;
  }

  // Contact info section
  .d-flex.flex-wrap.fw-bold.fs-6 {
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.75rem !important;
    margin-bottom: 1.5rem !important;

    a {
      margin-right: 0 !important;
      margin-bottom: 0 !important;
      width: 100% !important;
      max-width: 280px !important;
      justify-content: center !important;
      text-align: center !important;
      padding: 0.75rem !important;
      border-radius: 8px !important;
      background: rgba(248, 249, 250, 0.8) !important;
      border: 1px solid #e9ecef !important;

      app-keenicon {
        margin-right: 0.5rem !important;
        margin-left: 0 !important;
      }
    }
  }

  // Upgrade button section
  .d-flex.my-4 {
    width: 100% !important;
    justify-content: center !important;
    margin: 1rem 0 !important;
    order: 3 !important;

    .btn {
      width: 100% !important;
      max-width: 280px !important;
      padding: 0.875rem 1.5rem !important;
      font-size: 1rem !important;
    }
  }

  // Stats section for brokers
  .d-flex.flex-wrap.flex-stack {
    margin-top: 1.5rem !important;
  }

  .d-flex.flex-column.flex-grow-1.pe-8 {
    padding-right: 0 !important;
    width: 100% !important;
  }

  .d-flex.flex-wrap:not(.fw-bold) {
    justify-content: center !important;
    gap: 0.75rem !important;

    .border-dashed {
      min-width: calc(50% - 0.375rem) !important;
      margin-right: 0 !important;
      margin-bottom: 0.75rem !important;
      padding: 1rem 0.5rem !important;
      text-align: center !important;

      .fs-2 {
        font-size: 1.5rem !important;
      }

      .fs-6 {
        font-size: 0.8rem !important;
      }
    }
  }
}

// Extra small screens (320px and below) - Single column for stats
@media screen and (max-width: 320px) {
  .card-body {
    padding: 0.75rem !important;
  }

  .user-name {
    font-size: 1.25rem !important;
  }

  .d-flex.flex-wrap.fw-bold.fs-6 {
    a {
      max-width: 100% !important;
      font-size: 0.9rem !important;
    }
  }

  // Single column for stats on very small screens
  .border-dashed {
    flex: 0 0 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 0.5rem !important;
    padding: 0.75rem 0.5rem !important;

    .fs-2 {
      font-size: 1.1rem !important;
    }

    .fs-6 {
      font-size: 0.7rem !important;
    }
  }

  .btn {
    font-size: 0.9rem !important;
    padding: 0.75rem 1.25rem !important;
  }

  // Override 2-column layout for very small screens
  :host ::ng-deep .card-body {
    .d-flex.flex-wrap.flex-stack {
      .d-flex.flex-column.flex-grow-1 {
        .d-flex.flex-wrap {
          .border-dashed {
            flex: 0 0 100% !important;
            min-width: 100% !important;
            max-width: 100% !important;
            margin: 0 0 0.5rem 0 !important;
          }
        }
      }
    }
  }
}

// RTL Support for Mobile - Centered
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  @media screen and (max-width: 576px) {
    .card-body {
      text-align: center !important;
      direction: rtl !important;
    }

    .d-flex.flex-wrap.flex-sm-nowrap {
      align-items: center !important;
      justify-content: center !important;
      text-align: center !important;
    }

    .me-7 {
      margin-right: 0 !important;
      margin-left: 0 !important;
      justify-content: center !important;
      display: flex !important;
    }

    .flex-grow-1 {
      align-items: center !important;
      text-align: center !important;
      display: flex !important;
      flex-direction: column !important;
      width: 100% !important;
    }

    // Center all sections in RTL
    .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      text-align: center !important;
      width: 100% !important;
    }

    .d-flex.flex-column.flex-grow-1 {
      width: 100% !important;
      align-items: center !important;
      text-align: center !important;
    }

    .d-flex.flex-wrap.fw-bold.fs-6.mb-4.pe-2 {
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      text-align: center !important;
      width: 100% !important;
      padding-right: 0 !important;
      gap: 0.75rem !important;
    }

    .d-flex.my-4.align-self-center {
      width: 100% !important;
      justify-content: center !important;
      align-items: center !important;
      text-align: center !important;
      margin: 1.5rem 0 !important;

      .btn {
        width: 100% !important;
        max-width: 280px !important;
        text-align: center !important;
        justify-content: center !important;
        display: flex !important;
        align-items: center !important;
        margin: 0 auto !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
        direction: rtl !important;
      }
    }

    .d-flex.flex-wrap.flex-stack {
      justify-content: center !important;
      align-items: center !important;
      text-align: center !important;
      width: 100% !important;
    }

    .name-section {
      direction: rtl !important;
      text-align: center !important;
      justify-content: center !important;
      align-items: center !important;
    }

    .user-name {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      text-align: center !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      display: block !important;
      width: 100% !important;
    }

    // Specific targeting for Arabic user name span
    .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name {
      text-align: center !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      display: block !important;
      width: 100% !important;
      font-family: 'Noto Kufi Arabic', sans-serif !important;
    }

    // Remove margin from me-1 class in RTL
    .me-1 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .user-badge {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      text-align: center !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    // Remove margin-left from user name in RTL
    .text-gray-800.fs-2.fw-bolder {
      margin-left: 0 !important;
      margin-right: 0 !important;
      text-align: center !important;
      width: 100% !important;
      display: block !important;
      font-family: 'Noto Kufi Arabic', sans-serif !important;
    }

    .d-flex.flex-wrap.fw-bold.fs-6 {
      a {
        direction: rtl !important;
        text-align: center !important;

        app-keenicon {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }
    }

    .border-dashed {
      text-align: center !important;

      .fw-bold.fs-6 {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        text-align: center !important;
      }
    }
  }
}

// Additional centering for all screen sizes below 576px
@media screen and (max-width: 576px) {
  // Force center alignment for all elements
  * {
    &.d-flex.align-items-center.mb-2 {
      justify-content: center !important;
      text-align: center !important;
      width: 100% !important;
    }
  }

  // Center the profile image container
  .symbol-fixed {
    margin: 0 auto !important;
    display: block !important;
  }

  // Center camera button
  .position-absolute.bottom-0 {
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
  }

}

// Force center with highest specificity - Override everything
@media screen and (max-width: 576px) {
  :host ::ng-deep .card-body {
    text-align: center !important;

    .d-flex {
      justify-content: center !important;
      align-items: center !important;
      text-align: center !important;

      &.flex-wrap.flex-sm-nowrap {
        flex-direction: column !important;
        width: 100% !important;
      }

      &.justify-content-between {
        flex-direction: column !important;
        justify-content: center !important;
      }

      &.flex-wrap.fw-bold.fs-6 {
        flex-direction: column !important;
        gap: 1rem !important;
        width: 100% !important;
        margin-bottom: 0.75rem !important;

        a {
          width: 100% !important;
          max-width: 300px !important;
          justify-content: center !important;
          margin: 0 auto 0.75rem auto !important;
          padding: 0.75rem !important;
          background: rgba(248, 249, 250, 0.8) !important;
          border: 1px solid #e9ecef !important;
          border-radius: 8px !important;
        }
      }
    }

    .flex-grow-1 {
      width: 100% !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      text-align: center !important;
    }

    .user-name {
      text-align: center !important;
      width: 100% !important;
      margin: 0 auto !important;
    }

    // Remove margin-left from user name with high specificity
    .text-gray-800.fs-2.fw-bolder {
      margin-left: 0 !important;
      margin-right: 0 !important;
      text-align: center !important;
      width: 100% !important;
      display: block !important;
      margin: 0 auto !important;
    }

    .user-badge {
      margin: 0.5rem auto !important;
      text-align: center !important;
    }

    // Center upgrade button with high specificity and reduced margin
    .d-flex.my-4.align-self-center {
      width: 100% !important;
      justify-content: center !important;
      align-items: center !important;
      text-align: center !important;
      margin: 0.75rem 0 1.5rem 0 !important;

      .btn {
        width: 100% !important;
        max-width: 280px !important;
        text-align: center !important;
        justify-content: center !important;
        display: flex !important;
        align-items: center !important;
        margin: 0 auto !important;
        padding: 0.875rem 1.5rem !important;
        font-size: 1rem !important;
      }
    }

    // Stats section - 2 per row with reduced padding
    .d-flex.flex-wrap.flex-stack {
      width: 100% !important;
      justify-content: center !important;
      margin-top: 1.5rem !important;

      .d-flex.flex-column.flex-grow-1 {
        width: 100% !important;
        padding-right: 0 !important;

        .d-flex.flex-wrap {
          justify-content: center !important;
          gap: 0.5rem !important;
          max-width: 100% !important;

          .border-dashed {
            flex: 0 0 calc(50% - 0.25rem) !important;
            min-width: calc(50% - 0.25rem) !important;
            max-width: calc(50% - 0.25rem) !important;
            margin: 0 0 0.5rem 0 !important;
            padding: 0.75rem 0.25rem !important;
            text-align: center !important;

            .fs-2 {
              font-size: 1.1rem !important;
              margin-bottom: 0.25rem !important;
            }

            .fs-6 {
              font-size: 0.7rem !important;
              line-height: 1.1 !important;
              padding: 0 0.25rem !important;
            }
          }
        }
      }
    }
  }
}

// Specific override for margin-left removal on mobile screens with highest specificity
@media screen and (max-width: 576px) {
  // Override any margin-left on user name specifically with maximum specificity
  .card-body .flex-grow-1 .d-flex.justify-content-between .d-flex.flex-column .name-section .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
  .card-body .flex-grow-1 .d-flex.justify-content-between .d-flex.flex-column .name-section .text-gray-800.fs-2.fw-bolder,
  .card-body .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
  .card-body .text-gray-800.fs-2.fw-bolder,
  .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
  .text-gray-800.fs-2.fw-bolder {
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: center !important;
    width: 100% !important;
    display: block !important;
  }

  // Force override with ::ng-deep and highest specificity
  :host ::ng-deep .card-body {
    .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name,
    .text-gray-800.fs-2.fw-bolder {
      margin-left: 0 !important;
      margin-right: 0 !important;
      text-align: center !important;
      width: 100% !important;
      display: block !important;
      margin: 0 auto !important;
    }
  }

  // Also override any me-* classes that add margin
  .me-1, .me-2, .me-3, .me-4, .me-5 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  // Override ms-* classes for RTL
  .ms-1, .ms-2, .ms-3, .ms-4, .ms-5 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  // Force override any inline styles or other CSS
  * {
    &.text-gray-800.fs-2.fw-bolder {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }
}

// Fix upgrade button for 390px screens and below
@media screen and (max-width: 390px) {
  // Target the upgrade button container specifically
  .d-flex.my-4.align-self-center {
    width: 100% !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    margin: 0.5rem 0 1rem 0 !important; // Reduce top margin
    padding: 0 1rem !important;

    .btn {
      width: 100% !important;
      max-width: calc(100% - 1rem) !important;
      margin: 0 auto !important;
      padding: 0.75rem 1.25rem !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
      align-items: center !important;
      font-size: 0.9rem !important;
      border-radius: 8px !important;
    }
  }

  // Force center with ::ng-deep for 390px
  :host ::ng-deep .card-body {
    .d-flex.my-4.align-self-center {
      width: 100% !important;
      justify-content: center !important;
      align-items: center !important;
      margin: 0.5rem auto 1rem auto !important;
      padding: 0 1rem !important;

      .btn {
        width: 100% !important;
        max-width: calc(100% - 1rem) !important;
        margin: 0 auto !important;
        text-align: center !important;
        justify-content: center !important;
        display: flex !important;
      }
    }
  }

  // RTL support for 390px
  :host-context(html[dir="rtl"]),
  :host-context(html[lang="ar"]) {
    .d-flex.my-4.align-self-center {
      direction: rtl !important;
      text-align: center !important;

      .btn {
        direction: rtl !important;
        text-align: center !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
      }
    }
  }
}

// Override any RTL specific margins for centering on mobile
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  @media screen and (max-width: 576px) {
    .me-7 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .user-name {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .badge {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }
}

@media (min-width: 425px) {
    :host-context(html[dir=rtl]),:host-context(html[lang="ar"]) ::ng-deep {
    .text-gray-800.fs-2.fw-bolder.me-1.cursor-pointer.user-name{
      text-align: right !important;
    }
    .d-flex.main-parent-div{
      flex-direction: row !important;
    }
    a.d-flex.align-items-center.text-gray-500.text-hover-primary.me-5.mb-2.cursor-pointer.main-child-link{
      direction: rtl !important;
    }
  }
}
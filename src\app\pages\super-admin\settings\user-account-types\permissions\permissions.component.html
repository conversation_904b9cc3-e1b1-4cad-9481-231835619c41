<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header custom-permissions-header" [class.arabic-header]="translationService.isRTL()">
          <div class="header-content-wrapper">
            <div *ngIf="!translationService.isRTL()" class="header-icon-section">
              <div class="custom-icon-container">
                <i class="fas fa-shield-alt"></i>
              </div>
            </div>
            <div class="header-text-section">
              <h3 class="header-title">{{ 'PERMISSIONS.TITLE' | translate }}</h3>
              <span class="header-subtitle">{{ 'PERMISSIONS.SUBTITLE' | translate }}</span>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()" (click)="goBack()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'PERMISSIONS.BACK' | translate }}
            </button>
            <button class="btn btn-success btn-sm" (click)="addPermission()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-plus" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'PERMISSIONS.ADD_PERMISSION' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Permissions Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header custom-table-header" [class.arabic-table-header]="translationService.isRTL()">
          <div class="table-header-content">
            <h3 class="table-header-title">{{ 'PERMISSIONS.ALL_PERMISSIONS' | translate }}</h3>
          </div>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()">
                <i *ngIf="!translationService.isRTL()"
                  class="fas fa-search position-absolute top-50 translate-middle-y text-muted"
                  [class.start-0]="!translationService.isRTL()" [class.end-0]="translationService.isRTL()"
                  [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"></i>
                <input type="text" class="form-control form-control-sm" [class.ps-10]="!translationService.isRTL()"
                  [class.pe-10]="translationService.isRTL()"
                  [placeholder]="'PERMISSIONS.SEARCH_PLACEHOLDER' | translate" [(ngModel)]="searchText"
                  (ngModelChange)="onSearchChange($event)"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-rounded table-striped border gy-7 gs-7">
              <thead class="bg-light">
                <tr class="fw-bold fs-6 text-gray-800 border-bottom-2 border-gray-200"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                  <th class="min-w-300px ps-4" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'PERMISSIONS.PERMISSION_DETAILS' | translate }}
                  </th>
                  <th class="min-w-150px" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'PERMISSIONS.CREATED_DATE' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="permissions.length === 0">
                  <td colspan="2" class="text-center py-10">
                    <div class="custom-empty-state" [class.arabic-empty-state]="translationService.isRTL()">
                      <div *ngIf="!translationService.isRTL()" class="empty-state-icon">
                        <div class="symbol symbol-100px mb-5">
                          <div class="symbol-label bg-light-primary">
                            <i class="fas fa-shield-alt text-primary fs-2x"></i>
                          </div>
                        </div>
                      </div>
                      <div class="empty-state-content">
                        <div class="empty-state-title">
                          {{ searchText ? ('PERMISSIONS.NO_PERMISSIONS_MATCH' | translate) :
                          ('PERMISSIONS.NO_PERMISSIONS_FOUND' | translate) }}
                        </div>
                        <div class="empty-state-subtitle">
                          {{ 'PERMISSIONS.NO_PERMISSIONS_DESCRIPTION' | translate }}
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr *ngFor="let permission of permissions" class="border-bottom border-gray-300">
                  <td class="ps-4" [class.pe-4]="translationService.isRTL()" [class.ps-4]="!translationService.isRTL()">
                    <div class="d-flex align-items-center permission-row-layout"
                      [class.arabic-layout]="translationService.isRTL()">
                      <div class="symbol symbol-50px permission-icon">
                        <div class="symbol-label bg-light-primary">
                          <i class="fas fa-shield-alt text-primary fs-2"></i>
                        </div>
                      </div>
                      <div class="d-flex flex-column permission-details"
                        [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                        <div class="text-primary fs-6 fw-semibold mb-1">{{ permission.name }}</div>
                        <div class="fw-bold text-gray-800 fs-5 mb-1">{{ permission.displayName }}</div>
                        <div class="text-muted fs-7" *ngIf="permission.description">{{ permission.description }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="text-muted fs-6 fw-semibold permission-date-column"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    <div class="d-flex flex-column" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                      <span>{{ permission.createdAt | date:'short' }}</span>
                      <span class="text-muted fs-7">{{ permission.createdAt | date:'mediumDate' }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="!loading && permissions.length > 0" class="d-flex justify-content-center mt-5 mb-5">
          <app-pagination [totalItems]="totalElements" [itemsPerPage]="pageSize" [currentPage]="currentPage"
            (pageChange)="onPageChange($event)"></app-pagination>
        </div>
      </div>
    </div>
  </div>
</div>
// RTL Support for Header Layout - Arabic specific behavior
.rtl-header {
  .d-flex {
    flex-direction: row-reverse !important;
    direction: rtl !important;

    &.justify-content-between {
      justify-content: space-between !important;
    }
  }
}

// RTL Title styling
.rtl-title {
  text-align: right !important;
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
  font-family: 'Noto Kufi Arabic', sans-serif !important;
  font-weight: 800 !important;
}

// RTL Search form styling
.rtl-search {
  .form-control {
    text-align: right !important;
    padding-right: 1rem !important;
    padding-left: 2.5rem !important;
  }

  app-keenicon {
    right: auto !important;
    left: 12px !important;
  }
}

.rtl-input {
  text-align: right !important;
  font-family: 'Hacen Liner Screen', sans-serif !important;
}

// RTL Button styling
.rtl-button {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  font-weight: 600 !important;

  i {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
}

// RTL Center for assign button
.rtl-center {
  text-align: center !important;
}

// Enhanced button styling
.btn-light-dark-blue {
  border-radius: 8px !important;
  font-size: 0.95rem !important;
  transition: all 0.3s ease !important;
  padding: 0.5rem 1rem !important;

  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  &:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Header layout improvements
.card-body {
  .d-flex.flex-wrap.flex-sm-nowrap {
    gap: 1rem;

    .flex-grow-1 {
      .d-flex.justify-content-between {
        gap: 1rem;
        align-items: center !important;

        // Title section
        .d-flex.my-4:first-child {
          margin: 0 !important;

          h1 {
            margin: 0 !important;
            line-height: 1.2 !important;
          }
        }

        // Search section
        .d-flex.my-4:nth-child(2) {
          margin: 0 !important;

          form {
            margin-bottom: 0 !important;
          }
        }

        // Buttons section
        .d-flex.my-4:last-child {
          margin: 0 !important;
          gap: 0.5rem;

          .btn {
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// Strong Override for Arabic layout - Title left, Buttons right
:host-context(html[dir="rtl"]) .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2.rtl-header,
:host-context(html[lang="ar"]) .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2.rtl-header {
  flex-direction: row-reverse !important;
  justify-content: space-between !important;
  gap: 10px !important;
  direction: rtl !important;

  // First child - Title section (should be on the far left in Arabic)
  > .d-flex.my-4:first-child {
    order: 3 !important; // Move to far left
    margin: 0.5rem 0 !important;

    h1.rtl-title {
      text-align: right !important;
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }

  // Second child - Search section (should be in the middle)
  > .d-flex.my-4:nth-child(2) {
    order: 2 !important;
    margin: 0.5rem 0 !important;

    form.rtl-search {
      direction: rtl !important;

      .form-control.rtl-input {
        text-align: right !important;
        direction: rtl !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
      }
    }
  }

  // Third child - Buttons section (should be on the far right in Arabic)
  > .d-flex.my-4:last-child {
    order: 1 !important; // Move to far right
    margin: 0.5rem 0 !important;
    gap: 0.5rem !important;

    .btn.rtl-button {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      text-align: center !important;

      i {
        margin-right: 0.5rem !important;
        margin-left: 0 !important;
      }
    }
  }
}

// Additional override for any nested flex containers
:host-context(html[dir="rtl"]) .rtl-header .d-flex,
:host-context(html[lang="ar"]) .rtl-header .d-flex {
  flex-direction: row-reverse !important;
}

// Override the existing RTL header behavior for Arabic
.rtl-header {
  direction: rtl !important;

  .d-flex {
    flex-direction: row-reverse !important;

    &.justify-content-between {
      justify-content: space-between !important;
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 800 !important;
  }

  .btn {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-weight: 600 !important;
  }

  .form-control {
    font-family: 'Hacen Liner Screen', sans-serif !important;
  }

  // Header specific adjustments for Arabic
  .rtl-header {
    .d-flex.justify-content-between {
      flex-wrap: wrap !important;
      gap: 1rem !important;

      > div {
        flex: 0 0 auto !important;
      }
    }
  }

  // ===== RTL RESPONSIVE ADJUSTMENTS =====

  // Medium screens RTL (768px - 991px)
  @media screen and (min-width: 768px) and (max-width: 991px) {
    .rtl-header {
      .d-flex.my-4 {
        h1.rtl-title {
          text-align: center !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }

        form.rtl-search {
          .form-control.rtl-input {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
          }
        }

        .btn.rtl-button {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: center !important;
        }
      }
    }
  }

  // Small-medium screens RTL (576px - 767px)
  @media screen and (min-width: 576px) and (max-width: 767px) {
    .rtl-header {
      .d-flex.my-4 {
        h1.rtl-title {
          text-align: center !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }

        form.rtl-search {
          .form-control.rtl-input {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
          }
        }

        .btn.rtl-button {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: center !important;
        }
      }
    }
  }

  // Small screens RTL (425px - 575px)
  @media screen and (min-width: 425px) and (max-width: 575px) {
    .rtl-header {
      .d-flex.my-4 {
        h1.rtl-title {
          text-align: center !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }

        form.rtl-search {
          .form-control.rtl-input {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
          }
        }

        .btn.rtl-button {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: center !important;
        }
      }
    }
  }

  // Extra small screens RTL (≤424px)
  @media screen and (max-width: 424px) {
    .rtl-header {
      .d-flex.my-4 {
        h1.rtl-title {
          text-align: center !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }

        form.rtl-search {
          .form-control.rtl-input {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
          }
        }

        .btn.rtl-button {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: center !important;
        }
      }
    }
  }
}

// ===== COMPREHENSIVE RESPONSIVE DESIGN =====

// Large screens (1200px and above) - Normal layout
@media screen and (min-width: 1200px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        flex-wrap: nowrap !important;
        gap: 2rem !important;

        .d-flex.my-4 {
          margin: 1rem 0 !important;

          h1 {
            font-size: 1.75rem !important;
          }

          form {
            width: 350px !important;
          }

          .btn {
            padding: 0.6rem 1.2rem !important;
            font-size: 0.9rem !important;
          }
        }
      }
    }
  }
}

// Medium-large screens (992px - 1199px)
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        gap: 1.5rem !important;

        .d-flex.my-4 {
          margin: 0.75rem 0 !important;

          h1 {
            font-size: 1.5rem !important;
          }

          form {
            width: 300px !important;
          }

          .btn {
            padding: 0.5rem 1rem !important;
            font-size: 0.85rem !important;
          }
        }
      }
    }
  }
}

// Medium screens (768px - 991px) - Start responsive behavior
@media screen and (min-width: 768px) and (max-width: 991px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;

        // Title first (top)
        .d-flex.my-4:first-child {
          order: 1 !important;
          margin: 0.5rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          h1 {
            font-size: 1.4rem !important;
            text-align: center !important;
          }
        }

        // Search second (middle)
        .d-flex.my-4:nth-child(2) {
          order: 2 !important;
          margin: 0.5rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          form {
            width: 100% !important;
            max-width: 400px !important;

            .form-control {
              font-size: 0.9rem !important;
            }
          }
        }

        // Buttons third (bottom)
        .d-flex.my-4:last-child {
          order: 3 !important;
          margin: 0.5rem 0 !important;
          width: 100% !important;
          justify-content: center !important;
          gap: 0.5rem !important;
          display: flex !important;
          align-items: center !important;

          .btn {
            padding: 0.5rem 0.8rem !important;
            font-size: 0.8rem !important;
            flex: 0 0 auto !important;
            max-width: 100px !important;
            min-width: 80px !important;
            margin: 0 !important;

            // Remove all margin classes
            &.me-3, &.ms-3 {
              margin: 0 !important;
            }
          }
        }
      }
    }
  }
}

// Small-medium screens (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        flex-direction: column !important;
        align-items: center !important;
        gap: 0.75rem !important;

        // Title first (top)
        .d-flex.my-4:first-child {
          order: 1 !important;
          margin: 0.4rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          h1 {
            font-size: 1.25rem !important;
            text-align: center !important;
          }
        }

        // Search second (middle)
        .d-flex.my-4:nth-child(2) {
          order: 2 !important;
          margin: 0.4rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          form {
            width: 100% !important;
            max-width: 350px !important;

            .form-control {
              font-size: 0.85rem !important;
              padding: 0.6rem 1rem !important;
            }
          }
        }

        // Buttons third (bottom)
        .d-flex.my-4:last-child {
          order: 3 !important;
          margin: 0.4rem 0 !important;
          width: 100% !important;
          justify-content: center !important;
          gap: 0.4rem !important;
          display: flex !important;
          align-items: center !important;

          .btn {
            padding: 0.45rem 0.7rem !important;
            font-size: 0.75rem !important;
            flex: 0 0 auto !important;
            max-width: 90px !important;
            min-width: 70px !important;
            margin: 0 !important;

            // Remove all margin classes
            &.me-3, &.ms-3 {
              margin: 0 !important;
            }
          }
        }
      }
    }
  }
}

// Small screens (425px - 575px)
@media screen and (min-width: 425px) and (max-width: 575px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        flex-direction: column !important;
        align-items: center !important;
        gap: 0.6rem !important;

        // Title first (top)
        .d-flex.my-4:first-child {
          order: 1 !important;
          margin: 0.3rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          h1 {
            font-size: 1.1rem !important;
            text-align: center !important;
          }
        }

        // Search second (middle)
        .d-flex.my-4:nth-child(2) {
          order: 2 !important;
          margin: 0.3rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          form {
            width: 100% !important;
            max-width: 300px !important;

            .form-control {
              font-size: 0.8rem !important;
              padding: 0.5rem 0.8rem !important;
            }
          }
        }

        // Buttons third (bottom)
        .d-flex.my-4:last-child {
          order: 3 !important;
          margin: 0.3rem 0 !important;
          width: 100% !important;
          justify-content: center !important;
          gap: 0.3rem !important;
          display: flex !important;
          align-items: center !important;

          .btn {
            padding: 0.4rem 0.6rem !important;
            font-size: 0.7rem !important;
            flex: 0 0 auto !important;
            max-width: 80px !important;
            min-width: 60px !important;
            margin: 0 !important;

            // Remove all margin classes
            &.me-3, &.ms-3 {
              margin: 0 !important;
            }
          }
        }
      }
    }
  }
}

// Extra small screens (≤424px)
@media screen and (max-width: 424px) {
  .d-flex.flex-wrap.flex-sm-nowrap.mb-1 {
    .flex-grow-1 {
      .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
        flex-direction: column !important;
        align-items: center !important;
        gap: 0.5rem !important;

        // Title first (top)
        .d-flex.my-4:first-child {
          order: 1 !important;
          margin: 0.25rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          h1 {
            font-size: 1rem !important;
            text-align: center !important;
          }
        }

        // Search second (middle)
        .d-flex.my-4:nth-child(2) {
          order: 2 !important;
          margin: 0.25rem 0 !important;
          width: 100% !important;
          justify-content: center !important;

          form {
            width: 100% !important;
            max-width: 280px !important;

            .form-control {
              font-size: 0.75rem !important;
              padding: 0.45rem 0.7rem !important;
            }
          }
        }

        // Buttons third (bottom)
        .d-flex.my-4:last-child {
          order: 3 !important;
          margin: 0.25rem 0 !important;
          width: 100% !important;
          justify-content: center !important;
          gap: 0.25rem !important;
          display: flex !important;
          align-items: center !important;

          .btn {
            padding: 0.35rem 0.5rem !important;
            font-size: 0.65rem !important;
            flex: 0 0 auto !important;
            max-width: 70px !important;
            min-width: 55px !important;
            margin: 0 !important;

            // Remove all margin classes
            &.me-3, &.ms-3 {
              margin: 0 !important;
            }
          }
        }
      }
    }
  }
}

// ===== BUTTON SPACING FIX FOR RESPONSIVE SCREENS =====

// Override button margins for responsive screens (768px to 425px)
@media screen and (min-width: 425px) and (max-width: 768px) {
  .d-flex.my-4:last-child {
    .btn {
      // Force remove all margins
      margin-left: 0 !important;
      margin-right: 0 !important;

      &.me-3, &.ms-3 {
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
    }
  }
}

// Additional override for all responsive screens
@media screen and (max-width: 991px) {
  .d-flex.justify-content-between.align-items-start.flex-wrap.mb-2 {
    .d-flex.my-4:last-child {
      // Center the button container
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;

      .btn {
        // Remove Bootstrap margin classes
        margin: 0 !important;

        &.me-3, &.ms-3 {
          margin: 0 !important;
        }
      }
    }
  }
}

import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from '../base-grid/base-grid.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { TranslationService } from 'src/app/modules/i18n';
import { Subject } from 'rxjs';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { AccountsService } from './services/accounts.service';
import { environment } from 'src/environments/environment';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { PaginationComponent } from 'src/app/pagination/pagination.component';

@Component({
  selector: 'app-accounts',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    TranslateModule,
    FormsModule,
    RouterModule,
    // NgApexchartsModule,
    PaginationComponent,

  ],
  templateUrl: './accounts.component.html',
  styleUrl: './accounts.component.scss'
})
export class AccountsComponent extends BaseGridComponent {
  parentId: number;
  searchInput: string = '';
  isModalVisible = false;
  accountForm!: FormGroup;
  roles: any[] = [];

  constructor(
    protected cd: ChangeDetectorRef,
    private accountsService: AccountsService,
    private fb: FormBuilder,
    public translationService: TranslationService
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.parentId = user?.id;
    this.setService(accountsService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    // this.initForms();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.loadRoles();
    this.accountForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.email]],
      phone: ['', Validators.required],
      password: ['', [Validators.required, Validators.minLength(6)]],
      gender: ['', Validators.required],
      role: ['', Validators.required],
      parentId: [null]
    });
  }

  onSearch(event: any) {
    this.searchInput = event;
    this.page.pageNumber = 0;
    this.page.filters = { 'name': this.searchInput };
    console.log(event);
    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.page.filters = { ...this.page.filters, 'parentId': this.parentId }
    this.loading = true;

    await this._service.getAll(this.page).subscribe(
      (response: any) => {
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        console.log(this.rows);

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.error('Error loading data:', error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire(
          this.getTranslatedText('FAILED_TO_LOAD_DATA'),
          '',
          'error'
        );
      }
    );
  }

  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'ACCOUNTS': 'الحسابات الفرعية',
        'ALL_DEVELOPERS': 'جميع المطورين',
        'CONTRACTED': 'متعاقد',
        'NOT_CONTRACTED': 'غير متعاقد',
        'SEND_CONTRACT_REQUEST': 'إرسال طلب عقد',
        'SEARCH_DEVELOPERS': 'البحث في المطورين...',
        'SEARCH_USERS': 'البحث في المستخدمين...',
        'DEVELOPER_NAME': 'اسم المطور',
        'USER_NAME': 'الاسم',
        'ROLE': 'الأدوار',
        'PHONE': 'الهاتف',
        'EMAIL': 'البريد الإلكتروني',
        'PASSWORD': 'كلمة المرور',
        'GENDER': 'الجنس',
        'MALE': 'ذكر',
        'FEMALE': 'أنثى',
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'STATUS': 'الحالة',
        'ACTIVE': 'نشط',
        'INACTIVE': 'غير نشط',
        'SELECT': 'اختر',
        'ACTIONS': 'الإجراءات',
        'CONTRACT_REQUEST': 'طلب عقد',
        'UPLOAD_DOCUMENTS': 'رفع المستندات',
        'PERSONAL_IMAGE': 'الصورة الشخصية',
        'ID_FRONT': 'صورة البطاقة الأمامية',
        'ID_BACK': 'صورة البطاقة الخلفية',
        'UPLOAD_FILES': 'رفع الملفات',
        'SEND_REQUEST': 'إرسال الطلب',
        'CANCEL': 'إلغاء',
        'SUCCESS': 'تم بنجاح',
        'ERROR': 'خطأ!',
        'CONTRACT_REQUEST_SUCCESS': 'تم إرسال طلب العقد بنجاح!',
        'CONTRACT_REQUEST_ERROR': 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.',
        'FAILED_TO_LOAD_DATA': 'فشل في تحميل البيانات. يرجى المحاولة مرة أخرى لاحق.',
        'OK': 'موافق',
        'FILES_UPLOADED': 'ملف مرفوع',
        'NO_FILES': 'لا توجد ملفات',
        'CONTRACT_DURATION': 'مدة العقد',
        'CONTRACT_DATE': 'تاريخ العقد',
        'CONTRACT_END_DATE': 'تاريخ انتهاء العقد',
        'PROJECTS': 'مشاريع',
        'NO_OF_PROJECTS': 'عدد المشاريع',
        'FILTER': 'تصفية',
        'ADD_DEVELOPER': 'اضافة حساب',
        'VIEW_DEVELOPER': 'عرض المطور',
        'SEND_CONTRACT_REQUEST_MODAL': 'إرسال طلب عقد',
        'UPLOAD_DOCUMENTS_DESC': 'يرجى رفع المستندات المطلوبة',
        'PROFILE_PICTURE': 'الصورة الشخصية',
        'NATIONAL_ID_FRONT': 'صورة البطاقة الأمامية',
        'NATIONAL_ID_BACK': 'صورة البطاقة الخلفية',
        'FILE_SIZE': 'حجم الملف: حتى 5 ميجابايت',
        'ACCOUNT_ADDED_SUCCESSFULLY' : 'تمت اضافة الحساب بنجاح'
      },
      'en': {
        'ACCOUNTS': 'Accounts',
        'ALL_DEVELOPERS': 'All Developers',
        'CONTRACTED': 'Contracted',
        'NOT_CONTRACTED': 'Not Contracted',
        'SEND_CONTRACT_REQUEST': 'Send Contract Request',
        'SEARCH_DEVELOPERS': 'Search developers...',
        'SEARCH_USERS': 'Search users...',
        'DEVELOPER_NAME': 'Developer Name',
        'USER_NAME': 'Name',
        'ROLE': 'Role',
        'PHONE': 'Phone',
        'EMAIL': 'Email',
        'PASSWORD': 'Password',
        'GENDER': 'Gender',
        'MALE': 'Male',
        'FEMALE': 'Female',
        'CITY': 'City',
        'AREA': 'Area',
        'STATUS': 'Status',
        'ACTIVE': 'Active',
        'INACTIVE': 'Inactive',
        'SELECT': 'Select',
        'ACTIONS': 'Actions',
        'CONTRACT_REQUEST': 'Contract Request',
        'UPLOAD_DOCUMENTS': 'Upload Documents',
        'PERSONAL_IMAGE': 'Personal Image',
        'ID_FRONT': 'ID Front',
        'ID_BACK': 'ID Back',
        'UPLOAD_FILES': 'Upload Files',
        'SEND_REQUEST': 'Send Request',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success!',
        'ERROR': 'Error!',
        'CONTRACT_REQUEST_SUCCESS': 'Contract request submitted successfully!',
        'CONTRACT_REQUEST_ERROR': 'An error occurred while sending the contract request.',
        'FAILED_TO_LOAD_DATA': 'Failed to load data. Please try again later.',
        'OK': 'OK',
        'FILES_UPLOADED': 'file(s) uploaded',
        'NO_FILES': 'No files',
        'CONTRACT_DURATION': 'Contract Duration',
        'CONTRACT_DATE': 'Contract Date',
        'CONTRACT_END_DATE': 'Contract End Date',
        'PROJECTS': 'projects',
        'NO_OF_PROJECTS': 'Number of Projects',
        'FILTER': 'Filter',
        'ADD_DEVELOPER': 'Add Account',
        'VIEW_DEVELOPER': 'View Developer',
        'SEND_CONTRACT_REQUEST_MODAL': 'Send Contract Request',
        'UPLOAD_DOCUMENTS_DESC': 'Please upload the required documents',
        'PROFILE_PICTURE': 'Profile Picture',
        'NATIONAL_ID_FRONT': 'National ID Front',
        'NATIONAL_ID_BACK': 'National ID Back',
        'FILE_SIZE': 'File size: up to 5MB',
        'ACCOUNT_ADDED_SUCCESSFULLY' : 'Account Added Successfully'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  onPageChange(newPageNumber: number) {
    this.page.pageNumber = newPageNumber;
    this.reloadTable(this.page);
  }

  openModal() {
    this.isModalVisible = true;
  }

  closeModal() {
    this.isModalVisible = false;
  }

  loadRoles(): void {
    this._service.getRoles().subscribe({
      next: (response: any) => {
        console.log(response.data);
        this.roles = response.data.filter((role: any) =>
          !['broker', 'developer', 'admin', 'client'].includes(role.name)
        );
      },
      error: (err: any) => {
        console.error('Error loading Roles:', err);
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }


  submitForm() {
    this.accountForm.patchValue({ parentId: this.parentId });
    if (this.accountForm.valid) {
      console.log('Form Submitted:', this.accountForm.value);
      this._service.createChild(this.accountForm.value).subscribe(
        async (response: any) => {
          await Swal.fire({
            title: this.getTranslatedText('SUCCESS'),
            text: this.getTranslatedText('ACCOUNT_ADDED_SUCCESSFULLY'),
            confirmButtonText: this.getTranslatedText('OK')
          });
          this.cd.detectChanges();
          document.location.reload();
        },
        (error: any) => {
          this.cd.markForCheck();
          this.loading = false;
          Swal.fire(
            this.getTranslatedText('FAILED_TO_LOAD_DATA'),
            '',
            'error'
          );
        }
      );
      this.closeModal();
    } else {
      this.accountForm.markAllAsTouched();
    }
  }
  getTranslatedStatus(isActive: boolean): string {
    return isActive ? this.getTranslatedText('ACTIVE') : this.getTranslatedText('INACTIVE');
  }

  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge-light-success' : 'badge-light-danger';
  }

}

// ===== RTL SUPPORT FOR RECEIVED REQUESTS =====

// Arabic font support
:host-context(html[lang="ar"]) {
  .btn {
    font-family: 'Markazi Text', sans-serif;
    font-size: 1rem;
    font-weight: 600;
  }

  .rtl-button {
    direction: rtl;
    text-align: center;

    i {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
    }
  }

  // Table headers
  th {
    font-family: 'Noto Kufi Arabic', sans-serif;
    text-align: right !important;
  }

  // Table cells
  td {
    font-family: 'Markazi Text', sans-serif;
    text-align: right !important;
  }

  // Pagination
  .pagination {
    direction: rtl;

    .page-link {
      font-family: 'Markazi Text', sans-serif;
    }
  }

  // Search and filters
  .form-control {
    text-align: right;
    font-family: 'Markazi Text', sans-serif;
  }

  .form-select {
    text-align: right;
    font-family: 'Markazi Text', sans-serif;
  }

  // Action buttons
  .dropdown-menu {
    direction: rtl;
    text-align: right;

    .dropdown-item {
      font-family: 'Markazi Text', sans-serif;
      text-align: right;

      i {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }
    }
  }
}

// ===== RESPONSIVE DESIGN =====

// Medium screens (768px - 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .m-2 {
    margin: 1rem !important;
    text-align: center;

    .btn {
      width: 100%;
      padding: 0.75rem 1.5rem;
      font-size: 0.95rem;

      i {
        font-size: 1rem;
      }
    }
  }
}

// Small screens (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .m-2 {
    margin: 0.75rem !important;
    text-align: center;

    .btn {
      width: 100%;
      padding: 0.6rem 1.25rem;
      font-size: 0.9rem;

      i {
        font-size: 0.9rem;
      }
    }
  }
}

// Extra small screens (≤575px)
@media screen and (max-width: 575px) {
  .m-2 {
    margin: 0.5rem !important;
    text-align: center;

    .btn {
      width: 100%;
      padding: 0.5rem 1rem;
      font-size: 0.85rem;

      i {
        font-size: 0.8rem;
      }
    }
  }
}

// ===== BUTTON ENHANCEMENTS =====

.btn-primary {
  background-color: #009ef7;
  border-color: #009ef7;
  transition: all 0.3s ease;

  &:hover {
    background-color: #0077b6;
    border-color: #0077b6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 158, 247, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 158, 247, 0.2);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
  }

  i {
    transition: transform 0.2s ease;
  }

  &:hover i {
    transform: scale(1.1);
  }
}

// RTL button specific styles
:host-context(html[lang="ar"]) {
  .btn-primary {
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 158, 247, 0.3);
    }
  }
}

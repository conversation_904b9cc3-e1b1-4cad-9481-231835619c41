.home-page {
  background-color: #ffffff !important;
  padding-top: 90px; // Add padding to account for fixed navbar
}

// Home Header Styles
.home-header {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  font-family: "Noto Kufi Arabic" !important;

  // Navigation Bar
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.568);

    .container-fluid {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .navbar-brand {
      .logo-img {
        height: 50px;
        width: auto;
        object-fit: contain;
      }
    }

    .nav-list {
      list-style: none;
      gap: 2rem;
      margin: 0;
      padding: 0;

      .nav-item {
        .nav-link {
          color: #2c3e50;
          text-decoration: none;
          font-weight: 700;
          font-size: 1rem;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          transition: all 0.3s ease;
          direction: rtl;
          font-family: "Noto Kufi Arabic" !important;

          &:hover {
            background-color: #f8f9fa;
            color: #27ae60;
            transform: translateY(-2px);
          }

          &.user-link {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 25px;
            padding: 0.7rem 1.5rem;

            &:hover {
              background: linear-gradient(135deg, #229954, #27ae60);
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            }

            i {
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    // User Registration Link (separate from nav list)
    .user-link {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white !important;
      border-radius: 25px;
      padding: 0.7rem 1.5rem;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      font-family: "Noto Kufi Arabic" !important;

      &:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        color: white !important;
        text-decoration: none;
      }

      i {
        font-size: 0.9rem;
      }
    }

    // Language Toggle Button
    .language-toggle {
      position: relative;

      .language-btn {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 25px;
        padding: 0.5rem 1rem;
        color: #2c3e50;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(52, 152, 219, 0.5);
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
          color: #2c3e50;
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
          color: #2c3e50;
        }

        .language-text {
          font-weight: 600;
        }

        i.fa-chevron-down {
          font-size: 0.8rem;
          color: #3498db;
          transition: transform 0.3s ease;
        }
      }

      // Language Dropdown
      .language-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        min-width: 160px;
        z-index: 1000;
        border: 1px solid rgba(0, 0, 0, 0.08);
        overflow: hidden;
        animation: dropdownFadeIn 0.3s ease;
        display: none;

        .language-option {
          display: flex;
          align-items: center;
          padding: 10px 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);

          &:hover {
            background-color: #f8f9fa;
            transform: translateX(-3px);
          }

          &.active {
            background-color: #e3f2fd;

            span {
              color: #1976d2;
              font-weight: 600;
            }
          }

          &:last-child {
            border-bottom: none;
          }

          i {
            width: 18px;
            text-align: center;
            font-size: 0.9rem;
          }

          span {
            font-weight: 500;
            color: #2c3e50;
            font-size: 0.9rem;
          }
        }
      }

      // Show dropdown on hover (optional)
      &:hover .language-dropdown {
        display: block;
      }
    }

    // User Profile (when logged in)
    .user-profile {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      padding: 0.5rem 1rem;
      border: 1px solid rgba(250, 250, 250, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;
      font-family: "Noto Kufi Arabic" !important;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(39, 174, 96, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
      }

      .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #27ae60;
      }

      .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1.4rem;
      }

      i.fa-chevron-down {
        font-size: 0.8rem;
        color: #27ae60;
        transition: transform 0.3s ease;
      }
    }

    // User Dropdown Menu
    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 10px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      min-width: 220px;
      z-index: 1000;
      border: 1px solid rgba(0, 0, 0, 0.08);
      overflow: hidden;
      animation: dropdownFadeIn 0.3s ease;
      font-family: "Noto Kufi Arabic" !important;

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 10px 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(-3px);
        }

        i,
        .menu-icon {
          width: 18px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .menu-icon {
          svg {
            width: 16px;
            height: 16px;
          }
        }

        span {
          font-weight: 500;
          color: #2c3e50;
          font-size: 0.9rem;
        }

        &.logout-item {
          &:hover {
            background-color: #fff5f5;
          }

          span {
            color: #e74c3c;
          }
        }

        &.new-request-item {
          background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
          border-top: 2px solid #27ae60;

          &:hover {
            background: linear-gradient(135deg, #d4f4d4, #e8f5e8);
          }

          span {
            color: #27ae60;
            font-weight: 600;
            text-align: center;
            width: 100%;
          }
        }
      }

      .dropdown-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0;
      }
    }

    @keyframes dropdownFadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  // Hero Section
  .hero-section {
    position: relative;
    height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 1px;
    // margin-left: 10px;
    // margin-right: 10px;

    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      overflow: hidden;

      .hero-bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;

        transition: transform 0.3s ease;
      }

      &:hover .hero-bg-image {
        transform: scale(1.02);
      }

      .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        // background: linear-gradient(
        //   135deg,
        //   rgba(38, 83, 147, 0.515) 0%,
        //   rgba(52, 73, 94, 0.5) 30%,
        //   rgba(39, 174, 95, 0.518) 70%,
        //   rgba(46, 204, 113, 0.8) 100%
        // );
        // backdrop-filter: blur(2px);
      }
    }

    .hero-content {
      position: relative;
      z-index: 5;
      width: 100%;

      .hero-text-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 4rem;

        .hero-text-item {
          animation: fadeInUp 1s ease-out;

          &:nth-child(1) {
            text-align: right;
            animation-delay: 0.2s;
          }

          &:nth-child(2) {
            text-align: center;
            animation-delay: 0.4s;
          }

          &:nth-child(3) {
            text-align: left;
            animation-delay: 0.6s;
          }

          .hero-text {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5),
              2px 2px 8px rgba(0, 0, 0, 0.7), 4px 4px 15px rgba(0, 0, 0, 0.4);
            margin: 0;
            padding: 1.5rem 2.5rem;
            border-radius: 20px;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2),
              0 4px 16px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;

            &::before {
              content: "";
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
              );
              transition: left 0.6s ease;
            }

            &:hover {
              transform: translateY(-8px) scale(1.08);
              background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 100%
              );
              box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
              text-shadow: 0 0 30px rgba(255, 255, 255, 0.8),
                2px 2px 10px rgba(0, 0, 0, 0.8), 4px 4px 20px rgba(0, 0, 0, 0.5);

              &::before {
                left: 100%;
              }
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .home-header {
    .hero-section {
      margin-left: 20px;
      margin-right: 20px;
      border-radius: 20px;

      .hero-content {
        .hero-text-container {
          padding: 0 2rem;

          .hero-text-item {
            .hero-text {
              font-size: 2.5rem;
              padding: 1.2rem 2rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .home-page {
    padding-top: 70px; // Reduced padding for mobile
  }

  .home-header {
    .navbar {
      .container-fluid {
        flex-direction: column;
        gap: 1rem;
      }

      .nav-list {
        gap: 1rem;
        flex-direction: column;
        text-align: center;

        .nav-item {
          .nav-link {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
          }
        }
      }

      .user-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
      }

      .user-profile {
        padding: 0.4rem 0.8rem;

        .user-avatar {
          width: 30px;
          height: 30px;
        }

        .user-name {
          font-size: 0.85rem;
        }
      }

      .user-dropdown {
        min-width: 200px;
        right: -15px;

        .dropdown-item {
          padding: 8px 12px;

          span {
            font-size: 0.8rem;
          }

          i,
          .menu-icon {
            width: 16px;
          }

          .menu-icon {
            svg {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }

    .hero-section {
      margin-left: 15px;
      margin-right: 15px;
      margin-top: 15px;
      border-radius: 15px;

      .hero-content {
        .hero-text-container {
          flex-direction: column;
          gap: 1.5rem;
          padding: 0 1rem;

          .hero-text-item {
            text-align: center !important;

            .hero-text {
              font-size: 2rem;
              padding: 1rem 1.5rem;
              border-radius: 15px;
              box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2),
                0 3px 10px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .home-page {
    padding-top: 60px; // Further reduced padding for small mobile
  }

  .home-header {
    .navbar {
      .navbar-brand {
        .logo-img {
          height: 40px;
        }
      }
    }

    .hero-section {
      margin-left: 10px;
      margin-right: 10px;
      margin-top: 10px;
      border-radius: 12px;

      .hero-content {
        .hero-text-container {
          padding: 0 0.5rem;

          .hero-text-item {
            .hero-text {
              font-size: 1.5rem;
              padding: 0.8rem 1.2rem;
              border-radius: 12px;
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

// Mobile Navigation Styles
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 123, 255, 0.1);
  }

  &:focus {
    box-shadow: none;
    outline: none;
  }

  .navbar-toggler-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 1.2rem;
      color: #333;
      transition: all 0.3s ease;
    }
  }
}

// Mobile Navigation Dropdown
.mobile-nav-dropdown {
  position: absolute;
  top: calc(100% + -1.8rem) !important;
  left: 50%;
  transform: translateX(-50%);
  margin-left: -25px !important;
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1040;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: visible;
  animation: dropdownFadeIn 0.3s ease;
  margin-top: 0;
  font-family: "Noto Kufi Arabic" !important;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(0, 123, 255, 0.05);
      color: #007bff;
    }

    .menu-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;

      i {
        font-size: 0.8rem;
      }
    }

    span {
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

// App Footer Exact - Copy of Image
.app-footer-exact {
  background-color: #f5f5f5;
  padding: 20px 0;
  border-top: 1px solid #ddd;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  // App Buttons - Left Side
  .app-buttons-left {
    display: flex;
    flex-direction: column;
    gap: 8px;

    img {
      height: 40px;
      width: auto;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // Download Text - Center
  .download-text-center {
    flex: 1;
    text-align: center;

    h1 {
      font-size: 2.2rem;
      font-weight: bold;
      color: #00c851;
      margin: 0;
      font-family: "Noto Kufi Arabic", sans-serif;
    }
  }

  // Logo and Info - Right Side
  .logo-info-right {
    text-align: right;
    max-width: 300px;

    .logo {
      height: 50px;
      width: auto;
      margin-bottom: 10px;
      display: block;
      margin-left: auto;
      margin-right: 0;
    }

    .company-name {
      font-size: 0.95rem;
      font-weight: 600;
      color: #333;
      margin: 5px 0;
      font-family: "Noto Kufi Arabic";
      line-height: 1.3;
    }

    .copyright {
      font-size: 0.85rem;
      color: #666;
      margin: 3px 0;
      font-family: "Noto Kufi Arabic";
    }

    .email {
      font-size: 0.85rem;
      color: #666;
      margin: 3px 0 10px 0;
    }

    .social-icons {
      display: flex !important;
      gap: 8px;
      justify-content: flex-end;

      i {
        width: 30px !important;
        height: 30px !important;
        background-color: #1a365d !important;
        color: white !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 14px !important;
        cursor: pointer !important;
        transition: background-color 0.3s ease !important;
        visibility: visible !important;
        opacity: 1 !important;

        &:hover {
          background-color: #2c5282 !important;
        }

        // Override RTL styles specifically for FontAwesome icons
        &.fab,
        &.fas,
        &.far,
        &.fal,
        &.fad {
          display: flex !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
      }
    }
  }

  // Responsive
  @media (max-width: 768px) {
    .footer-content {
      flex-direction: column;
      text-align: center;
      gap: 20px;
    }

    .logo-info-right {
      text-align: center;
      max-width: none;

      .logo {
        margin: 0 auto 10px;
      }

      .social-icons {
        justify-content: center;
      }
    }
  }
}

// Newsletter Section
.newsletter-section {
  background: #f5f5f5;
  .newsletter-card {
    // background: white;
    // border-radius: 20px;
    // padding: 40px;
    // box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    // border: 1px solid #e9ecef;

    .newsletter-content {
      .newsletter-text-section {
        display: flex;
        align-items: center;
        gap: 20px;
        white-space: nowrap;

        .email-icon-container {
          flex-shrink: 0;

          .email-icon {
            width: 70px;
            height: 70px;
            margin-top: 10px;
            object-fit: contain;
          }
        }

        .newsletter-title {
          font-size: 1.4rem;
          font-weight: 700;
          color: #000c66;
          margin: 0;
          font-family: "Noto Kufi Arabic";
          white-space: nowrap;
          flex-shrink: 0;

          .green-text {
            color: #00ec42 !important;
          }
        }

        .newsletter-description {
          font-size: 0.9rem;
          color: #666;
          margin: 0;
          font-family: "Noto Kufi Arabic";
          white-space: nowrap;
          flex-shrink: 0;
        }

        .newsletter-input {
          flex: 2;
          min-width: 400px;
          max-width: 400px;
          padding: 15px 20px;
          border: 2px solid #e9ecef;
          border-radius: 4px;
          font-size: 1rem;
          height: 50px;

          &:focus {
            border-color: #000c66;
            box-shadow: 0 0 0 0.2rem rgba(0, 12, 102, 0.25);
          }

          &::placeholder {
            font-size: 0.9rem;
            color: #6c757d;
            overflow: visible;
          }
        }

        .newsletter-btn {
          background-color: #000c66;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 4px;
          font-weight: 600;
          font-size: 1rem;
          white-space: nowrap;
          flex-shrink: 0;
          transition: all 0.3s ease;

          &:hover {
            background-color: #00660f;
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// RTL Support for Newsletter
:host-context(html[dir="rtl"]) .newsletter-section,
:host-context(html[lang="ar"]) .newsletter-section {
  .newsletter-card {
    .newsletter-content {
      .newsletter-text-section {
        .newsletter-title,
        .newsletter-description {
          text-align: right;
        }

        .newsletter-input {
          margin-right: 100px;
        }
      }
    }
  }
}

// LTR Support for Newsletter
:host-context(html[dir="ltr"]) .newsletter-section,
:host-context(html[lang="en"]) .newsletter-section {
  .newsletter-card {
    .newsletter-content {
      .newsletter-text-section {
        .newsletter-title,
        .newsletter-description {
          text-align: left;
          font-family: "Markazi text", sans-serif;
        }

        .newsletter-title {
          .green-text {
            color: #00ec42 !important;
          }
        }

        .newsletter-input {
          margin-left: 100px;
        }
      }
    }
  }
}

// Responsive Design for Newsletter
@media (max-width: 768px) {
  .newsletter-section {
    .newsletter-card {
      padding: 20px;

      .newsletter-content {
        flex-direction: column;
        gap: 15px;

        .newsletter-text-section {
          flex-direction: column;
          gap: 15px;
          white-space: normal;
          text-align: center;

          .email-icon-container {
            .email-icon {
              width: 50px;
              height: 50px;
            }
          }

          .newsletter-title {
            font-size: 1.2rem;
            white-space: normal;
          }

          .newsletter-description {
            font-size: 0.85rem;
            white-space: normal;
          }

          .newsletter-input {
            min-width: auto;
            width: 100%;
            max-width: none;
            padding: 12px 16px;
            height: 45px;
            font-size: 0.9rem;

            &::placeholder {
              font-size: 0.8rem;
            }
          }

          .newsletter-btn {
            width: 100%;
          }
        }
      }
    }
  }
}

// Download App Footer Section - Updated
.download-app-footer {
  // background-color: #f8f9fa;
  // border-top: 1px solid #e9ecef;

  .row {
    align-items: center;
  }

  // Logo Section
  .logo-section {
    .footer-logo {
      height: 60px;
      width: auto;
      margin-bottom: 15px;
    }

    .contact-info {
      .contact-text {
        font-size: 1rem;
        font-weight: bold;
        color: #031752;
        margin-bottom: 8px;
        line-height: 1.4;
        font-family: "Noto Kufi Arabic";
      }

      .contact-details {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
        line-height: 1.3;
        font-family: "Noto Kufi Arabic";
      }

      .contact-email {
        font-size: 0.9rem;
        color: #000c66;
        margin-bottom: 15px;
      }

      .social-icons {
        display: flex;
        gap: 10px;
      }
    }
  }

  // Download Text Section
  .download-text-section {
    .download-title {
      font-size: 2.5rem;
      font-weight: 900;
      color: #00ec42;
      margin: 0;
      font-family: "Noto Kufi Arabic";
    }
  }

  // App Buttons Section
  .app-buttons-section {
    .app-buttons {
      align-items: flex-end;
      margin-left: 1%;

      .app-store-btn,
      .google-play-btn {
        height: 5.6rem;
        width: auto;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    // Footer Links
    .footer-links {
      .footer-link {
        font-size: 2.85rem !important;
        color: #666 !important; // Gray color for regular links
        cursor: pointer;
        transition: color 0.3s ease;
        font-family: "Noto Kufi Arabic", sans-serif !important;
        font-weight: 400 !important; // Regular weight for normal links
        text-decoration: none !important; // Remove underline
        display: inline-block;

        &:hover {
          color: #00660f !important;
          text-decoration: none !important;
        }

        &:focus,
        &:active,
        &:visited {
          text-decoration: none !important;
        }

        // Bold style for the first link (Home/الرئيسية)
        &.footer-link-bold {
          font-weight: 700 !important; // Bold weight
          color: #000c66 !important; // Keep original blue color for the bold link

          &:hover {
            color: #00660f !important;
          }
        }
      }
    }
  }
}

// Strong override for RTL FontAwesome hiding - Higher specificity
.app-footer-exact .social-icons i.fab,
.app-footer-exact .social-icons i.fas,
.app-footer-exact .social-icons i.far,
.app-footer-exact .social-icons i.fal,
.app-footer-exact .social-icons i.fad {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

// Even stronger override for RTL context
.rtl .app-footer-exact .social-icons i.fab,
.rtl .app-footer-exact .social-icons i.fas,
.rtl .app-footer-exact .social-icons i.far,
.rtl .app-footer-exact .social-icons i.fal,
.rtl .app-footer-exact .social-icons i.fad,
:host-context(.rtl) .app-footer-exact .social-icons i.fab,
:host-context(.rtl) .app-footer-exact .social-icons i.fas,
:host-context(.rtl) .app-footer-exact .social-icons i.far,
:host-context(.rtl) .app-footer-exact .social-icons i.fal,
:host-context(.rtl) .app-footer-exact .social-icons i.fad,
:host-context(html[dir="rtl"]) .app-footer-exact .social-icons i.fab,
:host-context(html[dir="rtl"]) .app-footer-exact .social-icons i.fas,
:host-context(html[dir="rtl"]) .app-footer-exact .social-icons i.far,
:host-context(html[dir="rtl"]) .app-footer-exact .social-icons i.fal,
:host-context(html[dir="rtl"]) .app-footer-exact .social-icons i.fad,
:host-context(html[lang="ar"]) .app-footer-exact .social-icons i.fab,
:host-context(html[lang="ar"]) .app-footer-exact .social-icons i.fas,
:host-context(html[lang="ar"]) .app-footer-exact .social-icons i.far,
:host-context(html[lang="ar"]) .app-footer-exact .social-icons i.fal,
:host-context(html[lang="ar"]) .app-footer-exact .social-icons i.fad {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 30px !important;
  height: 30px !important;
  background-color: #1a365d !important;
  color: white !important;
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
}

// App Buttons Section
.app-buttons-section {
  .app-buttons {
    display: flex;
    flex-direction: row;
    gap: 15px;
    align-items: flex-start;

    .app-store-btn,
    .google-play-btn {
      height: 72px;
      width: auto;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// Download Text Section
.download-text-section {
  text-align: center;

  .download-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #00ec42;
    margin: 0;
    font-family: "Noto Kufi Arabic";
  }
}

// Logo and Contact Section
.logo-contact-section {
  .logo-section {
    text-align: right;

    .footer-logo {
      height: 60px;
      width: auto;
      margin-bottom: 20px;
    }

    .contact-info {
      .contact-text {
        font-size: 1.1rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
        font-family: "Noto Kufi Arabic";
      }

      .contact-details {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 5px;
        font-family: "Noto Kufi Arabic";
      }

      .contact-email {
        font-size: 1.3rem;
        color: #666;
        margin-bottom: 15px;
      }

      .social-icons {
        display: flex;
        gap: 10px;
        // justify-content: flex-end;

        i {
          width: 35px;
          height: 35px;
          background-color: #000c66;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #001a99;
          }
        }
      }
    }
  }
}

// Bottom Section
.bottom-section {
  // margin-top: 30px;
  // padding-top: 20px;

  .links-section {
    .footer-links {
      display: flex;
      gap: 30px;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;

      span {
        color: #666;
        font-size: 0.9rem;
        cursor: pointer;
        font-family: "Noto Kufi Arabic";
        transition: color 0.3s ease;

        &:hover {
          color: #28a745;
        }
      }
    }
  }

  .join-section {
    .join-community {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 15px;

      .join-text {
        color: #666;
        font-size: 0.9rem;
        font-family: "Markazi text  " !important ;
      }

      .join-btn {
        background-color: #1e3a8a;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 5px;
        font-size: 0.9rem;
        cursor: pointer;
        font-family: "Markazi text";
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #1e40af;
        }
      }
    }
  }
}

// RTL Support for Download App Footer
:host-context(html[dir="rtl"]) .download-app-footer,
:host-context(html[lang="ar"]) .download-app-footer {
  // في العربية: اللوجو أولاً، النص في الوسط، الأزرار أخيراً
  .app-buttons-section {
    .app-buttons {
      align-items: flex-end;
      justify-content: flex-end;
    }

    // Footer Links Container for Arabic
    .footer-links-container {
      margin: 0 350px !important;
    }
  }

  .download-text-section {
    text-align: center;
  }

  .logo-contact-section {
    .logo-section {
      text-align: right;

      .contact-info {
        .social-icons {
          // justify-content: flex-end;
        }
      }
    }
  }

  .bottom-section {
    .links-section {
      .footer-links {
        justify-content: flex-end;
        flex-direction: row-reverse;
      }
    }

    .join-section {
      .join-community {
        justify-content: flex-start;
        flex-direction: row-reverse;
      }
    }
  }
}

// LTR Support for Download App Footer (English)
:host-context(html[dir="ltr"]) .download-app-footer,
:host-context(html[lang="en"]) .download-app-footer {
  .app-buttons-section {
    .app-buttons {
      align-items: flex-end;
      justify-content: flex-end;
      margin-right: 10%;
    }

    // Footer Links Container for English
    .footer-links-container {
      margin: 0 180px !important;
    }
  }

  .download-text-section {
    text-align: center;

    .download-title {
      font-family: "inter" !important;
    }
  }

  .logo-contact-section {
    .logo-section {
      text-align: left;

      .footer-logo {
        margin-left: 0;
        margin-right: auto;
      }

      .contact-info {
        text-align: left;

        .contact-text {
          font-family: "Inter" !important;
          color: #031752;
        }

        .contact-details {
          font-family: "Inter" !important;
        }

        .contact-email {
          color: #000c66;
        }

        .social-icons {
          justify-content: flex-start;
        }
      }
    }
  }

  // Footer Links for English
  .app-buttons-section {
    .footer-links {
      .footer-link {
        font-family: "Inter", sans-serif !important;
        font-weight: 400 !important; // Regular weight for normal links
        color: #666 !important; // Gray color for regular links
        text-decoration: none !important; // Remove underline
        display: inline-block;

        &:hover {
          color: #00660f !important;
          text-decoration: none !important;
        }

        &:focus,
        &:active,
        &:visited {
          text-decoration: none !important;
        }

        // Bold style for the first link (Home/الرئيسية)
        &.footer-link-bold {
          font-weight: 700 !important; // Bold weight
          color: #000c66 !important; // Keep original blue color for the bold link

          &:hover {
            color: #00660f !important;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  // Newsletter Section Mobile
  .newsletter-section {
    .newsletter-card {
      padding: 20px;

      .newsletter-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;

        .email-icon-container {
          .email-icon {
            width: 60px;
            height: 60px;
          }
        }

        .newsletter-text-section {
          .newsletter-title {
            font-size: 1.4rem;
          }

          .newsletter-description {
            font-size: 0.9rem;
          }

          .newsletter-form {
            flex-direction: column;
            gap: 15px;

            .newsletter-input {
              padding: 10px 14px;
            }

            .newsletter-btn {
              padding: 10px 20px;
            }
          }
        }
      }
    }
  }

  .download-app-footer {
    .download-text-section {
      .download-title {
        font-size: 2rem;
      }
    }

    .bottom-section {
      .links-section {
        .footer-links {
          justify-content: center;
          gap: 15px;
        }
      }

      .join-section {
        margin-top: 20px;

        .join-community {
          justify-content: center;
        }
      }
    }
  }
}

// Mobile dropdown size adjustments - removed since main CSS applies to all screens

@media (max-width: 576px) {
  .mobile-nav-dropdown {
    width: 180px !important;

    .dropdown-item {
      padding: 6px 10px !important;

      .menu-icon {
        width: 16px !important;
        height: 16px !important;

        i {
          font-size: 0.7rem !important;
        }
      }

      span {
        font-size: 0.75rem !important;
      }
    }
  }

  // Language toggle responsive
  .language-toggle {
    .language-btn {
      padding: 0.3rem 0.6rem !important;
      font-size: 0.75rem !important;
      min-width: 80px !important;

      .language-text {
        display: none; // Hide text on very small screens
      }

      i.fa-globe {
        font-size: 0.9rem !important;
      }

      i.fa-chevron-down {
        font-size: 0.6rem !important;
      }
    }

    .language-dropdown {
      min-width: 120px !important;
      right: -10px !important;

      .language-option {
        padding: 6px 8px !important;

        span {
          font-size: 0.75rem !important;
        }

        i {
          font-size: 0.75rem !important;
        }
      }
    }
  }

  // Mobile dropdown language toggle
  .mobile-language-toggle {
    padding: 6px 10px !important;
    display: flex !important;
    justify-content: center !important;
    position: relative !important;
    z-index: 1050 !important;

    .language-toggle {
      position: relative !important;
      z-index: 1051 !important;

      .language-btn {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.65rem !important;
        min-width: 60px !important;
        border: 1px solid rgba(40, 167, 69, 0.4) !important;
        background: rgba(40, 167, 69, 0.08) !important;
        border-radius: 12px !important;
        position: relative !important;
        z-index: 1052 !important;

        .language-text {
          display: none !important;
        }

        i.fa-globe {
          font-size: 0.75rem !important;
          color: #28a745 !important;
        }

        i.fa-chevron-down {
          font-size: 0.45rem !important;
          color: #28a745 !important;
        }

        &:hover {
          background: rgba(40, 167, 69, 0.15) !important;
          border-color: rgba(40, 167, 69, 0.6) !important;
        }
      }

      .language-dropdown {
        min-width: 90px !important;
        right: 0 !important;
        left: auto !important;
        margin-top: 4px !important;
        z-index: 1055 !important;
        position: absolute !important;

        .language-option {
          padding: 3px 5px !important;

          span {
            font-size: 0.65rem !important;
          }

          i {
            font-size: 0.65rem !important;
          }
        }
      }
    }
  }

  // RTL Language toggle fixes
  :host-context(html[dir="rtl"]) .language-toggle,
  :host-context(html[lang="ar"]) .language-toggle {
    .language-btn {
      min-width: 100px !important;
      padding: 0.4rem 0.8rem !important;

      .language-text {
        display: inline !important; // إظهار النص في العربية
        font-size: 0.8rem !important;
      }
    }

    .language-dropdown {
      min-width: 140px !important;
      left: -10px !important;
      right: auto !important;
    }
  }

  // RTL Mobile dropdown language toggle
  :host-context(html[dir="rtl"]) .mobile-language-toggle,
  :host-context(html[lang="ar"]) .mobile-language-toggle {
    .language-toggle {
      .language-btn {
        min-width: 70px !important;
        padding: 0.25rem 0.5rem !important;

        .language-text {
          display: none !important; // إخفاء النص في الموبايل حتى في العربية
        }
      }

      .language-dropdown {
        min-width: 100px !important;
        left: 0 !important;
        right: auto !important;
        z-index: 1055 !important;
      }
    }
  }

  // Ensure mobile dropdown doesn't get clipped
  .mobile-nav-dropdown .mobile-language-toggle {
    overflow: visible !important;

    .language-toggle {
      overflow: visible !important;

      .language-dropdown {
        position: fixed !important;
        top: auto !important;
        transform: translateY(0) !important;
      }
    }
  }
}

// Medium screens language toggle adjustments
@media (max-width: 768px) {
  .language-toggle {
    .language-btn {
      padding: 0.35rem 0.7rem !important;
      font-size: 0.8rem !important;
      min-width: 90px !important;

      .language-text {
        font-size: 0.75rem;
      }
    }

    .language-dropdown {
      min-width: 130px !important;
    }
  }
}

/* إصلاح انعكاس الأيقونات في Mobile Navigation Dropdown للعربية */
:host-context(html[dir="rtl"]) .mobile-nav-dropdown,
:host-context(html[lang="ar"]) .mobile-nav-dropdown {
  .dropdown-item {
    direction: rtl !important;
    text-align: right !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-direction: row !important;
    padding: 0.75rem 1rem !important;

    .menu-icon {
      margin-left: 0 !important;
      margin-right: auto !important;
      order: 999 !important; /* أقصى اليمين */
      flex-shrink: 0 !important;

      i {
        transform: none !important; /* منع انعكاس الأيقونات */
        direction: ltr !important;
        font-size: 1.1rem !important;
      }
    }

    span:not(.menu-icon) {
      order: 1 !important;
      font-family: "Noto Kufi Arabic" !important;
      letter-spacing: 0 !important;
      word-spacing: 0 !important;
      flex-grow: 1 !important;
      text-align: right !important;
    }

    a {
      order: 1 !important;
      font-family: "Noto Kufi Arabic" !important;
      letter-spacing: 0 !important;
      word-spacing: 0 !important;
      text-decoration: none !important;
      flex-grow: 1 !important;
      text-align: right !important;
    }
  }

  /* إصلاح الـ language toggle في الموبايل */
  .mobile-language-toggle {
    justify-content: center !important;
    flex-direction: row !important;
  }
}

/* منع انعكاس جميع الأيقونات في العربية */
:host-context(html[dir="rtl"]) i,
:host-context(html[lang="ar"]) i {
  transform: none !important;
  direction: ltr !important;
}

:host-context(html[dir="rtl"]) .fas,
:host-context(html[lang="ar"]) .fas,
:host-context(html[dir="rtl"]) .far,
:host-context(html[lang="ar"]) .far,
:host-context(html[dir="rtl"]) .fab,
:host-context(html[lang="ar"]) .fab {
  transform: none !important;
  direction: ltr !important;
}

/* إصلاح خاص للأيقونات في الموبايل */
@media (max-width: 991.98px) {
  :host-context(html[dir="rtl"]) .mobile-nav-dropdown,
  :host-context(html[lang="ar"]) .mobile-nav-dropdown {
    .dropdown-item {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-direction: row !important;
      padding: 0.75rem 1rem !important;
    }

    .menu-icon {
      position: absolute !important;
      right: 1rem !important;
      left: auto !important;
      margin: 0 !important;

      i {
        transform: none !important;
        direction: ltr !important;
        display: inline-block !important;
        font-size: 1.1rem !important;
      }
    }

    span:not(.menu-icon),
    a {
      padding-right: 3rem !important; /* مساحة للأيقونة */
      padding-left: 0 !important;
      width: 100% !important;
      text-align: right !important;
    }
  }

  :host-context(html[dir="rtl"]) .menu-icon i,
  :host-context(html[lang="ar"]) .menu-icon i {
    transform: none !important;
    direction: ltr !important;
  }
}

/* تحديث تنسيق property-badge ليكون زي الصورة */
.property-badge {
  position: absolute !important;
  top: 15px !important;
  right: 15px !important;
  background: white !important;
  color: #ff6b35 !important;
  padding: 6px 10px !important; /* تصغير الحجم */
  border-radius: 15px !important; /* تصغير الزوايا */
  font-size: 10px !important; /* تصغير الخط */
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important; /* تقليل المسافة */
  z-index: 10 !important;
  max-width: 90px !important; /* حد أقصى للعرض */

  i {
    color: #ffd700 !important;
    font-size: 8px !important; /* تصغير النجمة */
    transform: none !important;
    direction: ltr !important;
    flex-shrink: 0 !important; /* منع تقليص النجمة */
  }

  span {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-size: 10px !important;
  }
}

/* دعم العربية للـ property-badge */
:host-context(html[dir="rtl"]) .property-badge,
:host-context(html[lang="ar"]) .property-badge {
  right: auto !important;
  left: 15px !important;
  font-family: "Noto Kufi Arabic" !important;
  letter-spacing: 0 !important;
  word-spacing: 0 !important;
  flex-direction: row-reverse !important; /* ترتيب العناصر: نجمة ثم نص */
  max-width: 100px !important; /* مساحة أكبر للعربية */

  span {
    font-family: "Noto Kufi Arabic" !important;
    letter-spacing: 0 !important;
    word-spacing: 0 !important;
    font-size: 9px !important; /* حجم مناسب للعربية */
    order: 1 !important;
  }

  i {
    transform: none !important;
    direction: ltr !important;
    order: 2 !important; /* النجمة في النهاية */
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* تحسين responsive للـ badge */
@media (max-width: 768px) {
  .property-badge {
    padding: 4px 8px !important;
    font-size: 9px !important;
    max-width: 80px !important;

    i {
      font-size: 7px !important;
    }

    span {
      font-size: 9px !important;
    }
  }

  :host-context(html[dir="rtl"]) .property-badge,
  :host-context(html[lang="ar"]) .property-badge {
    max-width: 85px !important;

    span {
      font-size: 8px !important;
    }
  }
}

// Mobile Layout - Force Single Line
@media (max-width: 991.98px) {
  .navbar {
    .container-fluid {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      width: 100% !important;
      padding: 0.5rem 1rem !important;
      min-height: auto !important;
    }

    .navbar-brand {
      flex: 0 0 auto !important;
      margin: 0 !important;

      img {
        height: 35px !important;
        width: auto !important;
      }
    }

    .navbar-toggler {
      flex: 0 0 auto !important;
      margin: 0 !important;
      padding: 0.25rem 0.5rem !important;
      border: none !important;
      background: transparent !important;
      order: 0 !important;
    }

    .navbar-nav {
      flex: 0 0 auto !important;
      margin: 0 !important;

      &.position-relative {
        position: relative !important;
      }

      .user-profile {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.85rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;

        .user-avatar {
          width: 28px !important;
          height: 28px !important;
        }

        .user-name {
          display: inline !important; // Show username on larger mobile screens
        }

        .fas.fa-chevron-down {
          font-size: 0.7rem !important;
          margin-left: 0.25rem !important;
        }
      }

      .user-link {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.85rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;

        i {
          margin-right: 0.25rem !important;
        }
      }
    }

    // Hide desktop navigation
    .navbar-nav.mx-auto {
      display: none !important;
    }
  }
}

// Extra Small Screens
@media (max-width: 576px) {
  .navbar {
    .container-fluid {
      padding: 0.4rem 0.75rem !important;
    }

    .navbar-brand img {
      height: 30px !important;
    }

    .navbar-toggler {
      padding: 0.2rem 0.4rem !important;

      i {
        font-size: 1rem !important;
      }
    }

    .navbar-nav {
      .user-profile {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.8rem !important;

        .user-avatar {
          width: 24px !important;
          height: 24px !important;
        }
      }

      .user-link {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.8rem !important;

        span {
          display: none !important;
        }

        i {
          margin-right: 0 !important;
          font-size: 1rem !important;
        }
      }
    }
  }
}

// Force horizontal layout on all mobile screens
@media (max-width: 991.98px) {
  .home-header .navbar {
    .container-fluid {
      display: flex !important;
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      overflow: visible !important;
    }

    // Ensure all direct children stay on same line
    > .container-fluid > * {
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }
}

// Very small screens - Hide username only below 320px
@media (max-width: 319px) {
  .navbar {
    .navbar-nav {
      .user-profile {
        .user-name {
          display: none !important; // Hide username only on very small screens
        }
      }
    }
  }
}

// Back to Top Button Styles
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
    color: white !important;
  }

  &:active {
    transform: translateY(-1px);
  }

  &:focus {
    outline: none !important;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3) !important;
    color: white !important;
  }

  i {
    font-size: 16px;
    color: white !important;
    transform: none !important;
    direction: ltr !important;
  }
}

// Arabic RTL support for back-to-top button
:host-context(html[dir="rtl"]) .back-to-top,
:host-context(html[lang="ar"]) .back-to-top {
  right: auto !important;
  left: 30px !important;

  i {
    transform: none !important;
    direction: ltr !important;
    color: white !important;
  }
}

// Responsive adjustments for back to top button
@media (max-width: 768px) {
  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 16px;

    i {
      font-size: 14px;
    }
  }

  // Arabic responsive
  :host-context(html[dir="rtl"]) .back-to-top,
  :host-context(html[lang="ar"]) .back-to-top {
    right: auto !important;
    left: 20px !important;
  }
}

@media (max-width: 480px) {
  .back-to-top {
    bottom: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    font-size: 14px;

    i {
      font-size: 12px;
    }
  }

  // Arabic responsive
  :host-context(html[dir="rtl"]) .back-to-top,
  :host-context(html[lang="ar"]) .back-to-top {
    right: auto !important;
    left: 15px !important;
  }
}

// Ensure back-to-top button is always visible and clickable
.back-to-top {
  // Force visibility and interaction
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;

  // Ensure it's above everything
  z-index: 9999 !important;

  // Prevent any interference
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  // Ensure proper display
  display: flex !important;

  // Prevent text selection
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;

  // Force button behavior
  &:hover,
  &:focus,
  &:active {
    pointer-events: auto !important;
    cursor: pointer !important;
  }
}

// Additional RTL fixes for Arabic
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .back-to-top {
    // Ensure proper positioning in RTL
    position: fixed !important;
    left: 30px !important;
    right: auto !important;

    // Prevent any RTL interference
    direction: ltr !important;

    i {
      // Prevent icon flipping
      transform: none !important;
      direction: ltr !important;
      display: inline-block !important;
    }
  }
}

// Request Buttons Section
.request-buttons-section {
  margin-top: -10%;
  position: relative;
  .request-buttons-container {
    background-color: white;
    border-radius: 20px;
    padding: 20px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 0 20px;

    .request-btn {
      width: 100%;
      height: 100px;
      background: linear-gradient(135deg, #06498d, #0584ce);
      border: none;
      border-radius: 15px;
      color: white;
      font-weight: 600;
      font-size: 0.9rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      transition: all 0.3s ease;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);

      i {
        font-size: 1.5rem;
        margin-bottom: 5px;
        color: white; // White color for icons
      }

      span {
        text-align: center;
        line-height: 1.2;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        background: linear-gradient(135deg, #229954, #27ae60);
      }

      &:active {
        transform: translateY(-2px);
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 992px) {
    .request-buttons-container {
      margin: 0 15px;
      padding: 30px 20px;

      .request-btn {
        height: 100px;
        font-size: 0.8rem;

        i {
          font-size: 1.3rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .request-buttons-container {
      margin: 0 10px;
      padding: 25px 15px;

      .request-btn {
        height: 90px;
        font-size: 0.75rem;

        i {
          font-size: 1.2rem;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .request-buttons-container {
      margin: 0 5px;
      padding: 20px 10px;

      .request-btn {
        height: 80px;
        font-size: 0.7rem;

        i {
          font-size: 1.1rem;
        }
      }
    }
  }
}

// Arabic/RTL Support for Request Buttons
:host-context(html[dir="rtl"]) .request-buttons-section,
:host-context(html[lang="ar"]) .request-buttons-section {
  .request-btn {
    span {
      font-family: "Noto Kufi Arabic";
      font-weight: 500;
    }
  }
}

// Font Imports
@import url("https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Markazi+Text:wght@400;500;600;700&display=swap");

// Properties Section
.properties-section {
  padding: 20px 0;
  font-family: "Markazi Text";

  // background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);

  // Properties Row with Gap
  .row.g-4 {
    gap: 5px;
    justify-content: space-between;

    .col-lg-3 {
      flex: 0 0 auto;
      width: calc(25% - 15px);
      max-width: 320px;
    }
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2rem;
    position: relative;
    font-family: "Markazi Text";

    &::after {
      content: "";
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;

      border-radius: 2px;
    }
  }
}

.property-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 400px;
  width: 100%;
  max-width: 320px;
  opacity: 1;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  font-family: "Markazi Text";
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .property-image {
    position: relative;
    width: 100%;
    height: 320px;
    opacity: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    transform: rotate(0deg);

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      z-index: 1;
      pointer-events: none;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }

    // Top Badges Container
    .property-top-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      // justify-content: space-between;
      align-items: flex-start;
      width: auto;
      height: 24px;
      gap: 4px;
      z-index: 2;
    }

    // English layout - left positioning
    :host-context(html[dir="ltr"]) .property-top-badges,
    :host-context(html[lang="en"]) .property-top-badges {
      left: 12px;
    }

    // English layout - property location left positioning
    :host-context(html[dir="ltr"]) .property-location-text,
    :host-context(html[lang="en"]) .property-location-text {
      left: 12px;
    }

    // English layout - smaller font for Featured badge
    :host-context(html[dir="ltr"]) .property-badge-featured,
    :host-context(html[lang="en"]) .property-badge-featured {
      font-size: 0.7rem !important;
    }

    // Featured Badge
    .property-badge-featured {
      background: #ffffff;
      color: #333333;
      padding: 4px 10px;
      border-radius: 24px;
      font-size: 0.96rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 4px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-family: "Noto Kufi Arabic";

      i {
        color: #ffc107;
        font-size: 0.8rem;
      }
    }

    // Price Badge
    .property-price-overlay {
      background: #ffffff;
      color: #007bff;
      padding: 4px 10px;
      border-radius: 24px;
      font-size: 0.99rem;
      font-weight: 400;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 4px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-family: "Markazi Text";

      i {
        color: #28a745;
        font-size: 0.8rem;
      }

      .price-amount {
        color: #007bff;
      }
    }

    // Property Type - Bottom Left
    .property-type-badge {
      position: absolute;
      bottom: 70px;
      left: 12px;
      background: #ff6b35;
      color: white;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 0.99rem;
      font-weight: 600;
      text-transform: capitalize;
      z-index: 2;
      font-family: "Markazi Text";
    }

    // Property Title - Over Image
    .property-title {
      position: absolute;
      bottom: 45px;
      left: 12px;
      right: 12px;
      font-size: 0.95rem;
      font-weight: 600;
      color: white;
      margin-bottom: 0;
      line-height: 1.3;
      display: flex;
      align-items: center;
      gap: 6px;
      font-family: "Noto Kufi Arabic", "Markazi Text", sans-serif;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
      flex-wrap: wrap;

      .verified-icon {
        color: #28a745;
        font-size: 0.8rem;
        margin-left: 4px;
      }
    }

    // Property Location - Over Image
    .property-location-text {
      position: absolute;
      bottom: 20px;
      // left: 12px;
      right: 12px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.85rem;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 6px;
      font-family: "Noto Kufi Arabic", "Markazi Text", sans-serif;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);

      i {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.8rem;
      }
    }
  }

  .property-content {
    padding: 5px;

    .property-title {
      font-size: 1.1rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
      line-height: 1.4;
      display: flex;
      align-items: center;
      gap: 8px;
      font-family: "Markazi Text";

      .verified-icon {
        color: #28a745;
        font-size: 0.9rem;
      }
    }

    .property-location-text {
      color: #6c757d;
      font-size: 0.85rem;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
      font-family: "Markazi Text";

      i {
        color: #6c757d;
        font-size: 0.99rem;
      }
    }

    .property-rating {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;

      .stars {
        display: flex;
        gap: 2px;

        i {
          color: #f39c12;
          font-size: 0.9rem;
        }
      }

      .rating-text {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-right: 15px;
      }
    }

    .property-actions {
      display: flex;
      gap: 10px;

      .btn {
        border-radius: 8px;
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        background: white;
        color: #7f8c8d;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3498db;
          color: #3498db;
          background: rgba(52, 152, 219, 0.1);
        }

        i {
          font-size: 0.9rem;
        }
      }
    }

    // Bottom Row with Rating and Actions (New Design)
    .property-bottom-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 10px;
      // margin-top: 12px;

      .property-rating {
        display: flex;
        align-items: center;
        // gap: 6px;
        // flex: 1;

        .stars {
          display: flex;
          gap: 1px;

          i {
            color: #ffc107;
            font-size: 0.8rem;
          }
        }

        .rating-text {
          font-weight: 600;
          color: #2c3e50;
          font-size: 0.98rem;
          margin-left: 4px;
          font-family: "Markazi Text";
        }

        .reviews-count {
          color: #6c757d;
          // font-size: 0.75rem;
          // margin-left: 4px;
          font-family: "Markazi Text";
        }
      }

      .property-actions {
        display: flex;
        gap: 6px;
        flex: 0 0 auto;

        .btn-icon {
          background: none;
          border: none;
          color: #6c757d;
          padding: 6px;
          border-radius: 50%;
          transition: all 0.3s ease;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #f8f9fa;
            color: #007bff;
            transform: scale(1.1);
          }

          i {
            font-size: 0.85rem;
          }
        }
      }
    }

    // Keep old design for backward compatibility
    .property-rating-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;

      .property-rating {
        margin-bottom: 0;
        flex: 1;
      }

      .property-actions {
        flex: 0 0 auto;
      }
    }
  }
}

// Responsive Design for Properties
@media (max-width: 768px) {
  .properties-section {
    padding: 60px 0;

    .section-title {
      font-size: 2rem;
      margin-bottom: 2rem;
    }
  }

  .property-card {
    margin-bottom: 20px;

    .property-image {
      height: 180px;
    }

    .property-content {
      padding: 15px;

      .property-title {
        font-size: 1.2rem;
      }

      .property-price .price {
        font-size: 1.2rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .properties-section {
    // padding: 40px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
    }
  }

  .property-card {
    .property-image {
      height: 160px;

      .property-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
      }

      .property-location {
        font-size: 0.7rem;
        padding: 4px 8px;
      }
    }

    .property-content {
      padding: 12px;

      .property-title {
        font-size: 1.1rem;
      }

      .property-description {
        font-size: 0.8rem;
      }

      .property-price .price {
        font-size: 1.1rem;
      }

      .property-rating {
        .stars i {
          font-size: 0.99rem;
        }

        .rating-text {
          font-size: 0.99rem;
        }
      }

      .property-actions {
        .btn {
          padding: 6px 10px;

          i {
            font-size: 0.99rem;
          }
        }
      }
    }
  }
}

// Horizontal Carousel Section
.horizontal-carousel-section {
  background-color: #f8f8f8;
  margin-top: 10px;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #007bff, #0056b3);
      border-radius: 2px;
    }
  }

  .carousel-container {
    margin: 0 30px; // Increased margin to accommodate external buttons
    margin-top: 30px;
    // padding-bottom: 75px;

    position: relative; // For absolute positioning of buttons

    .carousel {
      .carousel-inner {
        .carousel-item {
          .row {
            gap: 20px; // Add gap between columns
            justify-content: center;
            flex-wrap: nowrap; // Prevent wrapping to avoid stacking
            overflow-x: hidden; // Hide horizontal scroll

            // Hide scrollbar completely
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            &::-webkit-scrollbar {
              display: none; /* Chrome, Safari, Opera */
            }

            .col-xl-2,
            .col-lg-2,
            .col-md-3,
            .col-sm-4,
            .col-6 {
              flex: 0 0 auto; // Don't grow or shrink
              width: calc(
                25% - 15px
              ) !important; // Show exactly 4 items per row
              max-width: none !important; // Override Bootstrap max-width
              min-width: 250px; // Minimum width to ensure cards are not cut
            }
          }

          .location-card {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            width: 277px !important; // Take full width of container
            height: 277px; // Fixed height for consistency
            margin: 0 auto; // Center the card within its container

            &:hover {
              transform: translateY(-5px);
              box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

              img {
                transform: scale(1.05);
              }

              .location-overlay {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }

            .location-overlay {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: rgba(1, 44, 69, 0.5);
              color: white;
              padding: 9px;
              transition: all 0.3s ease;

              .location-info {
                h5 {
                  font-size: 1.1rem;
                  font-weight: 600;
                  margin-bottom: 5px;
                  color: white;
                  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                }

                p {
                  font-size: 1.2rem;
                  margin: 0;
                  opacity: 0.9;
                  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);

                  i {
                    margin-right: 5px;
                    color: #ffd700;
                  }
                }
              }
            }
          }
        }
      }

      // Custom Carousel Controls - Positioned outside the cards
      .carousel-control-prev,
      .carousel-control-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px !important;
        height: 50px !important;
        background: #031752;
        border: none;
        border-radius: 50%;
        opacity: 1;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
        z-index: 10;

        &:hover {
          background: #1e40af;
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 20px;
          height: 20px;
          background-size: 20px 20px;
        }
      }

      .carousel-control-prev {
        left: -70px; // Move far left, outside the cards
      }

      .carousel-control-next {
        right: -70px; // Move far right, outside the cards
      }
    }
  }
}

// Extra Large screens (1400px and up)
@media (min-width: 1400px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 50px; // More space for larger buttons

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 60px;
          height: 60px;
        }

        .carousel-control-prev {
          left: -80px; // More space for larger screens
        }

        .carousel-control-next {
          right: -80px; // More space for larger screens
        }

        .carousel-inner {
          .carousel-item {
            .row {
              gap: 25px !important; // Larger gap for extra large screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                width: calc(
                  25% - 18.75px
                ) !important; // Exactly 4 items per row
                min-width: 280px; // Minimum width for larger screens
              }
            }
          }
        }
      }

      .location-card {
        height: 280px !important; // Larger height for extra large screens

        .location-overlay {
          .location-info {
            h5 {
              font-size: 1.3rem;
            }

            p {
              font-size: 0.95rem;
            }
          }
        }
      }
    }
  }
}

// Large screens (992px to 1399px)
@media (min-width: 992px) and (max-width: 1399px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 40px; // Space for buttons on large screens

      .carousel {
        .carousel-inner {
          .carousel-item {
            .row {
              gap: 20px !important; // Gap for large screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                width: calc(25% - 15px) !important; // Exactly 4 items per row
                min-width: 250px; // Minimum width for large screens
              }
            }
          }
        }

        .carousel-control-prev {
          left: -70px; // Standard spacing for large screens
        }

        .carousel-control-next {
          right: -70px; // Standard spacing for large screens
        }
      }

      .location-card {
        height: 250px !important;
        margin: 0 auto; // Center the card
      }
    }
  }
}

// Medium screens (768px to 991px)
@media (min-width: 768px) and (max-width: 991px) {
  .horizontal-carousel-section {
    .section-title {
      font-size: 2.2rem;
      margin-bottom: 2.5rem;
    }

    .carousel-container {
      margin: 0 30px;

      .carousel {
        .carousel-inner {
          .carousel-item {
            .row {
              gap: 15px !important; // Gap for medium screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                width: calc(
                  50% - 7.5px
                ) !important; // Show 2 items per row on medium screens
                min-width: 220px; // Minimum width for medium screens
              }
            }
          }
        }

        .carousel-control-prev,
        .carousel-control-next {
          width: 45px;
          height: 45px;
        }

        .carousel-control-prev {
          left: -60px; // Outside the cards for medium screens
        }

        .carousel-control-next {
          right: -60px; // Outside the cards for medium screens
        }
      }

      .location-card {
        height: 220px !important; // Adjusted height for medium screens
        margin: 0 auto; // Center the card

        .location-overlay {
          padding: 12px;

          .location-info {
            h5 {
              font-size: 1rem;
              color: white;
            }

            p {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

// Small screens (576px to 767px)
@media (min-width: 576px) and (max-width: 767px) {
  .horizontal-carousel-section {
    .section-title {
      font-size: 2rem;
      margin-bottom: 2rem;
    }

    .carousel-container {
      margin: 0 20px;

      .carousel {
        .carousel-inner {
          .carousel-item {
            .row {
              gap: 12px !important; // Gap for small screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                width: calc(
                  50% - 6px
                ) !important; // Show 2 items per row on small screens
                min-width: 180px; // Minimum width for small screens
              }
            }
          }
        }

        .carousel-control-prev,
        .carousel-control-next {
          width: 40px;
          height: 40px;
        }

        .carousel-control-prev {
          left: -50px; // Outside the cards for small screens
        }

        .carousel-control-next {
          right: -50px; // Outside the cards for small screens
        }
      }

      .location-card {
        height: 180px !important; // Adjusted height for small screens
        margin: 0 auto; // Center the card

        .location-overlay {
          padding: 10px;

          .location-info {
            h5 {
              font-size: 0.95rem;
              color: white;
            }

            p {
              font-size: 0.75rem;
            }
          }
        }
      }
    }
  }
}

// Extra Small screens (up to 575px)
@media (max-width: 575px) {
  .horizontal-carousel-section {
    padding: 40px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
    }

    .carousel-container {
      margin: 0 15px;

      .carousel {
        .carousel-inner {
          .carousel-item {
            .row {
              gap: 10px !important; // Gap for extra small screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                // width: 100% !important; // Show 1 item per row on extra small screens
                min-width: 160px; // Minimum width for extra small screens
              }
            }
          }
        }

        .carousel-control-prev,
        .carousel-control-next {
          width: 35px;
          height: 35px;
        }

        .carousel-control-prev {
          left: -40px; // Outside the cards for extra small screens
        }

        .carousel-control-next {
          right: -40px; // Outside the cards for extra small screens
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 16px;
          height: 16px;
          background-size: 16px 16px;
        }
      }

      .location-card {
        height: 160px !important; // Adjusted height for extra small screens
        margin: 0 auto; // Center the card

        .location-overlay {
          padding: 8px;

          .location-info {
            h5 {
              font-size: 0.85rem;
              color: white;
              margin-bottom: 3px;
            }

            p {
              font-size: 0.7rem;

              i {
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
}

// Very Small screens (up to 400px)
@media (max-width: 400px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 10px;

      .carousel {
        .carousel-inner {
          .carousel-item {
            .row {
              gap: 8px !important; // Minimal gap for very small screens

              .col-xl-3,
              .col-lg-3,
              .col-md-3,
              .col-sm-6,
              .col-6 {
                width: 100% !important; // Show 1 item per row on very small screens
                min-width: 140px; // Minimum width for very small screens
              }
            }
          }
        }

        .carousel-control-prev {
          left: -35px; // Outside the cards for very small screens
        }

        .carousel-control-next {
          right: -35px; // Outside the cards for very small screens
        }
      }

      .location-card {
        height: 140px !important; // Adjusted height for very small screens
        margin: 0 auto; // Center the card

        .location-overlay {
          padding: 6px;

          .location-info {
            h5 {
              font-size: 0.8rem;
            }

            p {
              font-size: 0.65rem;
            }
          }
        }
      }
    }
  }
}

// Articles Section
.articles-section {
  background-color: #ffffff;
  padding: 80px 0;
  margin-top: 0px;
  // margin-bottom: 5px;

  // Articles Carousel Styling
  .carousel-inner {
    .col-lg-3 {
      flex: 0 0 auto;
      width: 25%;
      // height: 430px;
    }

    .article-card {
      height: 100%; // Make card take full height
      display: flex;
      flex-direction: column;

      .article-image {
        flex: 1; // Take remaining space
        position: relative;
        overflow: hidden;
      }
    }
  }

  // Fix carousel indicators positioning
  .carousel-indicators {
    position: static !important; // Remove absolute positioning
    margin-top: 10px !important; // Small margin from cards
    margin-bottom: 0 !important;
    justify-content: center;

    button {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin: 0 5px;
      background-color: #dee2e6;
      border: none;
      opacity: 0.5;
      transition: all 0.3s ease;

      &.active {
        opacity: 1;
        background-color: #27ae60;
        transform: scale(1.2);
      }

      &:hover {
        opacity: 0.8;
        transform: scale(1.1);
      }
    }

    // Responsive adjustments for indicators
    @media (max-width: 767.98px) {
      margin-top: 8px !important;

      button {
        width: 10px;
        height: 10px;
        margin: 0 4px;
      }
    }

    @media (max-width: 575.98px) {
      margin-top: 8px !important;

      button {
        width: 8px;
        height: 8px;
        margin: 0 3px;
      }
    }
  }

  .section-header {
    margin-bottom: 60px;

    .left-controls {
      .carousel-control-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: none;
        background: #031752;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);

        &:hover {
          background: #1e40af;
          transform: scale(1.05);
          box-shadow: 0 8px 25px rgba(30, 58, 138, 0.35);
        }

        i {
          font-size: 18px;
        }
      }
    }

    .articles-title {
      font-size: 4.3rem; // Default size for English
      font-weight: 800;
      color: #031752;
      text-shadow: 2px 2px 4px rgba(30, 58, 138, 0.1);
      margin: 0;
      letter-spacing: -0.5px;
      direction: ltr;
      text-align: center;
      margin-left: 1%;
    }

    .right-link {
      .view-all-link {
        text-decoration: none;
        font-size: 1.4rem;
        font-weight: 600;
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
        direction: ltr;

        .text-success {
          color: #28a745 !important;
        }

        .green-underline {
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 3px;
          background: #28a745;
          border-radius: 2px;
        }

        &:hover {
          transform: translateX(-5px);

          .text-success {
            color: #1e7e34 !important;
          }

          .green-underline {
            background: #1e7e34;
          }

          i {
            transform: translateX(-3px);
          }
        }

        i {
          transition: transform 0.3s ease;
        }
      }
    }
  }

  #articlesCarousel {
    .carousel-inner {
      .carousel-item {
        .article-card {
          position: relative;
          border-radius: 8.55px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          cursor: pointer;
          height: 410px;

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);

            .article-image img {
              transform: scale(1.03);
            }

            .article-overlay {
              background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            }
          }

          .article-image {
            position: relative;
            height: 100%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            }

            .article-overlay {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
              color: white;
              padding: 40px 25px 25px;
              transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              .article-content {
                // direction: ltr;
                // text-align: left;

                h4 {
                  font-size: 1.4rem;
                  font-weight: 700;
                  margin-bottom: 12px;
                  color: white;
                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                  line-height: 1.3;
                }

                p {
                  font-size: 1.3rem;
                  margin: 0;
                  opacity: 0.95;
                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                  line-height: 1.6;
                }
              }
            }
          }
        }
      }
    }

    // Custom Carousel Controls for Articles
    .carousel-control-prev,
    .carousel-control-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: #1e3a8a;
      border: none;
      border-radius: 50%;
      opacity: 1;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);

      &:hover {
        background: #1e40af;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
      }

      .carousel-control-prev-icon,
      .carousel-control-next-icon {
        width: 20px;
        height: 20px;
        background-size: 20px 20px;
      }
    }

    .carousel-control-prev {
      left: -25px;
    }

    .carousel-control-next {
      right: -25px;
    }

    // Custom Indicators
    .carousel-indicators {
      bottom: -50px;
      margin-bottom: 0;

      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: none;
        background: rgba(30, 58, 138, 0.3);
        margin: 0 6px;
        transition: all 0.3s ease;

        &.active {
          background: #1e3a8a;
          transform: scale(1.2);
        }

        &:hover {
          background: #1e40af;
          transform: scale(1.1);
        }
      }
    }
  }
}

// Responsive Design for Articles Section
@media (max-width: 1200px) {
  .articles-section {
    #articlesCarousel {
      .carousel-control-prev {
        left: -15px;
      }

      .carousel-control-next {
        right: -15px;
      }
    }
  }
}

// Tablet Responsive - 1024px and below
@media (max-width: 1024px) {
  .articles-section {
    padding: 60px 0;

    .section-header {
      margin-bottom: 30px;

      .articles-title {
        font-size: 2.2rem;
      }

      .left-controls {
        gap: 8px;

        .carousel-control-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .row {
            // Keep 4 cards side by side on tablet but smaller
            .col-lg-3 {
              flex: 0 0 25%;
              max-width: 25%;
            }
          }

          .article-card {
            height: 300px; // Smaller height for tablet
            border-radius: 8px;

            .article-image {
              .article-overlay {
                padding: 20px;

                .article-content {
                  h4 {
                    font-size: 1.1rem;
                    margin-bottom: 6px;
                  }

                  p {
                    font-size: 0.8rem;
                    line-height: 1.3;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 40px;
        height: 40px;
      }
    }
  }
}

@media (max-width: 992px) {
  .articles-section {
    .section-header {
      .articles-title {
        font-size: 2rem;
      }

      .view-all-link a {
        font-size: 1.2rem;
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 380px;
            border-radius: 8.55px;

            .article-image {
              .article-overlay {
                padding: 25px;

                .article-content {
                  h4 {
                    font-size: 1.2rem;
                  }

                  p {
                    font-size: 0.9rem;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 45px;
        height: 45px;
      }
    }
  }
}

@media (max-width: 768px) {
  .articles-section {
    padding: 50px 0;

    .section-header {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 15px;
      margin-bottom: 25px;

      .articles-title {
        font-size: 1.8rem;
      }

      .left-controls {
        .carousel-control-btn {
          width: 35px;
          height: 35px;

          i {
            font-size: 12px;
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .row {
            // Show only 2 cards on mobile
            .col-lg-3 {
              flex: 0 0 50%;
              max-width: 50%;

              &:nth-child(n + 3) {
                display: none; // Hide 3rd and 4th cards
              }
            }
          }

          .article-card {
            height: 280px;
            border-radius: 8px;
            margin-bottom: 15px;

            .article-image {
              .article-overlay {
                padding: 18px;

                .article-content {
                  h4 {
                    font-size: 1rem;
                    margin-bottom: 5px;
                  }

                  p {
                    font-size: 0.8rem;
                    line-height: 1.3;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 35px;
        height: 35px;
      }
    }

    // Responsive layout for All Articles link and carousel
    .row {
      flex-direction: row;

      .col-lg-2,
      .col-md-3 {
        order: 2;
        margin-top: 20px;

        .right-link {
          text-align: center;

          .view-all-link {
            font-size: 1.2rem !important;

            i {
              font-size: 1rem !important;
            }

            span {
              font-size: 1.2rem !important;
            }
          }
        }
      }

      .col-lg-10,
      .col-md-9 {
        order: 1;
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 320px;
            border-radius: 8.55px;

            .article-image {
              .article-overlay {
                padding: 20px;

                .article-content {
                  h4 {
                    font-size: 1.1rem;
                    margin-bottom: 10px;
                  }

                  p {
                    font-size: 0.85rem;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 40px;
        height: 40px;
        top: 45%;

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 16px;
          height: 16px;
          background-size: 16px 16px;
        }
      }

      .carousel-control-prev {
        left: 10px;
      }

      .carousel-control-next {
        right: 10px;
      }

      .carousel-indicators {
        bottom: -40px;

        button {
          width: 10px;
          height: 10px;
          margin: 0 4px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .articles-section {
    padding: 40px 0;

    .section-header {
      .articles-title {
        font-size: 1.6rem;
      }

      .left-controls {
        .carousel-control-btn {
          width: 32px;
          height: 32px;

          i {
            font-size: 11px;
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .row {
            // Keep 2 cards on small mobile
            .col-lg-3 {
              flex: 0 0 50%;
              max-width: 50%;

              &:nth-child(n + 3) {
                display: none;
              }
            }
          }

          .article-card {
            height: 250px;
            border-radius: 6px;

            .article-image {
              .article-overlay {
                padding: 15px;

                .article-content {
                  h4 {
                    font-size: 0.9rem;
                    margin-bottom: 4px;
                  }

                  p {
                    font-size: 0.75rem;
                    line-height: 1.2;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 32px;
        height: 32px;
      }
    }

    // Extra responsive adjustments for very small screens
    .row {
      .col-lg-2,
      .col-md-3 {
        margin-top: 15px;

        .right-link {
          .view-all-link {
            font-size: 1rem !important;

            i {
              font-size: 0.9rem !important;
            }

            span {
              font-size: 1rem !important;
            }
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 280px;
            border-radius: 8.55px;

            .article-image {
              .article-overlay {
                padding: 15px;

                .article-content {
                  h4 {
                    font-size: 1.3rem;
                    margin-bottom: 8px;
                  }

                  p {
                    font-size: 1.4rem;
                    line-height: 1.4;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 35px;
        height: 35px;

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 14px;
          height: 14px;
          background-size: 14px 14px;
        }
      }

      .carousel-indicators {
        bottom: -35px;

        button {
          width: 8px;
          height: 8px;
          margin: 0 3px;
        }
      }
    }
  }
}

// Extra small screens (phones in portrait mode)
@media (max-width: 480px) {
  .articles-section {
    padding: 35px 0;

    .section-header {
      margin-bottom: 25px;

      .articles-title {
        font-size: 1.4rem;
        text-align: center;
      }

      .left-controls {
        justify-content: center;

        .carousel-control-btn {
          width: 30px;
          height: 30px;

          i {
            font-size: 10px;
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .row {
            // Show only 2 cards on very small mobile
            .col-lg-3 {
              flex: 0 0 50%;
              max-width: 50%;

              &:nth-child(n + 3) {
                display: none;
              }
            }
          }

          .article-card {
            height: 220px;
            margin-bottom: 10px;
            border-radius: 6px;

            .article-image {
              .article-overlay {
                padding: 12px;

                .article-content {
                  h4 {
                    font-size: 0.8rem;
                    margin-bottom: 3px;
                  }

                  p {
                    font-size: 0.7rem;
                    line-height: 1.1;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 30px;
        height: 30px;
      }
    }

    .row {
      .col-lg-2,
      .col-md-3 {
        margin-top: 10px;

        .right-link {
          .view-all-link {
            font-size: 0.9rem !important;

            i {
              font-size: 0.8rem !important;
            }

            span {
              font-size: 0.9rem !important;
            }
          }
        }
      }
    }
    h4 {
      font-size: 0.9rem;
      margin-bottom: 6px;
    }

    p {
      font-size: 0.75rem;
      line-height: 1.3;
    }
  }
}

.carousel-indicators {
  bottom: -30px;

  button {
    width: 6px;
    height: 6px;
    margin: 0 2px;
  }
}

:host-context(html[dir="rtl"]) .store-button .store-icon,
:host-context(html[lang="ar"]) .store-button .store-icon,
:host-context(.rtl) .store-button .store-icon {
  font-size: 1.4rem !important;
  transform: scale(1) !important;
  width: auto !important;
  height: auto !important;
}

:host-context(html[dir="rtl"]) .store-button .store-icon i,
:host-context(html[lang="ar"]) .store-button .store-icon i,
:host-context(.rtl) .store-button .store-icon i {
  font-size: 1.4rem !important;
  transform: scale(1) !important;
}

/* تكبير إضافي للموبايل في العربية */
@media (max-width: 576px) {
  :host-context(html[dir="rtl"]) .store-button .store-icon,
  :host-context(html[lang="ar"]) .store-button .store-icon,
  :host-context(.rtl) .store-button .store-icon {
    font-size: 2rem !important;
    transform: scale(1.2) !important;
  }
}

/* تكبير النصوص في العربية */
:host-context(html[dir="rtl"]) .store-button .store-text,
:host-context(html[lang="ar"]) .store-button .store-text,
:host-context(.rtl) .store-button .store-text {
  .download-text {
    font-size: 0.9rem !important;
    font-weight: 400;
  }

  .store-name {
    font-size: 1rem !important;
    font-weight: 400;
  }
}

/* كلاسات إضافية للعربية */
.arabic-store-btn {
  transform: scale(1.1);
}

.arabic-store-button {
  padding: 10px 22px !important;
}

.arabic-store-icon {
  font-size: 1rem !important;
  margin-left: 10px !important;
  margin-right: 0 !important;
}

.arabic-icon {
  font-size: 1.5rem !important;
}

.arabic-store-text {
  .arabic-download-text {
    font-size: 0.8rem !important;
    font-weight: 500;
  }

  .arabic-store-name {
    font-size: 1.4rem !important;
    font-weight: 700;
  }
}

/* إصلاح ترتيب الأيقونات في User Dropdown للعربية */
:host-context(html[dir="rtl"]) .user-dropdown .dropdown-item,
:host-context(html[lang="ar"]) .user-dropdown .dropdown-item,
:host-context(.rtl) .user-dropdown .dropdown-item {
  flex-direction: row-reverse !important;
  display: flex !important;
  align-items: center !important;
  text-align: right !important;
}

:host-context(html[dir="rtl"]) .user-dropdown .menu-icon,
:host-context(html[lang="ar"]) .user-dropdown .menu-icon,
:host-context(.rtl) .user-dropdown .menu-icon {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

:host-context(html[dir="rtl"])
  .user-dropdown
  .dropdown-item
  span:not(.menu-icon),
:host-context(html[lang="ar"])
  .user-dropdown
  .dropdown-item
  span:not(.menu-icon),
:host-context(.rtl) .user-dropdown .dropdown-item span:not(.menu-icon) {
  order: 1 !important;
  text-align: right !important;
  flex: 1 !important;
}

/* تطبيق left: 13px للعربية فقط وإزالة right */
:host-context(html[dir="rtl"]) .user-dropdown,
:host-context(html[lang="ar"]) .user-dropdown,
:host-context(.rtl) .user-dropdown {
  left: 13px !important;
  right: auto !important;
}

/* FINAL OVERRIDE FOR SOCIAL ICONS - HIGHEST PRIORITY */
.app-footer-exact .social-icons i,
.rtl .app-footer-exact .social-icons i,
html[dir="rtl"] .app-footer-exact .social-icons i,
html[lang="ar"] .app-footer-exact .social-icons i,
body.rtl .app-footer-exact .social-icons i {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 30px !important;
  height: 30px !important;
  background-color: #1a365d !important;
  color: white !important;
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
  cursor: pointer !important;
}

/* Override the specific RTL FontAwesome rule */
.rtl .app-footer-exact .social-icons .fab,
.rtl .app-footer-exact .social-icons .fas,
.rtl .app-footer-exact .social-icons .far,
.rtl .app-footer-exact .social-icons .fal,
.rtl .app-footer-exact .social-icons .fad {
  display: flex !important;
}

/* Nuclear option - Override any possible RTL interference */
.app-footer-exact .social-icons i[class*="fa-"] {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.rtl .app-footer-exact .social-icons i[class*="fa-"] {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

// Responsive Design for Download App Footer - Tablets and Mobile
@media (max-width: 1024px) {
  .download-app-footer {
    padding: 30px 0 !important;

    .container {
      padding: 0 15px;
    }

    .row {
      flex-direction: column !important;
      gap: 30px;
      text-align: center;
    }

    // Logo Section - Always first on mobile
    .logo-contact-section {
      order: 1 !important;
      margin-bottom: 20px;

      .logo-section {
        text-align: center !important;

        .footer-logo {
          height: 50px !important;
          margin: 0 auto 15px !important;
          display: block;
        }

        .contact-info {
          text-align: center;

          .contact-text {
            font-size: 0.9rem !important;
            margin-bottom: 8px;
          }

          .contact-details {
            font-size: 0.8rem !important;
            margin-bottom: 8px;
          }

          .contact-email {
            font-size: 0.9rem !important;
            margin-bottom: 15px;
          }

          // Social Icons and Footer Links Container - All on same line
          .d-flex {
            flex-direction: row !important;
            gap: 20px !important;
            align-items: center !important;
            justify-content: center !important;
            flex-wrap: wrap !important;

            // Social Icons - Inline with footer links
            .social-icons {
              justify-content: center !important;
              gap: 8px !important;
              flex-wrap: nowrap !important;
              flex-shrink: 0;

              i {
                width: 30px !important;
                height: 30px !important;
                font-size: 14px !important;
                flex-shrink: 0;
              }
            }

            // Footer Links - Same line as social icons
            .footer-links {
              margin: 0 !important;
              max-width: none !important;
              width: auto;
              overflow-x: visible;
              flex: 1;
              min-width: 0;

              .footer-links-container {
                display: flex !important;
                flex-wrap: wrap !important;
                justify-content: center !important;
                gap: 8px !important;
                margin: 0 !important;
                font-size: 0.8rem !important;
                padding: 0;

                .footer-link {
                  white-space: nowrap !important;
                  padding: 4px 6px;
                  border-radius: 4px;
                  transition: all 0.3s ease;
                  flex-shrink: 0;
                  border: 1px solid transparent;

                  &:hover {
                    background-color: rgba(0, 12, 102, 0.1);
                    border-color: rgba(0, 12, 102, 0.2);
                  }
                }
              }
            }
          }
        }
      }
    }

    // Download Text - Second on mobile
    .download-text-section {
      order: 2 !important;
      margin: 20px 0;

      .download-title {
        font-size: 1.8rem !important;
        margin-bottom: 0;
        text-align: center;
      }
    }

    // App Buttons - Last on mobile
    .app-buttons-section {
      order: 3 !important;

      .app-buttons {
        justify-content: center !important;
        align-items: center !important;
        margin: 0 !important;
        gap: 20px !important;

        .app-store-btn,
        .google-play-btn {
          height: 50px !important;
          width: auto !important;
          max-width: 150px;
        }
      }
    }
  }
}

// Mobile Specific (smaller screens)
@media (max-width: 768px) {
  .download-app-footer {
    padding: 25px 0 !important;

    .logo-contact-section {
      .logo-section {
        .footer-logo {
          height: 45px !important;
        }

        .contact-info {
          .contact-text {
            font-size: 0.85rem !important;
          }

          .contact-details {
            font-size: 0.75rem !important;
          }

          .contact-email {
            font-size: 0.85rem !important;
          }

          .d-flex {
            gap: 15px !important;

            .social-icons {
              gap: 6px !important;

              i {
                width: 28px !important;
                height: 28px !important;
                font-size: 12px !important;
              }
            }

            .footer-links {
              .footer-links-container {
                font-size: 0.75rem !important;
                gap: 6px !important;

                .footer-link {
                  padding: 3px 5px;
                }
              }
            }
          }
        }
      }
    }

    .download-text-section {
      .download-title {
        font-size: 1.5rem !important;
      }
    }

    .app-buttons-section {
      .app-buttons {
        flex-direction: column !important;
        gap: 15px !important;

        .app-store-btn,
        .google-play-btn {
          height: 45px !important;
          max-width: 140px;
        }
      }
    }
  }
}

// Extra Small Mobile
@media (max-width: 480px) {
  .download-app-footer {
    padding: 20px 0 !important;

    .container {
      padding: 0 10px;
    }

    .logo-contact-section {
      .logo-section {
        .footer-logo {
          height: 40px !important;
        }

        .contact-info {
          .d-flex {
            gap: 12px !important;

            .social-icons {
              gap: 5px !important;

              i {
                width: 26px !important;
                height: 26px !important;
                font-size: 11px !important;
              }
            }

            .footer-links {
              .footer-links-container {
                font-size: 0.7rem !important;
                gap: 5px !important;

                .footer-link {
                  padding: 2px 4px;
                }
              }
            }
          }
        }
      }
    }

    .download-text-section {
      .download-title {
        font-size: 1.3rem !important;
      }
    }

    .app-buttons-section {
      .app-buttons {
        .app-store-btn,
        .google-play-btn {
          height: 40px !important;
          max-width: 130px;
        }
      }
    }
  }
}

/* تحريك Download App Section لأقصى الشمال في العربية */
:host-context(html[dir="rtl"]) .col-lg-6.d-flex.justify-content-end,
:host-context(html[lang="ar"]) .col-lg-6.d-flex.justify-content-end,
:host-context(.rtl) .col-lg-6.d-flex.justify-content-end {
  justify-content: flex-end !important;
}

:host-context(html[dir="rtl"]) .app-preview.text-end,
:host-context(html[lang="ar"]) .app-preview.text-end,
:host-context(.rtl) .app-preview.text-end {
  text-align: left !important;
}

:host-context(html[dir="rtl"]) .app-logo-container,
:host-context(html[lang="ar"]) .app-logo-container,
:host-context(.rtl) .app-logo-container {
  text-align: left !important;
}

:host-context(html[dir="rtl"]) .app-info,
:host-context(html[lang="ar"]) .app-info,
:host-context(.rtl) .app-info {
  text-align: left !important;
}

/* إصلاح Property Cards في العربية */
:host-context(html[dir="rtl"]) .property-badge,
:host-context(html[lang="ar"]) .property-badge,
:host-context(.rtl) .property-badge {
  right: 10px !important;
  left: auto !important;
}

:host-context(html[dir="rtl"]) .property-location,
:host-context(html[lang="ar"]) .property-location,
:host-context(.rtl) .property-location {
  right: 10px !important;
  left: auto !important;
  text-align: right !important;
  flex-direction: row-reverse !important;
}

/* إصلاح التصميم الجديد للعربية */
:host-context(html[dir="rtl"]) .property-top-badges,
:host-context(html[lang="ar"]) .property-top-badges,
:host-context(.rtl) .property-top-badges {
  flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-type-badge,
:host-context(html[lang="ar"]) .property-type-badge,
:host-context(.rtl) .property-type-badge {
  right: 12px !important;
  left: auto !important;
}

/* ترتيب العناصر الجديدة في العربية */
:host-context(html[dir="rtl"]) .property-title,
:host-context(html[lang="ar"]) .property-title,
:host-context(.rtl) .property-title {
  text-align: right !important;
  // flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-location-text,
:host-context(html[lang="ar"]) .property-location-text,
:host-context(.rtl) .property-location-text {
  text-align: right !important;
  flex-direction: row-reverse !important;
}

/* فرض استخدام فونت Markazi Text للنصوص العربية */
:host-context(html[dir="rtl"]) .property-card,
:host-context(html[lang="ar"]) .property-card,
:host-context(.rtl) .property-card {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-badge-featured,
:host-context(html[lang="ar"]) .property-badge-featured,
:host-context(.rtl) .property-badge-featured {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-price-overlay,
:host-context(html[lang="ar"]) .property-price-overlay,
:host-context(.rtl) .property-price-overlay {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-type-badge,
:host-context(html[lang="ar"]) .property-type-badge,
:host-context(.rtl) .property-type-badge {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-location-text,
:host-context(html[lang="ar"]) .property-location-text,
:host-context(.rtl) .property-location-text {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .rating-text,
:host-context(html[lang="ar"]) .rating-text,
:host-context(.rtl) .rating-text {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .reviews-count,
:host-context(html[lang="ar"]) .reviews-count,
:host-context(.rtl) .reviews-count {
  font-family: "Markazi Text", sans-serif !important;
}

/* فرض استخدام Markazi Text لجميع النصوص في العربية */
:host-context(html[dir="rtl"]) p,
:host-context(html[lang="ar"]) p,
:host-context(.rtl) p,
:host-context(html[dir="rtl"]) span,
:host-context(html[lang="ar"]) span,
:host-context(.rtl) span,
:host-context(html[dir="rtl"]) div,
:host-context(html[lang="ar"]) div,
:host-context(.rtl) div,
:host-context(html[dir="rtl"]) .text-muted,
:host-context(html[lang="ar"]) .text-muted,
:host-context(.rtl) .text-muted,
:host-context(html[dir="rtl"]) .badge,
:host-context(html[lang="ar"]) .badge,
:host-context(.rtl) .badge,
:host-context(html[dir="rtl"]) .btn,
:host-context(html[lang="ar"]) .btn,
:host-context(.rtl) .btn,
:host-context(html[dir="rtl"]) .form-control,
:host-context(html[lang="ar"]) .form-control,
:host-context(.rtl) .form-control,
:host-context(html[dir="rtl"]) .form-label,
:host-context(html[lang="ar"]) .form-label,
:host-context(.rtl) .form-label {
  font-family: "Markazi Text", sans-serif !important;
}

/* استثناء للعناوين - تبقى Noto Kufi Arabic */
:host-context(html[dir="rtl"]) h1,
:host-context(html[lang="ar"]) h1,
:host-context(.rtl) h1,
:host-context(html[dir="rtl"]) h2,
:host-context(html[lang="ar"]) h2,
:host-context(.rtl) h2,
:host-context(html[dir="rtl"]) h3,
:host-context(html[lang="ar"]) h3,
:host-context(.rtl) h3,
:host-context(html[dir="rtl"]) h4,
:host-context(html[lang="ar"]) h4,
:host-context(.rtl) h4,
:host-context(html[dir="rtl"]) h5,
:host-context(html[lang="ar"]) h5,
:host-context(.rtl) h5,
:host-context(html[dir="rtl"]) h6,
:host-context(html[lang="ar"]) h6,
:host-context(.rtl) h6,
:host-context(html[dir="rtl"]) .property-title,
:host-context(html[lang="ar"]) .property-title,
:host-context(.rtl) .property-title {
  font-family: "Noto Kufi Arabic", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-location-text,
:host-context(html[lang="ar"]) .property-location-text,
:host-context(.rtl) .property-location-text {
  flex-direction: row-reverse !important;
  text-align: right !important;
}

/* ترتيب الأيقونات في العربية */
:host-context(html[dir="rtl"]) .property-badge-featured,
:host-context(html[lang="ar"]) .property-badge-featured,
:host-context(.rtl) .property-badge-featured {
  // flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-price-overlay,
:host-context(html[lang="ar"]) .property-price-overlay,
:host-context(.rtl) .property-price-overlay {
  // flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-location i,
:host-context(html[lang="ar"]) .property-location i,
:host-context(.rtl) .property-location i {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}

:host-context(html[dir="rtl"]) .property-content,
:host-context(html[lang="ar"]) .property-content,
:host-context(.rtl) .property-content {
  text-align: right !important;
  direction: rtl !important;
}

:host-context(html[dir="rtl"]) .property-title,
:host-context(html[lang="ar"]) .property-title,
:host-context(.rtl) .property-title {
  text-align: right !important;
}

:host-context(html[dir="rtl"]) .property-price,
:host-context(html[lang="ar"]) .property-price,
:host-context(.rtl) .property-price {
  text-align: right !important;
}

// App Preview Section RTL Support - Force positioning
:host-context(html[dir="rtl"]) .col-lg-6.d-flex,
:host-context(html[lang="ar"]) .col-lg-6.d-flex,
:host-context(.rtl) .col-lg-6.d-flex {
  justify-content: flex-end !important;

  .app-preview {
    text-align: left !important;
  }
}

:host-context(html[dir="ltr"]) .col-lg-6.d-flex,
:host-context(html[lang="en"]) .col-lg-6.d-flex {
  justify-content: flex-end !important;

  .app-preview {
    text-align: right !important;
  }
}

// Force RTL layout for app preview section
html[dir="rtl"] .app-preview,
html[lang="ar"] .app-preview,
.rtl .app-preview {
  text-align: left !important;
  direction: rtl !important;
}

html[dir="ltr"] .app-preview,
html[lang="en"] .app-preview {
  text-align: right !important;
  direction: ltr !important;
}

// Additional force for justify-content
html[dir="rtl"] .col-lg-6.d-flex.justify-content-start,
html[lang="ar"] .col-lg-6.d-flex.justify-content-start,
.rtl .col-lg-6.d-flex.justify-content-start {
  justify-content: flex-start !important;
  display: flex !important;
}

html[dir="ltr"] .col-lg-6.d-flex.justify-content-end,
html[lang="en"] .col-lg-6.d-flex.justify-content-end {
  justify-content: flex-end !important;
  display: flex !important;
}

// Super specific CSS for app preview positioning
.home-page html[dir="rtl"] .col-lg-6.d-flex,
.home-page html[lang="ar"] .col-lg-6.d-flex,
body html[dir="rtl"] .col-lg-6.d-flex,
body html[lang="ar"] .col-lg-6.d-flex {
  justify-content: flex-start !important;
}

.home-page html[dir="ltr"] .col-lg-6.d-flex,
.home-page html[lang="en"] .col-lg-6.d-flex,
body html[dir="ltr"] .col-lg-6.d-flex,
body html[lang="en"] .col-lg-6.d-flex {
  justify-content: flex-end !important;
}

// Force with Angular classes
.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

/* Property Card Fixed Dimensions */
@media (min-width: 1200px) {
  .property-card {
    width: 100% !important;
    max-width: 320px !important;
    height: 290px !important;
  }

  .properties-section .row.g-4 {
    gap: 5px !important;
    justify-content: space-between !important;

    .col-lg-3 {
      width: calc(25% - 15px) !important;
      max-width: 320px !important;
    }
  }
}

// ===== COMPREHENSIVE RESPONSIVE DESIGN FOR HOME PAGE =====

// Large screens (1024px - 1199px)
@media (max-width: 1199px) and (min-width: 1024px) {
  .property-card {
    width: 100% !important;
    max-width: 240px !important;
    height: 270px !important;
  }

  .properties-section .row.g-4 {
    gap: 8px !important;

    .col-lg-3 {
      width: calc(25% - 6px) !important;
      max-width: 240px !important;
    }
  }

  // Location cards carousel
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 10px;

      .carousel-inner {
        .carousel-item {
          .row {
            gap: 6px !important;

            .col-xl-3,
            .col-lg-6,
            .col-md-4,
            .col-sm-6 {
              width: calc(25% - 4.5px) !important;
              min-width: 180px !important;
              max-width: 200px !important;
              padding: 0 3px !important;
            }
          }
        }
      }
    }

    .location-card {
      height: 180px !important;

      .location-overlay {
        .location-info {
          h5 {
            font-size: 0.85rem !important;
          }

          p {
            font-size: 0.7rem !important;
          }
        }
      }
    }
  }

  // Property badges
  .property-top-badges {
    .property-badge-featured,
    .property-price-overlay {
      padding: 2px 5px !important;
      font-size: 0.6rem !important;

      i {
        font-size: 0.55rem !important;
      }
    }
  }

  // Property content
  .property-content {
    padding: 6px !important;

    .property-bottom-row {
      .property-rating {
        .stars i {
          font-size: 0.65rem !important;
        }

        .rating-text {
          font-size: 0.7rem !important;
        }

        .reviews-count {
          font-size: 0.65rem !important;
        }
      }

      .property-actions {
        .btn {
          padding: 3px 5px !important;

          i {
            font-size: 0.65rem !important;
          }
        }
      }
    }
  }
}

// Medium-Large screens (992px - 1023px)
@media (max-width: 1023px) and (min-width: 992px) {
  .property-card {
    width: 100% !important;
    max-width: 250px !important;
    height: 280px !important;
  }

  .properties-section .row.g-4 {
    gap: 10px !important;

    .col-lg-3 {
      width: calc(25% - 8px) !important;
      max-width: 250px !important;
    }
  }

  // Location cards
  .horizontal-carousel-section {
    .carousel-inner {
      .carousel-item {
        .row {
          gap: 8px !important;

          .col-xl-3,
          .col-lg-6,
          .col-md-4,
          .col-sm-6 {
            width: calc(25% - 6px) !important;
            min-width: 200px !important;
            max-width: 220px !important;
          }
        }
      }
    }

    .location-card {
      height: 200px !important;
    }
  }
}

// Tablet screens (768px - 991px)
@media (max-width: 991px) and (min-width: 768px) {
  .property-card {
    width: 100% !important;
    max-width: 220px !important;
    height: 300px !important;
  }

  .properties-section .row.g-4 {
    gap: 8px !important;

    .col-lg-3 {
      width: calc(50% - 4px) !important;
      max-width: 220px !important;
    }
  }

  // Location cards - 2 per row on tablet
  .horizontal-carousel-section {
    .carousel-inner {
      .carousel-item {
        .row {
          gap: 10px !important;
          justify-content: center !important;

          .col-xl-3,
          .col-lg-6,
          .col-md-4,
          .col-sm-6 {
            width: calc(50% - 5px) !important;
            min-width: 250px !important;
            max-width: 300px !important;
            padding: 0 5px !important;
          }
        }
      }
    }

    .location-card {
      height: 220px !important;

      .location-overlay {
        .location-info {
          h5 {
            font-size: 1rem !important;
          }

          p {
            font-size: 0.8rem !important;
          }
        }
      }
    }
  }

  // Property badges - normal size for tablet
  .property-top-badges {
    .property-badge-featured,
    .property-price-overlay {
      padding: 4px 8px !important;
      font-size: 0.75rem !important;

      i {
        font-size: 0.7rem !important;
      }
    }
  }

  // Property content
  .property-content {
    padding: 10px !important;

    .property-bottom-row {
      .property-rating {
        .stars i {
          font-size: 0.8rem !important;
        }

        .rating-text {
          font-size: 0.85rem !important;
        }

        .reviews-count {
          font-size: 0.8rem !important;
        }
      }

      .property-actions {
        .btn {
          padding: 5px 8px !important;

          i {
            font-size: 0.8rem !important;
          }
        }
      }
    }
  }
}

// Mobile screens (≤767px)
@media (max-width: 767px) {
  .property-card {
    width: 100% !important;
    max-width: 280px !important;
    height: 320px !important;
    margin: 0 auto !important;
  }

  .properties-section .row.g-4 {
    gap: 15px !important;

    .col-lg-3 {
      width: 100% !important;
      max-width: 280px !important;
      margin: 0 auto !important;
    }
  }

  // Location cards - 1 per row on mobile
  .horizontal-carousel-section {
    .carousel-inner {
      .carousel-item {
        .row {
          gap: 15px !important;
          justify-content: center !important;

          .col-xl-3,
          .col-lg-6,
          .col-md-4,
          .col-sm-6,
          .col-12 {
            width: 100% !important;
            min-width: 280px !important;
            max-width: 350px !important;
            padding: 0 10px !important;
          }
        }
      }
    }

    .location-card {
      height: 250px !important;
      margin: 0 auto !important;

      .location-overlay {
        .location-info {
          h5 {
            font-size: 1.1rem !important;
          }

          p {
            font-size: 0.9rem !important;
          }
        }
      }
    }
  }

  // Property badges - larger for mobile
  .property-top-badges {
    .property-badge-featured,
    .property-price-overlay {
      padding: 5px 10px !important;
      font-size: 0.8rem !important;

      i {
        font-size: 0.75rem !important;
      }
    }
  }

  // Property content
  .property-content {
    padding: 12px !important;

    .property-bottom-row {
      .property-rating {
        .stars i {
          font-size: 0.9rem !important;
        }

        .rating-text {
          font-size: 0.95rem !important;
        }

        .reviews-count {
          font-size: 0.85rem !important;
        }
      }

      .property-actions {
        .btn {
          padding: 6px 10px !important;

          i {
            font-size: 0.9rem !important;
          }
        }
      }
    }
  }
}

// Small mobile screens (≤576px)
@media (max-width: 576px) {
  .property-card {
    max-width: 300px !important;
    height: 264px !important;
  }

  .properties-section .row.g-4 {
    .col-lg-3 {
      max-width: 300px !important;
    }
  }

  // Location cards - full width on small mobile
  .horizontal-carousel-section {
    .carousel-inner {
      .carousel-item {
        .row {
          .col-xl-3,
          .col-lg-6,
          .col-md-4,
          .col-sm-6,
          .col-12 {
            min-width: 300px !important;
            max-width: 100% !important;
            padding: 0 15px !important;
          }
        }
      }
    }

    .location-card {
      height: 280px !important;
    }
  }
}

// Extra small mobile screens (≤480px)
@media (max-width: 480px) {
  .property-card {
    max-width: 280px !important;
    height: 262px !important;
  }

  .horizontal-carousel-section {
    .location-card {
      height: 260px !important;
    }
  }

  // Smaller badges for very small screens
  .property-top-badges {
    .property-badge-featured,
    .property-price-overlay {
      padding: 4px 8px !important;
      font-size: 0.75rem !important;

      i {
        font-size: 0.7rem !important;
      }
    }
  }
}

// Very small screens (≤375px)
@media (max-width: 375px) {
  .property-card {
    max-width: 260px !important;
    height: 262px !important;
  }

  .horizontal-carousel-section {
    .location-card {
      height: 240px !important;

      .location-overlay {
        .location-info {
          h5 {
            font-size: 1rem !important;
          }

          p {
            font-size: 0.8rem !important;
          }
        }
      }
    }
  }

  .property-top-badges {
    .property-badge-featured,
    .property-price-overlay {
      padding: 3px 6px !important;
      font-size: 0.7rem !important;

      i {
        font-size: 0.65rem !important;
      }
    }
  }
}

/* Property Image Responsive Dimensions */
@media (min-width: 1200px) {
  .property-image {
    width: 100% !important;
    height: 320px !important;
  }
}

@media (max-width: 1199px) and (min-width: 992px) {
  .property-image {
    width: 100% !important;
    height: 280px !important;
  }
}

@media (max-width: 991px) and (min-width: 768px) {
  .property-image {
    width: 100% !important;
    height: 250px !important;
  }
}

@media (max-width: 767px) {
  .property-image {
    width: 100% !important;
    height: 220px !important;
  }
}

// RTL Support for Articles Title - Arabic font size
:host-context(html[dir="rtl"])
  .articles-section
  .section-header
  .articles-title,
:host-context(html[lang="ar"])
  .articles-section
  .section-header
  .articles-title {
  font-size: 8.3rem !important; // Larger size for Arabic
  font-family: "Noto Kufi Arabic", sans-serif !important;
  direction: rtl !important;
  text-align: center !important;
}

// Responsive adjustments for Arabic articles title
@media (max-width: 992px) {
  :host-context(html[dir="rtl"])
    .articles-section
    .section-header
    .articles-title,
  :host-context(html[lang="ar"])
    .articles-section
    .section-header
    .articles-title {
    font-size: 3.5rem !important; // Proportionally larger for Arabic on tablets
  }
}

@media (max-width: 768px) {
  :host-context(html[dir="rtl"])
    .articles-section
    .section-header
    .articles-title,
  :host-context(html[lang="ar"])
    .articles-section
    .section-header
    .articles-title {
    font-size: 2.8rem !important; // Proportionally larger for Arabic on mobile
  }
}

@media (max-width: 576px) {
  :host-context(html[dir="rtl"])
    .articles-section
    .section-header
    .articles-title,
  :host-context(html[lang="ar"])
    .articles-section
    .section-header
    .articles-title {
    font-size: 2.4rem !important; // Proportionally larger for Arabic on small mobile
  }
}

@media (max-width: 480px) {
  :host-context(html[dir="rtl"])
    .articles-section
    .section-header
    .articles-title,
  :host-context(html[lang="ar"])
    .articles-section
    .section-header
    .articles-title {
    font-size: 2rem !important; // Proportionally larger for Arabic on very small screens
  }
}

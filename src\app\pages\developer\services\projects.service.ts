import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { Page } from 'src/app/models/page.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ProjectsService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/developers/projects`;

  getAllModels(page: Page): Observable<any> {
    const offset = page.size * page.pageNumber;

    const queryParams = {
      limit: page.size,
      offset: offset,
      sort: page.orderDir,
      sortBy: page.orderBy,
    };
    let params = this.appendPageFilter(page, queryParams);
    return this.http.get<any[]>(`${environment.apiUrl}/model`, { params });
  }

  getCities(
    cityId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (cityId) {
      params['filters[cityId]'] = cityId;
    }

    return this.http.get(`${environment.apiUrl}/location/city`, { params });
  }

  getAreas(
    cityId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (cityId) {
      params['cityId'] = cityId;
    }
    return this.http.get(`${environment.apiUrl}/location/area`, { params });
  }

  getSubAreas(
    areaId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (areaId) {
      params['areaId'] = areaId;
    }
    return this.http.get(`${environment.apiUrl}/location/sub-area`, { params });
  }

  createProject(formData: FormData): Observable<any> {
    return this.http.post(`${this.apiUrl}`, formData);
  }

  updateProject(id: number, formData: FormData): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}`, formData);
  }

  downloadModel(id: any): Observable<Blob> {
    const url = `${environment.apiUrl}/unit/download-excel-units-projects-models`;
      return this.http.post(url, { id }, {
      responseType: 'blob',
    });
  }

  uploadModel(file: File, id: any): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', id);
    const url = `${environment.apiUrl}/unit/import-excel-projects-units-models`;
    return this.http.post(url, formData);
  }
}

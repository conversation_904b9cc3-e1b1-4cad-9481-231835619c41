// Subscription form component styles
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
}

.form-label.required::after {
  content: " *";
  color: #dc3545;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.img-thumbnail {
  border-radius: 8px;
  border: 2px solid #e1e3ea;
}

h5 {
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #e1e3ea;
  padding-bottom: 0.5rem;
}

.invalid-feedback {
  font-size: 0.875rem;
}

.form-text {
  font-size: 0.8rem;
  color: #6c757d;
}

// RTL Support
[dir="rtl"] {
  .form-label.required::after {
    content: " *";
    margin-right: 0.25rem;
  }

  .card-toolbar {
    .btn {
      margin-left: 0.5rem;
      margin-right: 0;
    }
  }

  .d-flex.gap-3 {
    flex-direction: row-reverse;
  }

  // Header improvements
  .card-header {
    .d-flex.align-items-center {
      gap: 10px !important;
    }
  }
}

// Arabic font improvements
.arabic-text {
  font-family: 'Noto Kufi Arabic', sans-serif;
  line-height: 1.6;
}

.arabic-input {
  font-family: 'Hacen Liner Screen', sans-serif;
  text-align: right;
  direction: rtl;
}

import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import { TranslationService } from 'src/app/modules/i18n';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;
  allPermissions: any[] = [];

  // Search and pagination properties (following all-developers pattern)
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Sort options
  sortBy = 'id';
  sortDir = 'desc';

  constructor(
    private router: Router,
    private settingService: settingService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService,
    private translateService: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadRoles();
    this.loadAllPermissions();
  }

  loadRoles(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.settingService.getAllRolls(params).subscribe({
      next: (response) => {
        console.log(response);
        this.roles = response.data || [];
        this.totalElements = response.count || response.totalElements || 0;
        this.totalPages = Math.ceil(this.totalElements / this.pageSize);
        this.loading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.loading = false;
        Swal.fire(
          this.translateService.instant('ROLES.ERROR'),
          this.translateService.instant('ROLES.LOAD_ROLES_FAILED'),
          'error'
        );
      }
    });
  }

  addRole(): void {
    const isRTL = this.translationService.isRTL();
    const fontFamily = isRTL ? 'Hacen Liner Screen, sans-serif' : 'inherit';

    Swal.fire({
      title: this.translateService.instant('ROLES.ADD_NEW_ROLE'),
      input: 'text',
      inputLabel: this.translateService.instant('ROLES.ROLE_NAME'),
      inputPlaceholder: this.translateService.instant('ROLES.ENTER_ROLE_NAME'),
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('ROLES.CREATE_ROLE'),
      cancelButtonText: this.translateService.instant('ROLES.CANCEL'),
      customClass: {
        popup: isRTL ? 'swal2-rtl' : '',
        title: isRTL ? 'swal2-title-rtl' : '',
        input: isRTL ? 'swal2-input-rtl' : ''
      },
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return this.translateService.instant('ROLES.ROLE_NAME_REQUIRED');
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const roleData = {
          role: result.value.trim()
        };

        this.settingService.createRoles(roleData).subscribe({
          next: (response) => {
            Swal.fire({
              title: this.translateService.instant('ROLES.SUCCESS'),
              text: this.translateService.instant('ROLES.ROLE_CREATED_SUCCESS'),
              icon: 'success',
              timer: 2000,
              showConfirmButton: false,
              customClass: {
                popup: isRTL ? 'swal2-rtl' : '',
                title: isRTL ? 'swal2-title-rtl' : ''
              }
            }).then(() => {
              this.loadRoles();
            });
          },
          error: (error) => {
            console.error('Error creating role:', error);
            Swal.fire({
              title: this.translateService.instant('ROLES.ERROR'),
              text: this.translateService.instant('ROLES.ROLE_CREATE_FAILED'),
              icon: 'error',
              customClass: {
                popup: isRTL ? 'swal2-rtl' : '',
                title: isRTL ? 'swal2-title-rtl' : ''
              }
            });
          }
        });
      }
    });
  }

  onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadRoles();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadRoles();
  }

  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);


    const role = this.roles.find(r => r.id === id);
    if (role) {

      this.selectedRole = JSON.parse(JSON.stringify(role));


      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }


      this.allPermissions = this.allPermissions.map(permission => {

        const roleHasPermission = this.selectedRole.permissions.some((rolePermission: any) => {

          const permissionId = typeof rolePermission === 'string' ? rolePermission : rolePermission.id || rolePermission.name;
          return permissionId === permission.id || permissionId === permission.name;
        });

        return {
          ...permission,
          isSelected: roleHasPermission
        };
      });


      this.originalPermissions = JSON.parse(JSON.stringify(this.allPermissions));


      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.allPermissions || this.allPermissions.length === 0) {
      return false;
    }
    return this.allPermissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.allPermissions) {
      this.allPermissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {
    this.cd.detectChanges();
  }



loadAllPermissions() {
  this.settingService.AllPermission().subscribe({
    next: (response) => {
      console.log('ooooo',response)
      this.allPermissions = response.data ;
      this.cd.detectChanges();
    },
    error: (error) => {
      Swal.fire('Error loading permissions:', error);
    }
  });
}

  savePermissions(): void {
    const selectedPermissions = this.allPermissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.name);
    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = selectedPermissions;
        }

        const isRTL = this.translationService.isRTL();
        Swal.fire({
          title: this.translateService.instant('ROLES.PERMISSIONS_UPDATED_SUCCESS'),
          icon: 'success',
          customClass: {
            popup: isRTL ? 'swal2-rtl' : '',
            title: isRTL ? 'swal2-title-rtl' : ''
          }
        }).then(() => {
          this.closePermissionsModal();
          window.location.reload();
        });
      },
      error: (error) => {
        console.error('Error:', error);
        const isRTL = this.translationService.isRTL();
        Swal.fire({
          title: this.translateService.instant('ROLES.PERMISSIONS_UPDATE_FAILED'),
          icon: 'error',
          customClass: {
            popup: isRTL ? 'swal2-rtl' : '',
            title: isRTL ? 'swal2-title-rtl' : ''
          }
        }).then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
      this.allPermissions = this.allPermissions.map(permission => ({
      ...permission,
      isSelected: false
    }));
  }

}

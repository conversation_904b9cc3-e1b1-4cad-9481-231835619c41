import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { TranslationService } from '../../../modules/i18n/translation.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-language-toggle',
  templateUrl: './language-toggle.component.html',
  styleUrls: ['./language-toggle.component.scss']
})
export class LanguageToggleComponent implements OnInit, OnDestroy {
  currentLanguage: string = 'ar';
  showDropdown: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private translationService: TranslationService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Subscribe to language changes
    this.translationService.currentLanguage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(lang => {
        this.currentLanguage = lang;
      });

    // Initialize current language
    this.currentLanguage = this.translationService.getCurrentLanguage();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleDropdown(): void {
    this.showDropdown = !this.showDropdown;
  }

  selectLanguage(lang: string): void {
    if (lang !== this.currentLanguage) {
      this.translationService.setLanguage(lang);
      this.currentLanguage = lang;

      // Reload the page to apply RTL/LTR styles properly
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
    this.showDropdown = false;
  }

  getCurrentLanguageLabel(): string {
    return this.currentLanguage === 'ar' ? 'العربية' : 'English';
  }

  getOtherLanguageLabel(): string {
    return this.currentLanguage === 'ar' ? 'English' : 'العربية';
  }

  getOtherLanguageCode(): string {
    return this.currentLanguage === 'ar' ? 'en' : 'ar';
  }

  // Close dropdown when clicking outside
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.language-toggle');

    if (dropdown && !dropdown.contains(target)) {
      this.showDropdown = false;
    }
  }
}

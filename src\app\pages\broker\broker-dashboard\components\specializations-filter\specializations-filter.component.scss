// ===== SPECIALIZATIONS FILTER POSITIONING =====

// Default dropdown styling
.menu-sub-dropdown {
  display: none;
  position: absolute;
  z-index: 1050;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  border: 1px solid #e4e6ea;

  &.show {
    display: block;
  }
}

// Host element positioning
:host {
  position: absolute !important;
  top: 10% !important;
  left: 0 !important;
  right: auto !important;
  z-index: 1050 !important;
  margin-top: 0.5rem !important;

  &.show {
    display: block !important;
  }
}

// Arabic positioning (opens to the right)
:host-context(html[lang="ar"]) {
  // left: auto !important;
  // right: 0 !important;

  // Ensure proper RTL layout inside dropdown
  form {
    text-align: right;

    .fs-5 {
      text-align: right;
    }

    .form-label {
      text-align: right;
    }

    .d-flex.justify-content-end {
      justify-content: flex-start !important;
      flex-direction: row-reverse !important;

      .btn {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;

        &:first-child {
          margin-right: 0 !important;
        }
      }
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====

// Mobile screens - center the dropdown
@media (max-width: 768px) {
  :host {
    left: 50% !important;
    transform: translateX(-50%) !important;
    right: auto !important;
    // width: 280px !important;
  }

  :host-context(html[lang="ar"]) {
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
  }
}

// Large screens optimization (1024px+)
@media (min-width: 1024px) {
  :host {
    width: 350px !important;

    form {
      padding: 2rem !important;

      .fs-5 {
        font-size: 1.1rem !important;
      }

      .form-label {
        font-size: 0.95rem !important;
        margin-bottom: 0.75rem !important;
      }

      .form-select {
        padding: 0.75rem 1rem !important;
        font-size: 0.9rem !important;
      }

      .btn {
        padding: 0.6rem 1.5rem !important;
        font-size: 0.9rem !important;
      }
    }
  }

  // :host-context(html[lang="ar"]) {
  //   right: 0 !important;
  //   left: auto !important;
  // }
}

// Extra large screens
@media (min-width: 1200px) {
  :host {
    width: 380px !important;
  }
}

@media (max-width: 425px) {
 :host-context(html[lang="ar"]) {
    left: 50% !important;
    right: auto !important;

  }
}

<!-- Loading State -->
<div *ngIf="isLoading" class="d-flex justify-content-center align-items-center py-10">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">{{ getTranslatedText('LOADING') }}</span>
  </div>
  <span class="ms-3 text-gray-600">{{ getTranslatedText('LOADING') }}</span>
</div>

<!-- No History State -->
<div *ngIf="!isLoading && requestHistory.length === 0" class="text-center py-10">
  <div class="text-gray-500 fs-4 mb-3">
    <i class="ki-duotone ki-file-deleted fs-2x">
      <span class="path1"></span>
      <span class="path2"></span>
    </i>
  </div>
  <h5 class="text-gray-600 fw-bold mb-2">{{ getTranslatedText('NO_HISTORY') }}</h5>
</div>

<!-- History Items -->
<div class="d-flex align-items-center mb-10" [class.rtl-history-item]="translationService.getCurrentLanguage() === 'ar'"
  *ngFor="let history of requestHistory" [class.d-none]="isLoading">
  <!-- Icons - Show only in English -->
  <span *ngIf="checkAssign(history.status) && translationService.getCurrentLanguage() !== 'ar'">
    <span *ngIf="history.status === 'Create'" class="bullet bullet-vertical h-40px bg-danger me-5"></span>
    <span *ngIf="history.status === 'Assign'" class="bullet bullet-vertical h-40px bg-primary me-5"></span>
    <span *ngIf="history.status === 'Update_status'" class="bullet bullet-vertical h-40px bg-warning me-5"></span>
    <span *ngIf="history.status === 'Reply'" class="bullet bullet-vertical h-40px bg-success me-5"></span>
  </span>

  <div class="flex-grow-1" [class.rtl-history-content]="translationService.getCurrentLanguage() === 'ar'"
    *ngIf="checkAssign(history.status)">
    <span class="text-gray-800 fw-bolder fs-5"
      [class.rtl-description]="translationService.getCurrentLanguage() === 'ar'">
      {{ getTranslatedDescription(history.description) }}
    </span>
    <span class="text-muted fw-bold d-block" [class.rtl-date]="translationService.getCurrentLanguage() === 'ar'">
      {{ history.createdAt | date: 'fullDate' }}
    </span>
  </div>

  <span *ngIf="checkAssign(history.status)">
    <span *ngIf="history.status === 'Create'" class="badge badge-light-danger fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Assign'" class="badge badge-light-primary fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Update_status'" class="badge badge-light-warning fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Reply'" class="badge badge-light-success fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
  </span>
</div>
<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center" [class.flex-row-reverse]="translationService.isRTL()">
              <i class="fas fa-users text-primary fs-2" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()"></i>
              <div [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                <h3 class="mb-0" [style.font-size]="translationService.isRTL() ? '1.6rem' : 'inherit'">
                  {{ 'USERS.TITLE' | translate }}
                </h3>
                <span class="text-muted fs-6"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                  [style.font-size]="translationService.isRTL() ? '1rem' : 'inherit'">
                  {{ 'USERS.SUBTITLE' | translate }}
                </span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()" (click)="goBack()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'USERS.BACK' | translate }}
            </button>
            <button class="btn btn-primary btn-sm" (click)="addUser()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-plus" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'USERS.ADD_USER' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"
            [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
            [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
            {{ 'USERS.ALL_USERS' | translate }}
          </h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()">
                <i class="fas fa-search position-absolute top-50 translate-middle-y text-muted"
                  [class.start-0]="!translationService.isRTL()" [class.end-0]="translationService.isRTL()"
                  [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"></i>
                <input type="text" class="form-control form-control-sm" [class.ps-10]="!translationService.isRTL()"
                  [class.pe-10]="translationService.isRTL()" [placeholder]="'USERS.SEARCH_PLACEHOLDER' | translate"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              </div>
              <select class="form-select form-select-sm" style="width: auto;"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <option value="">{{ 'USERS.ALL_ROLES' | translate }}</option>
                <option value="admin">{{ 'USERS.ADMIN' | translate }}</option>
                <option value="broker">{{ 'USERS.BROKER' | translate }}</option>
                <option value="developer">{{ 'USERS.DEVELOPER' | translate }}</option>
                <option value="client">{{ 'USERS.CLIENT' | translate }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                  <th [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'USERS.USER' | translate }}
                  </th>
                  <th [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'USERS.ROLE' | translate }}
                  </th>
                  <th [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'USERS.STATUS' | translate }}
                  </th>
                  <th [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'USERS.LAST_LOGIN' | translate }}
                  </th>
                  <th [style.text-align]="translationService.isRTL() ? 'left' : 'right'">
                    {{ 'USERS.ACTIONS' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let user of users">
                  <td>
                    <div class="d-flex align-items-center" [class.flex-row-reverse]="translationService.isRTL()">
                      <div class="symbol symbol-45px" [class.me-3]="!translationService.isRTL()"
                        [class.ms-3]="translationService.isRTL()">
                        <img [src]="user.avatar" alt="User Avatar" class="rounded">
                      </div>
                      <div [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                        <div class="fw-bold text-gray-800">{{ user.name }}</div>
                        <div class="text-muted fs-7">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge" [ngClass]="getRoleBadgeClass(user.role)"
                      [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                      {{ user.role }}
                    </span>
                  </td>
                  <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge" [ngClass]="getStatusBadgeClass(user.status)"
                      [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                      {{ user.status }}
                    </span>
                  </td>
                  <td class="text-muted" [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ user.lastLogin }}
                  </td>
                  <td [style.text-align]="translationService.isRTL() ? 'left' : 'right'">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-light btn-active-light-primary" type="button"
                        data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu"
                        [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                        <li><a class="dropdown-item" href="#" (click)="editUser(user.id)">
                            <i class="fas fa-edit" [class.me-2]="!translationService.isRTL()"
                              [class.ms-2]="translationService.isRTL()"></i>
                            {{ 'USERS.EDIT' | translate }}
                          </a></li>
                        <li><a class="dropdown-item text-danger" href="#" (click)="deleteUser(user.id)">
                            <i class="fas fa-trash" [class.me-2]="!translationService.isRTL()"
                              [class.ms-2]="translationService.isRTL()"></i>
                            {{ 'USERS.DELETE' | translate }}
                          </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
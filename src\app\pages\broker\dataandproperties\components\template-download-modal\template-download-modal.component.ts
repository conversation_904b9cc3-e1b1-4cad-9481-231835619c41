import { Component, EventEmitter, Output } from '@angular/core';
import { TranslationService } from '../../../../../modules/i18n';

@Component({
  selector: 'app-template-download-modal',
  templateUrl: './template-download-modal.component.html',
  styleUrl: './template-download-modal.component.scss'
})
export class TemplateDownloadModalComponent {
  unitType: string = '';
  compoundType: string = '';
  operationType: string = '';
  isDownloading: boolean = false;

  constructor(public translationService: TranslationService) {}

  unitTypeOptions = [
    { key: 'APARTMENTS', value: 'apartments' },
    { key: 'DUPLEXES', value: 'duplexes' },
    { key: 'STUDIOS', value: 'studios' },
    { key: 'PENTHOUSES', value: 'penthouses' },
    { key: 'ROOFS', value: 'roofs' },
    { key: 'VILLAS', value: 'villas' },
    { key: 'I_VILLA', value: 'i_villa' },
    { key: 'TWIN_HOUSES', value: 'twin_houses' },
    { key: 'TOWN_HOUSES', value: 'town_houses' },
    { key: 'ADMINISTRATIVE_UNITS', value: 'administrative_units' },
    { key: 'MEDICAL_CLINICS', value: 'medical_clinics' },
    { key: 'PHARMACIES', value: 'pharmacies' },
    { key: 'COMMERCIAL_STORES', value: 'commercial_stores' },
    { key: 'WAREHOUSES', value: 'warehouses' },
    { key: 'FACTORY_LANDS', value: 'factory_lands' },
    { key: 'WAREHOUSES_LAND', value: 'warehouses_land' },
    { key: 'STANDALONE_VILLAS', value: 'standalone_villas' },
    { key: 'COMMERCIAL_ADMINISTRATIVE_BUILDINGS', value: 'commercial_administrative_buildings' },
    { key: 'COMMERCIAL_ADMINISTRATIVE_LANDS', value: 'commercial_administrative_lands' },
    { key: 'RESIDENTIAL_BUILDINGS', value: 'residential_buildings' },
    { key: 'RESIDENTIAL_LANDS', value: 'residential_lands' },
    { key: 'CHALETS', value: 'chalets' },
    { key: 'HOTELS', value: 'hotels' },
    { key: 'FACTORIES', value: 'factories' },
    { key: 'BASEMENTS', value: 'basements' },
    { key: 'FULL_BUILDINGS', value: 'full_buildings' },
    { key: 'COMMERCIAL_UNITS', value: 'commercial_units' },
    { key: 'SHOPS', value: 'shops' },
    { key: 'MIXED_HOUSINGS', value: 'mixed_housings' },
    { key: 'COOPERATIVES', value: 'cooperatives' },
    { key: 'YOUTH_UNITS', value: 'youth_units' },
    { key: 'GANAT_MISR', value: 'ganat_misr' },
    { key: 'DAR_MISR', value: 'dar_misr' },
    { key: 'SAKAN_MISR', value: 'sakan_misr' },
    { key: 'INDUSTRIAL_LANDS', value: 'industrial_lands' },
    { key: 'CABIN', value: 'cabin' },
    { key: 'VACATION_VILLA', value: 'vacation_villa' },
    { key: 'RESIDENTIAL_VILLA_LANDS', value: 'residential_villa_lands' },
    { key: 'RESIDENTIAL_BUILDINGS_LANDS', value: 'residential_buildings_lands' },
    { key: 'ADMINSTRATIVE_LANDS', value: 'adminstrative_lands' },
    { key: 'COMMERCIAL_LANDS', value: 'commercial_lands' },
    { key: 'MEDICAL_LANDS', value: 'medical_lands' },
    { key: 'MIXED_LANDS', value: 'mixed_lands' },
  ];

  compoundTypeOptions: { key: string; value: string }[] = [
    { key: 'OUTSIDE_COMPOUND', value: 'outside_compound' },
    { key: 'INSIDE_COMPOUND', value: 'inside_compound' },
  ];

  operationTypeOptions: { key: string; value: string }[] = [
    { key: 'SELL', value: 'sell' },
    { key: 'RENT', value: 'rent_out' },
  ];

  @Output() download = new EventEmitter<{
    unitType: string;
    compoundType: string;
    operationType: string;
  }>();

  @Output() close = new EventEmitter<void>();

  submit() {
    if (!this.unitType || !this.compoundType || !this.operationType) {
      // Show validation message
      return;
    }

    this.isDownloading = true;

    // Simulate download delay
    setTimeout(() => {
      this.download.emit({
        unitType: this.unitType,
        compoundType: this.compoundType,
        operationType: this.operationType,
      });
      this.isDownloading = false;
    }, 1000);
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    return this.translationService.getCurrentLanguage() === 'ar'
      ? this.getArabicTranslation(key)
      : this.getEnglishTranslation(key);
  }

  private getArabicTranslation(key: string): string {
    const translations: any = {
      'TITLE': 'تحميل النموذج',
      'SUBTITLE': 'اختر نوع العقار والعملية لتحميل النموذج المناسب',
      'COMPOUND_TYPE': 'نوع الكمبوند',
      'OPERATION_TYPE': 'نوع العملية',
      'UNIT_TYPE': 'نوع الوحدة',
      'SELECT_COMPOUND_TYPE': 'اختر نوع الكمبوند',
      'SELECT_OPERATION_TYPE': 'اختر نوع العملية',
      'SELECT_UNIT_TYPE': 'اختر نوع الوحدة',
      'OUTSIDE_COMPOUND': 'خارج الكمبوند',
      'INSIDE_COMPOUND': 'داخل الكمبوند',
      'SELL': 'بيع',
      'RENT': 'إيجار',
      'CANCEL': 'إلغاء',
      'DOWNLOAD': 'تحميل',
      'DOWNLOADING': 'جاري التحميل...'
    };
    return translations[key] || key;
  }

  private getEnglishTranslation(key: string): string {
    const translations: any = {
      'TITLE': 'Download Template',
      'SUBTITLE': 'Select property type and operation to download the appropriate template',
      'COMPOUND_TYPE': 'Compound Type',
      'OPERATION_TYPE': 'Operation Type',
      'UNIT_TYPE': 'Unit Type',
      'SELECT_COMPOUND_TYPE': 'Select Compound Type',
      'SELECT_OPERATION_TYPE': 'Select Operation Type',
      'SELECT_UNIT_TYPE': 'Select Unit Type',
      'OUTSIDE_COMPOUND': 'Outside Compound',
      'INSIDE_COMPOUND': 'Inside Compound',
      'SELL': 'Sell',
      'RENT': 'Rent',
      'CANCEL': 'Cancel',
      'DOWNLOAD': 'Download',
      'DOWNLOADING': 'Downloading...'
    };
    return translations[key] || key;
  }

  // Get translated option text
  getTranslatedOptionText(options: any[], value: string): string {
    const option = options.find(opt => opt.value === value);
    return option ? this.getTranslatedText(option.key) : '';
  }

  // Get translated unit type
  getTranslatedUnitType(key: string): string {
    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      const arabicTranslations: any = {
        'APARTMENTS': 'شقق',
        'DUPLEXES': 'دوبلكس',
        'STUDIOS': 'استوديوهات',
        'PENTHOUSES': 'بنتهاوس',
        'ROOFS': 'أسطح',
        'VILLAS': 'فيلل',
        'I_VILLA': 'فيلا مستقلة',
        'TWIN_HOUSES': 'توين هاوس',
        'TOWN_HOUSES': 'تاون هاوس',
        'ADMINISTRATIVE_UNITS': 'وحدات إدارية',
        'MEDICAL_CLINICS': 'عيادات طبية',
        'PHARMACIES': 'صيدليات',
        'COMMERCIAL_STORES': 'محلات تجارية',
        'WAREHOUSES': 'مخازن',
        'FACTORY_LANDS': 'أراضي مصانع',
        'WAREHOUSES_LAND': 'أراضي مخازن',
        'STANDALONE_VILLAS': 'فيلل مستقلة',
        'COMMERCIAL_ADMINISTRATIVE_BUILDINGS': 'مباني تجارية إدارية',
        'COMMERCIAL_ADMINISTRATIVE_LANDS': 'أراضي تجارية إدارية',
        'RESIDENTIAL_BUILDINGS': 'مباني سكنية',
        'RESIDENTIAL_LANDS': 'أراضي سكنية',
        'CHALETS': 'شاليهات',
        'HOTELS': 'فنادق',
        'FACTORIES': 'مصانع',
        'BASEMENTS': 'بيزمنت',
        'FULL_BUILDINGS': 'مباني كاملة',
        'COMMERCIAL_UNITS': 'وحدات تجارية',
        'SHOPS': 'محلات',
        'MIXED_HOUSINGS': 'إسكان مختلط',
        'COOPERATIVES': 'تعاونيات',
        'YOUTH_UNITS': 'وحدات شباب',
        'GANAT_MISR': 'جنة مصر',
        'DAR_MISR': 'دار مصر',
        'SAKAN_MISR': 'سكن مصر',
        'INDUSTRIAL_LANDS': 'أراضي صناعية',
        'CABIN': 'كابينة',
        'VACATION_VILLA': 'فيلا مصيفية',
        'RESIDENTIAL_VILLA_LANDS': 'أراضي فيلل سكنية',
        'RESIDENTIAL_BUILDINGS_LANDS': 'أراضي مباني سكنية',
        'ADMINSTRATIVE_LANDS': 'أراضي إدارية',
        'COMMERCIAL_LANDS': 'أراضي تجارية',
        'MEDICAL_LANDS': 'أراضي طبية',
        'MIXED_LANDS': 'أراضي مختلطة'
      };
      return arabicTranslations[key] || key;
    }

    // English translations (keep original keys as they are already in English)
    return key.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }
}


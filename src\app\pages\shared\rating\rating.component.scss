.rating-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 30px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.rating-title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
}

.rating-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.rating-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-of-type {
    border-bottom: none;
  }
}

.rating-label {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  min-width: 150px;
}

.stars-container {
  display: flex;
  gap: 8px;
  direction: ltr;
}

.star {
  font-size: 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    transform: scale(1.1);
  }

  &.filled {
    color: #ffd700;
    filter: brightness(1.2);
  }

  &:not(.filled) {
    filter: grayscale(100%) brightness(0.7);
    opacity: 0.5;
  }
}

.submit-button {
  background: linear-gradient(135deg, #4285f4, #1a73e8);
  color: white;
  border: none;
  padding: 16px 40px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #1a73e8, #1557b0);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
  }

  &:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

// RTL Layout Styles
.rtl-layout {
  direction: rtl;

  .rating-title {
    text-align: center;
    font-family: 'Noto Kufi Arabic', sans-serif;
    font-size: 30px;
    font-weight: bold;
  }
}

.rtl-form {
  direction: rtl;
}

.rtl-row {
  direction: rtl;

  &.flex-row-reverse {
    flex-direction: row-reverse;
  }
}

.rtl-label {
  text-align: right;
  font-family: 'Markazi Text', sans-serif;
  font-size: 19px;
  font-weight: 600;
}

.rtl-stars {
  direction: ltr; // Keep stars left-to-right for consistency
}

.rtl-button {
  font-family: 'Markazi Text', sans-serif;
  font-size: 19px;
  font-weight: 600;
}

// LTR specific styles
.rating-container:not(.rtl-layout) {
  .rating-row {
    direction: ltr;
  }

  .rating-label {
    text-align: left;
  }
}

// Responsive Design for All Screen Sizes

// Extra Large Screens (1200px and above)
@media (min-width: 1200px) {
  .rating-container {
    max-width: 700px;
    padding: 40px;
  }

  .rating-title {
    font-size: 32px;
    margin-bottom: 35px;
  }

  .rating-label {
    font-size: 20px;
    min-width: 180px;
  }

  .star {
    font-size: 36px;
  }

  .submit-button {
    padding: 18px 50px;
    font-size: 20px;
  }
}

// Large Screens (992px to 1199px)
@media (min-width: 992px) and (max-width: 1199px) {
  .rating-container {
    max-width: 650px;
    padding: 35px;
  }

  .rating-title {
    font-size: 30px;
    margin-bottom: 32px;
  }

  .rating-label {
    font-size: 19px;
    min-width: 170px;
  }

  .star {
    font-size: 34px;
  }

  .submit-button {
    padding: 17px 45px;
    font-size: 19px;
  }
}

// Medium Screens (768px to 991px)
@media (min-width: 768px) and (max-width: 991px) {
  .rating-container {
    max-width: 600px;
    padding: 30px;
  }

  .rating-title {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .rating-label {
    font-size: 18px;
    min-width: 150px;
  }

  .star {
    font-size: 32px;
  }

  .submit-button {
    padding: 16px 40px;
    font-size: 18px;
  }
}

// Small Screens (576px to 767px)
@media (min-width: 576px) and (max-width: 767px) {
  .rating-container {
    max-width: 95%;
    padding: 25px;
    margin: 10px auto;
  }

  .rating-title {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .rating-row {
    flex-direction: column;
    gap: 15px;
    text-align: center;

    &.rtl-row {
      flex-direction: column;
    }
  }

  .rating-label {
    font-size: 17px;
    min-width: auto;
    text-align: center;
    margin-bottom: 10px;

    &.rtl-label {
      text-align: center;
      font-size: 18px;
    }
  }

  .stars-container {
    justify-content: center;
  }

  .star {
    font-size: 30px;
  }

  .submit-button {
    padding: 15px 35px;
    font-size: 17px;
    width: 100%;
  }
}

// Extra Small Screens (425px to 575px)
@media (min-width: 425px) and (max-width: 575px) {
  .rating-container {
    max-width: 100%;
    padding: 20px;
    margin: 8px;
  }

  .rating-title {
    font-size: 22px;
    margin-bottom: 22px;
  }

  .rating-row {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 12px 0;

    &.rtl-row {
      flex-direction: column;
    }
  }

  .rating-label {
    font-size: 16px;
    min-width: auto;
    text-align: center;
    margin-bottom: 8px;

    &.rtl-label {
      text-align: center;
      font-size: 17px;
    }
  }

  .stars-container {
    justify-content: center;
    gap: 6px;
  }

  .star {
    font-size: 28px;
  }

  .submit-button {
    padding: 14px 30px;
    font-size: 16px;
    width: 100%;
    margin-top: 15px;

    &.rtl-button {
      font-size: 17px;
    }
  }
}

// Very Small Screens (320px to 424px)
@media (max-width: 424px) {
  .rating-container {
    max-width: 100%;
    padding: 15px;
    margin: 5px;
    box-sizing: border-box;
  }

  .rating-title {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .rating-form {
    gap: 15px;
  }

  .rating-row {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 10px 0;

    &.rtl-row {
      flex-direction: column;
    }
  }

  .rating-label {
    font-size: 15px;
    min-width: auto;
    text-align: center;
    margin-bottom: 8px;

    &.rtl-label {
      text-align: center;
      font-size: 16px;
    }
  }

  .stars-container {
    justify-content: center;
    gap: 5px;
  }

  .star {
    font-size: 26px;
  }

  .submit-button {
    padding: 12px 25px;
    font-size: 14px;
    width: 100%;
    margin-top: 15px;

    &.rtl-button {
      font-size: 15px;
    }
  }
}

@media  (max-width: 370px) {

  .star {
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;



  }

}

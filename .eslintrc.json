{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "no-empty-function": "off", "@typescript-eslint/no-empty-function": "off", "@angular-eslint/no-empty-lifecycle-method": "off", "@angular-eslint/no-input-rename": "off"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}]}
// ===== MOBILE FIRST RESPONSIVE DESIGN =====

// Mobile first - force vertical layout and center alignment
.card-header {
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  gap: 1rem !important;
  padding: 1rem !important;
  flex-wrap: nowrap !important;

  .d-flex.flex-column.flex-grow-1 {
    width: 100% !important;
    align-items: center !important;
    text-align: center !important;

    .d-flex.align-items-center {
      justify-content: center !important;
      text-align: center !important;
      margin-bottom: 0.75rem !important;
      flex-wrap: wrap !important;

      i, span {
        margin-right: 0.5rem !important;
      }

      .card-label {
        font-size: 1rem !important;
        line-height: 1.4 !important;
        word-wrap: break-word !important;
        text-align: center !important;
      }
    }

    .text-muted {
      text-align: center !important;
      font-size: 0.8rem !important;
      width: 100% !important;
    }
  }

  .card-toolbar {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    margin-left: 0 !important;

    .btn {
      font-size: 0.85rem !important;
      padding: 0.6rem 1.2rem !important;
      white-space: nowrap !important;
      min-width: 120px !important;
    }
  }
}

// Arabic specific fixes
:host-context(html[lang="ar"]) {
  // Mobile first - Arabic
  .card-header {
    .d-flex.flex-column.flex-grow-1 {
      .d-flex.align-items-center {
        flex-direction: row-reverse !important;
        justify-content: center !important;

        // Icon spacing for Arabic
        i, app-keenicon, span {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }

        .me-2 {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }

      .text-muted {
        text-align: center !important;
      }
    }

    .card-toolbar {
      .btn {
        i {
          margin-left: 0 !important;
          margin-right: 0.5rem !important;

          // Prevent icon flipping in Arabic
          &.fa-angles-right {
            transform: none !important;
            -webkit-transform: none !important;
          }

          // Fix all directional icons
          &[class*="angle"],
          &[class*="arrow"],
          &[class*="chevron"] {
            transform: none !important;
            -webkit-transform: none !important;
          }
        }
      }
    }
  }

  // Small screens and up - Arabic RTL
  @media (min-width: 576px) {
    .card-header {
      .d-flex.flex-column.flex-grow-1 {
        .d-flex.align-items-center {
          justify-content: flex-start !important;
        }

        .text-muted {
          text-align: right !important;
        }
      }

      .card-toolbar {
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }
    }
  }

  // Prevent all icons from flipping
  i, svg, span {
    transform: none !important;
    -webkit-transform: none !important;

    &.fa-angles-right,
    &.fa-angle-right,
    &.fa-arrow-right,
    &.fa-chevron-right {
      transform: none !important;
      -webkit-transform: none !important;
    }

    // Fix Keen Icons
    &[class*="ki-"],
    &.ki-outline,
    &.ki-solid,
    &.ki-duotone {
      transform: none !important;
      -webkit-transform: none !important;
    }
  }

  // Fix app-keenicon component
  app-keenicon {
    transform: none !important;
    -webkit-transform: none !important;

    span {
      transform: none !important;
      -webkit-transform: none !important;
    }
  }
}

// ===== COMPREHENSIVE RESPONSIVE DESIGN =====

// Mobile first (default) - Very small screens
.card-header {
  flex-direction: column !important;
  align-items: center !important;
  gap: 1rem !important;
  padding: 1rem !important;
  text-align: center !important;

  .d-flex.flex-column.flex-grow-1 {
    width: 100% !important;
    align-items: center !important;

    .d-flex.align-items-center {
      justify-content: center !important;
      text-align: center !important;
      margin-bottom: 0.75rem !important;
      flex-wrap: wrap !important;

      i, app-keenicon, span {
        margin-right: 0.5rem !important;
      }

      .card-label {
        font-size: 1rem !important;
        line-height: 1.4 !important;
        word-wrap: break-word !important;
        text-align: center !important;
      }
    }

    .text-muted {
      text-align: center !important;
      font-size: 0.8rem !important;
      width: 100% !important;
    }
  }

  .card-toolbar {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;

    .btn {
      font-size: 0.85rem !important;
      padding: 0.6rem 1.2rem !important;
      white-space: nowrap !important;
      min-width: 120px !important;
    }
  }
}

// Extra small screens (≤425px) - Force center alignment
@media (max-width: 425px) {
  .card-header {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.75rem !important;
    gap: 0.75rem !important;

    .d-flex.flex-column.flex-grow-1 {
      width: 100% !important;
      align-items: center !important;
      text-align: center !important;

      .d-flex.align-items-center {
        justify-content: center !important;
        text-align: center !important;
        flex-direction: column !important;
        gap: 0.5rem !important;
        width: 100% !important;

        i, span {
          margin-right: 0 !important;
          margin-left: 0 !important;
        }

        .card-label {
          text-align: center !important;
          font-size: 0.9rem !important;
          margin: 0 !important;
          width: 100% !important;
        }
      }

      .text-muted {
        text-align: center !important;
        font-size: 0.75rem !important;
        margin-top: 0.5rem !important;
        width: 100% !important;
      }
    }

    .card-toolbar {
      width: 100% !important;
      justify-content: center !important;
      margin-top: 0 !important;
      margin-left: 0 !important;

      .btn {
        font-size: 0.8rem !important;
        padding: 0.5rem 1rem !important;
        min-width: 100px !important;
      }
    }
  }
}

// Very small screens (≤375px) - Extra compact
@media (max-width: 375px) {
  .card-header {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.5rem !important;
    gap: 0.5rem !important;

    .d-flex.flex-column.flex-grow-1 {
      width: 100% !important;
      align-items: center !important;
      text-align: center !important;

      .d-flex.align-items-center {
        flex-direction: column !important;
        justify-content: center !important;
        text-align: center !important;
        gap: 0.25rem !important;
        width: 100% !important;

        i, span {
          margin-right: 0 !important;
          margin-left: 0 !important;
        }

        .card-label {
          font-size: 0.85rem !important;
          text-align: center !important;
          width: 100% !important;
          margin: 0 !important;
        }
      }

      .text-muted {
        font-size: 0.7rem !important;
        text-align: center !important;
        width: 100% !important;
        margin-top: 0.25rem !important;
      }
    }

    .card-toolbar {
      width: 100% !important;
      justify-content: center !important;
      margin-top: 0.5rem !important;
      margin-left: 0 !important;

      .btn {
        font-size: 0.75rem !important;
        padding: 0.4rem 0.8rem !important;
        min-width: 80px !important;
      }
    }
  }
}

// Small screens (576px+) - side by side
@media (min-width: 576px) {
  .card-header {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.25rem !important;

    .d-flex.flex-column.flex-grow-1 {
      .d-flex.align-items-center {
        justify-content: flex-start !important;
        text-align: left !important;
        margin-bottom: 0.5rem !important;

        .card-label {
          font-size: 1.1rem !important;
        }
      }

      .text-muted {
        text-align: left !important;
        font-size: 0.85rem !important;
      }
    }

    .card-toolbar {
      width: auto !important;
      justify-content: flex-end !important;
      flex-shrink: 0 !important;
      margin-left: 1rem !important;

      .btn {
        font-size: 0.9rem !important;
        padding: 0.6rem 1.2rem !important;
        min-width: auto !important;
      }
    }
  }
}

// Medium screens (768px+)
@media (min-width: 768px) {
  .card-header {
    padding: 1.5rem !important;

    .d-flex.flex-column.flex-grow-1 {
      .d-flex.align-items-center {
        .card-label {
          font-size: 1.2rem !important;
        }
      }

      .text-muted {
        font-size: 0.9rem !important;
      }
    }

    .card-toolbar {
      .btn {
        font-size: 0.9rem !important;
        padding: 0.7rem 1.4rem !important;
      }
    }
  }
}

// Large screens (1024px+)
@media (min-width: 1024px) {
  .card-header {
    padding: 1.5rem 1.5rem 1.25rem 1.5rem !important;

    .d-flex.flex-column.flex-grow-1 {
      .d-flex.align-items-center {
        .card-label {
          font-size: 1rem !important;
        }
      }

      .text-muted {
        font-size: 0.8rem !important;
      }
    }

    .card-toolbar {
      .btn {
        font-size: 0.85rem !important;
        padding: 0.5rem 1rem !important;
      }
    }
  }
}

// ===== ARABIC RESPONSIVE FIXES =====



// ===== ARABIC RESPONSIVE FIXES =====

// Arabic specific fixes for very small screens
:host-context(html[lang="ar"]) {
  // Very small screens (≤375px) - Arabic center layout
  @media (max-width: 375px) {
    .card-header {
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      text-align: center !important;

      .d-flex.flex-column.flex-grow-1 {
        align-items: center !important;
        text-align: center !important;

        .d-flex.align-items-center {
          flex-direction: row-reverse !important;
          justify-content: center !important;
          text-align: center !important;

          i, span {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
          }

          .card-label {
            text-align: center !important;
            margin: 0 !important;
            width: 100% !important;
          }
        }

        .text-muted {
          text-align: center !important;
          width: 100% !important;
        }
      }

      .card-toolbar {
        justify-content: center !important;
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
    }
  }

  // Small screens (≤425px) - Arabic center layout
  @media (max-width: 425px) {
    .card-header {
      .d-flex.flex-column.flex-grow-1 {
        .d-flex.align-items-center {
          flex-direction: row-reverse !important;

          i, span {
            margin-right: 0 !important;
            margin-left: -3.5rem !important;

          }
        }
      }
    }
  }

  // Medium screens and up - Arabic RTL
  @media (min-width: 576px) {
    .card-header {
      .d-flex.flex-column.flex-grow-1 {
        .d-flex.align-items-center {
          flex-direction: row-reverse !important;
          justify-content: flex-start !important;
          text-align: right !important;

          i, span {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
          }
        }

        .text-muted {
          text-align: right !important;
        }
      }

      .card-toolbar {
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }
    }
  }
}

// Card hover effects
.card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

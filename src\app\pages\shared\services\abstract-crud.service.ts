import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, throwError } from "rxjs";
import { Page } from "src/app/models/page.model";

@Injectable({
  providedIn: 'root',
})

export abstract class AbstractCrudService {

  protected apiUrl: string;
  protected constructor(protected http: HttpClient){

  }

  /**
   * Get records by criteria
   * @param
   */
  getAll(page: Page): Observable<any> {

    let offset = page.size * page.pageNumber;
    let queryParams= {
      limit: page.size,
      offset: offset,
      sort: page.orderDir,
      sortBy: page.orderBy,
    };
    let params = this.appendPageFilter(page, queryParams);
    return this.http.get<any[]>(this.apiUrl, {params});
  }

  getAllCategories(id: any) :Observable<any> {

    if(id != null){
      return this.http.get<any[]>(this.apiUrl + '/' + id, {});
    }else{
      return this.http.get<any[]>(this.apiUrl + '/', {});
    }
  }

  create(model: any): Observable<any> {

    return this.http.post(this.apiUrl, model);
  }

  update(id: number, model: any): Observable<any> {

    return this.http.put(this.apiUrl + '/' + id, model);
  }

  delete(id: number): Observable<any> {

    return this.http.delete(this.apiUrl + '/' + id, {});
  }

  getById(id: number): Observable<any> {

    return this.http.get(this.apiUrl + '/' + id, {});
  }

  appendPageFilter(page: Page, params: any) {

    if(page.filters) {
      Object.entries(page.filters).forEach(([key, value], index) => {
        if(value) {
          params[key] = page.filters[key];
        }
      })
    }
    return params;
  }

  protected handleError(error: HttpErrorResponse) {

    if(error.status === 0) {
      console.error('An error Occurred:', error.error);
    } else {
      console.error(`Backend Returned Code ${error.status}, body was:`, error.error);
    }
    return throwError(() => new Error('something bad happened; please try again later'));
  }

}

.table {
  border-collapse: separate;
  border-spacing: 0;

  th {
    border-bottom: 1px solid #e4e6ef;
    font-weight: 600;
    color: #181c32;
    background-color: #f8f9fa;
    padding: 1rem 0.75rem;
    vertical-align: middle;
  }

  td {
    border-bottom: 1px solid #eff2f5;
    vertical-align: middle;
    padding: 1rem 0.75rem;
  }

  .symbol img {
    object-fit: cover;
  }

  // Fix spacing between table cells
  tbody tr td {
    border-left: none;
    border-right: none;
    padding: 1rem 0.75rem;
  }

  thead tr th {
    border-left: none;
    border-right: none;
    padding: 1rem 0.75rem;
  }

  // Ensure proper spacing for first and last columns
  .ps-4 {
    padding-left: 1.5rem !important;
  }

  .pe-4 {
    padding-right: 1.5rem !important;
  }
}

.badge {
  &.badge-light-warning {
    background-color: #fff8dd;
    color: #f1bc00;
  }

  &.badge-light-success {
    background-color: #e8fff3;
    color: #50cd89;
  }

  &.badge-light-danger {
    background-color: #fff5f8;
    color: #f1416c;
  }

  &.badge-light-primary {
    background-color: #eff6ff;
    color: #009ef7;
  }

  &.badge-light-secondary {
    background-color: #f5f8fa;
    color: #7e8299;
  }
}

.text-dark-blue {
  color: #1e2129 !important;
}

.bg-light-dark-blue {
  background-color: #f8f9fa !important;
}

// LTR Support for Contract Requests (English)
.ltr-layout {
  direction: ltr;
  text-align: left;

  .table-responsive {
    overflow-x: visible !important;
    overflow: auto !important;
  }
  .table {
    direction: ltr;
    text-align: left;

    th, td {
      font-family: inherit;
      text-align: left;
      border-right: none;
      border-left: none;
      padding: 1rem 0.75rem;
      vertical-align: middle;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
      background-color: #f8f9fa;
    }

    .text-end {
      text-align: right !important;
    }

    .text-start {
      text-align: left !important;
    }

    .ps-4 {
      padding-left: 1.5rem !important;
      padding-right: 0.75rem !important;
    }

    .pe-4 {
      padding-right: 1.5rem !important;
      padding-left: 0.75rem !important;
    }

    .ms-1, .ms-2 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-1, .me-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .me-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    // Dropdown positioning
    .dropdown {
      .btn {
        font-family: inherit;
      }

      .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        text-align: left !important;
        direction: ltr !important;

        &[data-bs-popper] {
          right: 0 !important;
          left: auto !important;
          transform: translate3d(0px, 38px, 0px) !important;
        }

        .dropdown-item {
          text-align: left !important;
          direction: ltr !important;
          font-family: inherit !important;
          padding: 0.5rem 1rem !important;

          &:hover {
            background-color: #f8f9fa !important;
            text-align: left !important;
          }

          i {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
          }
        }

        .dropdown-header {
          font-family: inherit !important;
          text-align: left !important;
          direction: ltr !important;
        }
      }
    }
  }

  .badge {
    font-family: inherit;
  }

  .btn {
    font-family: inherit;
  }

  .menu-link {
    font-family: inherit;
    text-align: left;
    direction: ltr;

    i {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }
  }
}

// RTL Support for Contract Requests
.rtl-layout {
  direction: rtl;
  text-align: right;

  .table-responsive {
    overflow-x: visible !important;
    overflow: auto !important;
  }

  .table {
    direction: rtl;
    text-align: right;

    th, td {
      font-family: 'Hacen Liner Screen St', sans-serif;
      text-align: right;
      border-right: none;
      // border-left: 1px solid #eff2f5;
      padding: 1rem 0.75rem;
      vertical-align: middle;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
      background-color: #f8f9fa;
    }

    .text-end {
      text-align: left !important;
    }

    .text-start {
      text-align: right !important;
    }

    .ps-4 {
      padding-right: 1.5rem !important;
      padding-left: 0.75rem !important;
    }

    .pe-4 {
      padding-left: 1.5rem !important;
      padding-right: 0.75rem !important;
    }

    // Fix table cell spacing in RTL
    tbody tr td,
    thead tr th {
      border-left: none !important;
      border-right: none !important;
      padding: 1rem 0.75rem !important;
    }

    // Ensure consistent spacing
    .table-row-bordered tbody tr td {
      border-bottom: 1px solid #eff2f5 !important;
      border-left: none !important;
      border-right: none !important;
    }

    .ms-1, .ms-2 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .me-1, .me-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    // Dropdown positioning
    .dropdown {
      .btn {
        font-family: 'Noto Kufi Arabic', sans-serif;
      }

      .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        text-align: right !important;
        direction: rtl !important;

        &[data-bs-popper] {
          right: 0 !important;
          left: auto !important;
          transform: translate3d(0px, 38px, 0px) !important;
        }

        .dropdown-item {
          text-align: right !important;
          direction: rtl !important;
          font-family: 'Hacen Liner Screen St', sans-serif !important;
          padding: 0.5rem 1rem !important;

          &:hover {
            background-color: #f8f9fa !important;
            text-align: right !important;
          }

          i {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }

        .dropdown-header {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          text-align: right !important;
          direction: rtl !important;
        }
      }
    }
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .menu-link {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
    direction: rtl;

    i {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }
  }
}

.rtl-table-header {
  direction: rtl;
  text-align: right;

  th {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }
}

.rtl-empty-state {
  direction: rtl;
  text-align: center;

  p {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-size: 1.1rem;
  }
}

// Enhanced English support - same styling as Arabic
:host-context(html[lang="en"]) {
  .table {
    direction: ltr;
    text-align: left;

    th, td {
      font-family: inherit;
      text-align: left;
      border-right: none;
      border-left: none;
      padding: 1rem 0.75rem;
      vertical-align: middle;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
      background-color: #f8f9fa;
    }

    .text-end {
      text-align: right !important;
    }

    .text-start {
      text-align: left !important;
    }

    .ps-4 {
      padding-left: 1.5rem !important;
      padding-right: 0.75rem !important;
    }

    .pe-4 {
      padding-right: 1.5rem !important;
      padding-left: 0.75rem !important;
    }

    .ms-1, .ms-2 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-1, .me-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .me-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    // Fix table cell spacing in LTR
    tbody tr td,
    thead tr th {
      border-left: none !important;
      border-right: none !important;
      padding: 1rem 0.75rem !important;
    }

    // Ensure consistent spacing
    .table-row-bordered tbody tr td {
      border-bottom: 1px solid #eff2f5 !important;
      border-left: none !important;
      border-right: none !important;
    }

    // Dropdown positioning for English
    .dropdown {
      .btn {
        font-family: inherit;
      }

      .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        text-align: left !important;
        direction: ltr !important;
        min-width: 200px !important;

        &[data-bs-popper] {
          right: 0 !important;
          left: auto !important;
          transform: translate3d(0px, 38px, 0px) !important;
        }

        .dropdown-item {
          text-align: left !important;
          direction: ltr !important;
          font-family: inherit !important;
          padding: 0.5rem 1rem !important;
          white-space: nowrap !important;

          &:hover {
            background-color: #f8f9fa !important;
            text-align: left !important;
          }

          i {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
          }
        }

        .dropdown-header {
          font-family: inherit !important;
          text-align: left !important;
          direction: ltr !important;
          font-weight: 600 !important;
        }
      }
    }
  }

  .badge {
    font-family: inherit;
  }

  .btn {
    font-family: inherit;
  }

  .menu-link {
    font-family: inherit;
    text-align: left;
    direction: ltr;

    i {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }
  }
}

// Enhanced RTL support
:host-context(html[lang="ar"]) {
  .table {
    direction: rtl;
    text-align: right;

    th, td {
      font-family: 'Hacen Liner Screen St', sans-serif;
      text-align: right;
    }

    .text-end {
      text-align: left !important;
    }

    .text-start {
      text-align: right !important;
    }

    .ps-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .pe-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .ms-1, .ms-2 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .me-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .card-body {
    direction: rtl;
  }
}

.form-control-solid {
  background-color: #f5f8fa;
  border-color: #f5f8fa;
  color: #5e6278;

  &:focus {
    background-color: #eef3f7;
    border-color: #009ef7;
    color: #181c32;
  }
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.menu {
  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
  border-radius: 0.475rem;
}

.menu-link {
  transition: all 0.15s ease;

  &:hover {
    background-color: #f5f8fa;
    color: #009ef7;
  }
}

// Company Modal Styles
::ng-deep .company-images-modal {
  .modal-dialog {
    max-width: 800px;
  }

  .modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    border-bottom: 1px solid #eff2f5;
    padding: 1.5rem;

    .modal-title {
      font-weight: 600;
      color: #181c32;
    }
  }

  .modal-body {
    padding: 2rem;
    min-height: 400px;

    .image-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;

      img {
        border-radius: 0.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }
    }

    // No images state
    .no-images-container {
      background-color: #f8f9fa;
      border-radius: 0.75rem;
      border: 2px dashed #e4e6ef;

      i {
        opacity: 0.6;
      }

      h5,
      p {
        margin: 0;
      }
    }

    .btn-icon {
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .modal-footer {
    border-top: 1px solid #eff2f5;
    padding: 1rem 1.5rem;

    .btn {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}

// Company button hover effect
.btn-link {
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(5px);

    .fa-images {
      color: #009ef7 !important;
      transform: scale(1.2);
    }
  }
}

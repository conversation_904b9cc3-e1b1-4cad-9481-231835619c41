// Override global flex-wrap rules for specialization badges
:host {
  .d-flex.flex-wrap {
    direction: inherit !important;
    gap: 0.5rem !important;
    justify-content: flex-start !important;
  }
}

// Enhanced Arabic RTL Support for Broker Title
:host-context(html[lang="ar"]) {
  // Override global flex-wrap rules specifically for Arabic
  .d-flex.flex-wrap {
    direction: rtl !important;
    gap: 0.5rem !important;
    justify-content: flex-start !important;
  }
  .card {
    direction: rtl !important;
    text-align: right !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e5e7eb !important;
    margin-bottom: 1.5rem !important;
  }

  .card-body {
    padding: 1.5rem !important;

    .row.align-items-center {
      gap: 1rem !important;

      .col-12.col-sm-auto {
        text-align: center !important;

        .profile-image-arabic {
          width: 90px !important;
          height: 90px !important;
          border-radius: 12px !important;
          overflow: hidden !important;
          border: 3px solid #e5e7eb !important;
          margin: 0 auto !important;

          img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
          }
        }
      }

      .col {
        .row.justify-content-between {
          .col-12.col-lg-9 {
            .d-flex.align-items-center.flex-wrap {
              justify-content: flex-start !important;
              gap: 0.5rem !important;
              margin-bottom: 1rem !important;

              .text-gray-800.fs-2.fw-bolder {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 800 !important;
                color: #1e3a8a !important;
                margin: 0 !important;
              }

              a {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 700 !important;
                color: #1f2937 !important;
                text-decoration: none !important;

                &:hover {
                  color: #1e3a8a !important;
                }
              }

              .badge {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-size: 0.8rem !important;
                font-weight: 600 !important;
                padding: 0.4rem 0.8rem !important;
                border-radius: 6px !important;
                margin-right: 0.5rem !important;
                margin-left: 0 !important;
              }
            }

            .d-flex.flex-wrap.fw-bold.fs-6 {
              gap: 1rem !important;
              margin-bottom: 1rem !important;

              .d-flex.align-items-center {
                gap: 0.5rem !important;

                app-keenicon {
                  color: #1e3a8a !important;
                }

                span, a {
                  font-family: 'Hacen Liner Screen', sans-serif !important;
                  font-size: 0.95rem !important;
                  color: #6b7280 !important;

                  &:hover {
                    color: #1e3a8a !important;
                  }
                }
              }
            }
          }

          .col-lg-auto {
            &.button-container-arabic {
              text-align: left !important;
              display: flex !important;
              // justify-content: flex-start !important;
              align-items: flex-start !important;
              margin-top: 0.5rem !important;

              .btn {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-weight: 600 !important;
                border-radius: 8px !important;
                padding: 0.6rem 1.2rem !important;
                white-space: nowrap !important;
                transition: all 0.3s ease !important;
                margin: 0 !important;

                &.btn-dark-blue {
                  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
                  border: none !important;
                  color: white !important;

                  &:hover {
                    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // Text alignment fixes
  .text-lg-end {
    text-align: left !important;
  }

  .me-1, .me-5 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }

  .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
}

// Default styles for Arabic layout
.profile-image-arabic {
  width: 90px;
  height: 90px;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid #e5e7eb;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.button-container-arabic {
  text-align: left;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .btn {
    margin: 0;
    font-family: 'Hacen Liner Screen', sans-serif;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    transition: all 0.3s ease;
  }
}

// Enhanced card styling
.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

// ===== RESPONSIVE DESIGN FOR ALL SCREEN SIZES =====

// Large Desktop (1400px and above)
@media screen and (min-width: 1400px) {
  .card-body {
    padding: 2rem !important;
  }

  .text-gray-800.fs-2 {
    font-size: 2.2rem !important;
  }

  .badge {
    font-size: 0.9rem !important;
    padding: 0.5rem 1rem !important;
  }
}

// Desktop (1200px to 1399px)
@media screen and (min-width: 1200px) and (max-width: 1399px) {
  .card-body {
    padding: 1.75rem !important;
  }

  .text-gray-800.fs-2 {
    font-size: 2rem !important;
  }

  .badge {
    font-size: 0.85rem !important;
    padding: 0.45rem 0.9rem !important;
  }
}

// Large Tablet (992px to 1199px)
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .card-body {
    padding: 1.5rem !important;
  }

  .text-gray-800.fs-2 {
    font-size: 1.8rem !important;
  }

  .badge {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  .btn {
    font-size: 0.9rem !important;
    padding: 0.5rem 1rem !important;
  }
}

// Tablet (768px to 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .card-body {
    padding: 1.25rem !important;
  }

  .row.align-items-center {
    .col-12.col-sm-auto {
      margin-bottom: 1rem !important;
      text-align: center !important;
    }

    .col {
      .row.justify-content-between {
        .col-12.col-lg-9 {
          margin-bottom: 1rem !important;
        }

        .col-12.col-lg-auto {
          text-align: center !important;

          .btn {
            width: 100% !important;
            max-width: 250px !important;
            margin: 0 auto !important;
          }
        }
      }
    }
  }

  .text-gray-800.fs-2 {
    font-size: 1.6rem !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 0.35rem 0.7rem !important;
    margin-bottom: 0.5rem !important;
  }

  .symbol {
    width: 80px !important;
    height: 80px !important;
  }
}

// Small Tablet (576px to 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .card {
    margin-bottom: 1rem !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .row.align-items-center {
    .col-12.col-sm-auto {
      margin-bottom: 1rem !important;
      text-align: center !important;
    }
  }

  .text-gray-800.fs-2 {
    font-size: 1.4rem !important;
    text-align: center !important;
  }

  .d-flex.align-items-center.flex-wrap {
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
  }

  .d-flex.flex-wrap {
    justify-content: center !important;
    gap: 0.4rem !important;
  }

  .badge {
    font-size: 0.7rem !important;
    padding: 0.3rem 0.6rem !important;
    margin: 0.2rem !important;
  }

  .btn {
    width: 100% !important;
    max-width: 220px !important;
    margin: 0 auto !important;
    font-size: 0.85rem !important;
    padding: 0.6rem 1.2rem !important;
  }

  .symbol {
    width: 70px !important;
    height: 70px !important;
  }
}

// Mobile and Small Screens (575px and below) - Center Everything
@media screen and (max-width: 575px) {
  // Force center all content
  .card {
    margin: 0 auto 0.75rem auto !important;
    text-align: center !important;
  }

  .card-body {
    padding: 0.75rem !important;
    text-align: center !important;
  }

  // Center the main row
  .row.align-items-center {
    justify-content: center !important;
    text-align: center !important;

    .col-12.col-sm-auto {
      margin-bottom: 0.75rem !important;
      text-align: center !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
    }

    .col {
      text-align: center !important;

      .row.justify-content-between {
        justify-content: center !important;
        text-align: center !important;

        .col-12.col-lg-9 {
          text-align: center !important;
          margin-bottom: 1rem !important;

          // Center the name and hello section
          .d-flex.align-items-center.flex-wrap {
            justify-content: center !important;
            text-align: center !important;
            margin-bottom: 0.75rem !important;
            flex-direction: column !important;
            gap: 0.5rem !important;

            .text-gray-800.fs-2.fw-bolder {
              text-align: center !important;
              margin: 0 auto !important;
              display: block !important;
            }

            a {
              text-align: center !important;
              margin: 0 auto !important;
              display: block !important;
            }
          }

          // Center the badges row
          .row {
            justify-content: center !important;

            .col-12.d-flex.flex-wrap {
              justify-content: center !important;
              text-align: center !important;
              gap: 0.3rem !important;

              .badge {
                margin: 0.15rem !important;
                text-align: center !important;
              }
            }
          }
        }

        // Center the button
        .col-12.col-lg-auto {
          text-align: center !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          margin-top: 0.5rem !important;

          .btn {
            width: 100% !important;
            max-width: 200px !important;
            margin: 0 auto !important;
            text-align: center !important;
            justify-content: center !important;
            display: flex !important;
            align-items: center !important;
          }
        }
      }
    }
  }
}

// Mobile (375px to 575px) - Additional specific styles
@media screen and (min-width: 375px) and (max-width: 575px) {
  .card {
    margin-bottom: 0.75rem !important;
    border-radius: 8px !important;
  }

  .card-body {
    padding: 0.75rem !important;
  }

  .row.align-items-center {
    .col-12.col-sm-auto {
      margin-bottom: 0.75rem !important;
      text-align: center !important;
    }
  }

  .text-gray-800.fs-2 {
    font-size: 1.2rem !important;
    text-align: center !important;
    line-height: 1.3 !important;
  }

  .d-flex.align-items-center.flex-wrap {
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 0.75rem !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .d-flex.flex-wrap {
    justify-content: center !important;
    gap: 0.3rem !important;
  }

  .badge {
    font-size: 0.65rem !important;
    padding: 0.25rem 0.5rem !important;
    margin: 0.15rem !important;
    white-space: nowrap !important;
  }

  .btn {
    width: 100% !important;
    max-width: 200px !important;
    margin: 0 auto !important;
    font-size: 0.8rem !important;
    padding: 0.5rem 1rem !important;
  }

  .symbol {
    width: 60px !important;
    height: 60px !important;
  }

  .profile-image-arabic {
    width: 60px !important;
    height: 60px !important;
  }
}

// Small Mobile (320px to 374px)
@media screen and (max-width: 374px) {
  .card {
    margin-bottom: 0.5rem !important;
    border-radius: 6px !important;
  }

  .card-body {
    padding: 0.5rem !important;
  }

  .row.align-items-center {
    .col-12.col-sm-auto {
      margin-bottom: 0.5rem !important;
      text-align: center !important;
    }
  }

  .text-gray-800.fs-2 {
    font-size: 1rem !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }

  .d-flex.align-items-center.flex-wrap {
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 0.5rem !important;
    flex-direction: column !important;
    gap: 0.3rem !important;
  }

  .d-flex.flex-wrap {
    justify-content: center !important;
    gap: 0.2rem !important;
  }

  .badge {
    font-size: 0.6rem !important;
    padding: 0.2rem 0.4rem !important;
    margin: 0.1rem !important;
    white-space: nowrap !important;
  }

  .btn {
    width: 100% !important;
    max-width: 180px !important;
    margin: 0 auto !important;
    font-size: 0.75rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  .symbol {
    width: 50px !important;
    height: 50px !important;
  }

  .profile-image-arabic {
    width: 50px !important;
    height: 50px !important;
  }
}

// RTL Responsive Adjustments
:host-context(html[lang="ar"]) {
  @media screen and (max-width: 767px) {
    .d-flex.align-items-center.flex-wrap {
      justify-content: center !important;
      text-align: center !important;
    }

    .button-container-arabic {
      text-align: center !important;
      justify-content: center !important;
    }

    .badge {
      font-family: 'Hacen Liner Screen', sans-serif !important;
    }

    .text-gray-800.fs-2.fw-bolder,
    a {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
    }
  }

  @media screen and (max-width: 575px) {
    .text-gray-800.fs-2 {
      font-size: 1.1rem !important;
    }

    .badge {
      font-size: 0.6rem !important;
    }
  }

  @media screen and (max-width: 374px) {
    .text-gray-800.fs-2 {
      font-size: 0.95rem !important;
    }

    .badge {
      font-size: 0.55rem !important;
    }
  }
}

// Force center with highest specificity for 575px and below
@media screen and (max-width: 575px) {
  :host ::ng-deep {
    .card {
      text-align: center !important;

      .card-body {
        text-align: center !important;

        .row.align-items-center {
          justify-content: center !important;
          text-align: center !important;

          .col-12.col-sm-auto {
            text-align: center !important;
            display: flex !important;
            justify-content: center !important;
          }

          .col {
            text-align: center !important;

            .row.justify-content-between {
              justify-content: center !important;

              .col-12.col-lg-9 {
                text-align: center !important;

                .d-flex.align-items-center.flex-wrap {
                  justify-content: center !important;
                  text-align: center !important;
                  flex-direction: column !important;

                  .text-gray-800.fs-2.fw-bolder,
                  a {
                    text-align: center !important;
                    margin: 0 auto !important;
                    display: block !important;
                  }
                }

                .row {
                  justify-content: center !important;

                  .col-12.d-flex.flex-wrap {
                    justify-content: center !important;
                    text-align: center !important;
                  }
                }
              }

              .col-12.col-lg-auto {
                text-align: center !important;
                justify-content: center !important;

                .btn {
                  margin: 0 auto !important;
                  text-align: center !important;
                  justify-content: center !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

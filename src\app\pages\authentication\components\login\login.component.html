<div class="register-container">
  <!-- Registration Form Card -->
  <div class="register-card">
    <!-- Step 1: Login Form -->
    <div *ngIf="currentStep === 1" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">{{ 'AUTH.LOGIN.TITLE' | translate }}</h1>

          <form [formGroup]="loginForm" (ngSubmit)="login()">
            <!-- phone -->
            <div class="form-group">
              <label for="phone" class="form-label fs-2 fw-bold">
                <i class="ki-outline ki-user"></i>
                {{ 'AUTH.LOGIN.PHONE' | translate }} <span class="required"></span>
              </label>
              <input type="tel" id="phone" formControlName="phone" class="form-control fs-3 fw-bold"
                [class.is-invalid]="isFieldInvalid('phone')" [placeholder]="'AUTH.LOGIN.PHONE_PLACEHOLDER' | translate"
                required autocomplete="tel" (blur)="markFieldAsTouched('phone')" />
              <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
                {{ getFieldError("phone") }}
              </div>
            </div>

            <!-- Password -->
            <div class="form-group">
              <label for="password" class="form-label fs-2 fw-bold">
                <i class="ki-outline ki-lock"></i>
                {{ 'AUTH.LOGIN.PASSWORD' | translate }} <span class="required"></span>
              </label>
              <input type="password" id="password" formControlName="password" class="form-control fs-3"
                [class.is-invalid]="isFieldInvalid('password')"
                [placeholder]="'AUTH.LOGIN.PASSWORD_PLACEHOLDER' | translate" required autocomplete="current-password"
                (blur)="markFieldAsTouched('password')" />
              <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
                {{ getFieldError("password") }}
              </div>
            </div>

            <!-- Forgot Password & Remember Me Row -->
            <div class="form-row">
              <!-- Forgot Password Link (Left) -->
              <div class="forgot-password-link">
                <a (click)="goToForgotPassword()" class="forgot-link fs-4" style="cursor: pointer">{{
                  'AUTH.LOGIN.FORGOT_PASSWORD' | translate }}</a>
              </div>

              <!-- Remember Me (Right) -->
              <div class="form-check">
                <input type="checkbox" id="rememberMe" formControlName="rememberMe" class="form-check-input" />
                <label for="rememberMe" class="form-check-label fw-bold">
                  {{ 'AUTH.LOGIN.REMEMBER_ME' | translate }}
                </label>
              </div>
            </div>

            <!-- Login Error Message -->
            <div *ngIf="loginErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ loginErrorMessage }}
            </div>

            <!-- Login Button -->
            <button type="submit" class="btn btn-primary btn-verification fs-3" [class.loading]="isLoadingLogin"
              [disabled]="!isLoginFormValid() || isLoadingLogin" (click)="login()">
              <span *ngIf="isLoadingLogin" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingLogin ? ('AUTH.LOGIN.LOGGING_IN' | translate) : ('AUTH.LOGIN.LOG_IN' | translate) }}
            </button>
          </form>

          <!-- Help Text -->
          <div class="help-text register-section">
            <div class="register-text">
              <a routerLink="../register" class="register-link">
                <span class="register-message">{{ 'AUTH.GENERAL.DONT_HAVE_ACCOUNT' | translate }}
                  <span class="create-link">{{ 'AUTH.GENERAL.CREATE_ONE' | translate }}</span></span>
                <i class="ki-outline ki-arrow-right"></i>
              </a>
            </div>
          </div>

          <div class="help-text">
            {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' |
              translate }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Forgot Password Form -->
    <div *ngIf="currentStep === 2" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">{{ 'AUTH.FORGOT.TITLE' | translate }}</h1>
          <p class="step-subtitle">
            {{ 'AUTH.FORGOT.DESC' | translate }}
          </p>

          <form [formGroup]="forgotPasswordForm">
            <!-- Email Input -->
            <div class="form-group">
              <label for="forgot-email" class="form-label">
                <i class="ki-outline ki-phone"></i>
                {{ 'AUTH.FORGOT.EMAIL_OR_MOBILE' | translate }} <span class="required"></span>
              </label>
              <input type="text" id="forgot-email" formControlName="input" class="form-control"
                [class.is-invalid]="isFieldInvalid('input', forgotPasswordForm)"
                [placeholder]="'AUTH.FORGOT.EMAIL_OR_MOBILE_PLACEHOLDER' | translate" required
                (blur)="markFieldAsTouched('input', forgotPasswordForm)" />
              <div *ngIf="isFieldInvalid('input', forgotPasswordForm)" class="invalid-feedback">
                {{ getFieldError("input", forgotPasswordForm) }}
              </div>
            </div>

            <!-- Error Message -->
            <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ otpErrorMessage }}
            </div>

            <!-- Send Verification Button -->
            <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingSendOtp"
              [disabled]="!isForgotPasswordFormValid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
              <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingSendOtp ? ('AUTH.FORGOT.SENDING' | translate) : ('AUTH.FORGOT.SEND_VERIFICATION_CODE' |
              translate) }}
            </button>
          </form>

          <!-- Back to Login -->
          <div class="help-text">
            <a (click)="backToLogin()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              {{ 'AUTH.FORGOT.BACK_TO_LOGIN' | translate }}
            </a>
          </div>

          <!-- Need Help -->
          <div class="help-text">
            {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <a href="#" class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' |
              translate }}</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 3: Verification Code -->
    <div *ngIf="currentStep === 3" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">{{ 'AUTH.VERIFICATION.TITLE' | translate }}</h1>

          <!-- Verification Code Input -->
          <div class="verification-code-section" [formGroup]="forgotPasswordForm">
            <div formArrayName="verificationCode" class="verification-inputs">
              <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
                <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
                  [class.is-invalid]="ctrl.invalid && ctrl.touched" (input)="autoFocusNext($event, i)" />
              </div>
            </div>
          </div>

          <!-- Countdown Timer -->
          <div class="countdown-section">
            <span class="countdown-text" *ngIf="!showResendButton">
              {{ 'AUTH.VERIFICATION.RESEND_IN' | translate }}
              <span class="countdown-timer">
                0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
              </span>

              <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
                {{ 'AUTH.VERIFICATION.RESEND_CODE' | translate }}
              </button>
          </div>

          <!-- OTP Error Message -->
          <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
            {{ otpErrorMessage }}
          </div>

          <!-- Next Button -->
          <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
            [disabled]="!isVerificationCodeValid() || isLoadingCheckOtp" (click)="checkOTP()">
            <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ isLoadingCheckOtp ? ('AUTH.VERIFICATION.VERIFYING' | translate) : ('AUTH.VERIFICATION.VERIFIED_NEXT' |
            translate) }}
          </button>

          <!-- Back to Forgot Password -->
          <div class="help-text">
            <a (click)="backToForgotPassword()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              {{ 'AUTH.VERIFICATION.BACK_TO_FORGOT_PASSWORD' | translate }}
            </a>
          </div>

          <!-- Help Text -->
          <div class="help-text">
            {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' |
              translate }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 4: Reset Password Form -->
    <div *ngIf="currentStep === 4" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">{{ 'AUTH.RESET_PASSWORD.TITLE' | translate }}</h1>
          <p class="step-subtitle">{{ 'AUTH.RESET_PASSWORD.DESC' | translate }}</p>

          <form [formGroup]="resetPasswordForm">
            <!-- New Password -->
            <div class="form-group">
              <label for="new-password" class="form-label">
                <i class="ki-outline ki-lock"></i>
                {{ 'AUTH.RESET_PASSWORD.NEW_PASSWORD' | translate }} <span class="required"></span>
              </label>
              <input type="password" id="new-password" formControlName="newPassword" class="form-control"
                [class.is-invalid]="isFieldInvalid('newPassword', resetPasswordForm)"
                [placeholder]="'AUTH.RESET_PASSWORD.NEW_PASSWORD_PLACEHOLDER' | translate" minlength="8"
                pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
                title="Password must be at least 8 characters with uppercase, lowercase and number" required
                autocomplete="new-password" (blur)="markFieldAsTouched('newPassword', resetPasswordForm)" />
              <div *ngIf="isFieldInvalid('newPassword', resetPasswordForm)" class="invalid-feedback">
                {{ getFieldError("newPassword", resetPasswordForm) }}
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
              <label for="confirm-password" class="form-label">
                <i class="ki-outline ki-lock"></i>
                {{ 'AUTH.RESET_PASSWORD.CONFIRM_PASSWORD' | translate }} <span class="required"></span>
              </label>
              <input type="password" id="confirm-password" formControlName="password_confirmation" class="form-control"
                [class.is-invalid]="isFieldInvalid('password_confirmation', resetPasswordForm) || getFormError(resetPasswordForm)"
                [placeholder]="'AUTH.RESET_PASSWORD.CONFIRM_PASSWORD_PLACEHOLDER' | translate" required
                autocomplete="new-password" (blur)="markFieldAsTouched('password_confirmation', resetPasswordForm)" />
              <div *ngIf="isFieldInvalid('password_confirmation', resetPasswordForm)" class="invalid-feedback">
                {{ getFieldError("password_confirmation", resetPasswordForm) }}
              </div>
              <div *ngIf="getFormError(resetPasswordForm)" class="invalid-feedback">
                {{ getFormError(resetPasswordForm) }}
              </div>
            </div>

            <!-- Reset Password Error Message -->
            <div *ngIf="resetPasswordErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ resetPasswordErrorMessage }}
            </div>

            <!-- Submit Button -->
            <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingResetPassword"
              [disabled]="!isResetPasswordFormValid() || isLoadingResetPassword" (click)="submitResetPassword()">
              <span *ngIf="isLoadingResetPassword" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingResetPassword ? ('AUTH.RESET_PASSWORD.SAVING' | translate) :
              ('AUTH.RESET_PASSWORD.SAVE_NEW_PASSWORD' | translate) }}
            </button>
          </form>

          <!-- Back to Verification Code -->
          <div class="help-text">
            <a (click)="backToVerificationCode()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              {{ 'AUTH.RESET_PASSWORD.BACK_TO_VERIFICATION' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
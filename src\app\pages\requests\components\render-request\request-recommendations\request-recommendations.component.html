<div *ngIf="recommendedUnits?.length > 0">
  <div class="table-responsive">
    <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
      <thead>
        <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1"
          [class.rtl-table-header]="translationService.getCurrentLanguage() === 'ar'">
          <th class="w-25px ps-4 rounded-start" [class.rounded-start]="translationService.getCurrentLanguage() !== 'ar'"
            [class.rounded-end]="translationService.getCurrentLanguage() === 'ar'">
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input" type="checkbox" [checked]="isAllSelected"
                (change)="toggleAllUnits($event)" />
            </div>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('unit_number')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('UNIT_NUMBER') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('unit_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('floor')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('FLOOR') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('floor') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('property_number')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('PROPERTY_NUMBER') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('property_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('area')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('AREA') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('area') }}</span>
          </th>
          <th class="min-w-50px cursor-pointer" (click)="sortData('rooms')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('ROOMS') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('rooms') }}</span>
          </th>
          <th class="min-w-50px cursor-pointer" (click)="sortData('bathrooms')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('BATHROOMS') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('bathrooms') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('view')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('VIEW') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('view') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_date')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('DELIVERY_DATE') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('delivery_date') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_status')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('FINISHING_STATUS') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('finishing_status') }}</span>
          </th>
          <th class="min-w-150px" [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('UNIT_PLAN') }}
          </th>
          <th class="min-w-250px cursor-pointer" (click)="sortData('unit_location')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('UNIT_LOCATION_MASTER_PLAN') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('unit_location') }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('price_per_meter_cash')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('PRICE_PER_METER_CASH') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('price_per_meter_cash') }}</span>
          </th>
          <th class="min-w-250px cursor-pointer" (click)="sortData('price_per_meter_installment')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('PRICE_PER_METER_INSTALLMENT') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('price_per_meter_installment') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('total_price_cash')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('TOTAL_PRICE_CASH') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('total_price_cash') }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('total_price_installment')"
            [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('TOTAL_PRICE_INSTALLMENT') }}
            <span [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
              [class.me-1]="translationService.getCurrentLanguage() === 'ar'" class="text-primary fw-bold">{{
              getSortArrow('total_price_installment') }}</span>
          </th>
          <th class="min-w-150px" [class.text-end]="translationService.getCurrentLanguage() === 'ar'">{{
            getTranslatedText('OTHER_ACCESSORIES') }}</th>
          <th class="min-w-50px text-end rounded-end pe-4"
            [class.rounded-end]="translationService.getCurrentLanguage() !== 'ar'"
            [class.rounded-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
            [class.ps-4]="translationService.getCurrentLanguage() === 'ar'"
            [class.pe-0]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('STATUS') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let unit of recommendedUnits">
          <td class="ps-4">
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input widget-13-check" type="checkbox" [checked]="isUnitSelected(unit.id)"
                (change)="onUnitCheckboxChange($event, unit.id)" />
            </div>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.unitNumber }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.floor }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5"> {{ unit.buildingNumber }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.unitArea | number:'1.0-2' }} m²</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfRooms }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfBathrooms }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.view }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.deliveryDate }}</span>
          </td>
          <td>
            <span *ngIf="unit.finishingType === 'On Brick'" class="badge badge-light-danger fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Semi finished'" class="badge badge-light-danger fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Company finished'" class="badge badge-light-success fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Super Lux'" class="badge badge-light-info fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Ultra Super Lux'" class="badge badge-light-info fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Standard'" class="badge badge-light-info fs-5">
              {{ unit.finishingType }}
            </span>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <button class="btn btn-sm btn-light-info fs-5" (click)="showUnitPlanModal(unit.diagram)"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
              <i class="fa-solid fa-file-image" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i> {{ getTranslatedText('VIEW_PLAN')
              }}
            </button>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <button class="btn btn-sm btn-light-info fs-5" (click)="showUnitPlanModal(unit.locationInMasterPlan)"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
              <i class="fa-solid fa-file-image" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i> {{
              getTranslatedText('VIEW_LOCATION') }}
            </button>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <span class="badge badge-light-warning fw-bold fs-5 p-3"
              [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ unit.pricePerMeterInCash ?? 0 }}
              {{ getTranslatedText('EGP') }}</span>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <span class="badge badge-light-primary fw-bold fs-5 p-3"
              [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ unit.pricePerMeterInInstallment ??
              0 }} {{ getTranslatedText('EGP') }}</span>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <span class="badge badge-light-warning fw-bold fs-5 p-3"
              [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ unit.totalPriceInCash ?? 0 }} {{
              getTranslatedText('EGP') }}</span>
          </td>
          <td [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
            <span class="badge badge-light-primary fw-bold fs-5 p-3"
              [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ unit.totalPriceInInstallment ?? 0
              }} {{ getTranslatedText('EGP') }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ formatAccessories(unit.otherAccessories) }}</span>
          </td>
          <td class="text-end pe-4">
            <span *ngIf="unit.status === 'sold'" class="badge badge-light-danger fw-bold fs-5 p-3">{{ unit.status
              }}</span>
            <span *ngIf="unit.status === 'available'" class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.status
              }}</span>
            <span *ngIf="unit.status === 'new'" class="badge badge-light-success fw-bold fs-5 p-3">{{ unit.status
              }}</span>
            <span *ngIf="unit.status === 'reserved'" class="badge badge-light-info fw-bold fs-5 p-3">{{ unit.status
              }}</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>

  <div class="text-center mt-3 mb-3" [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
    <a class="btn btn-md btn-dark-blue fw-bold" (click)="makeReply()" [class.disabled]="isReplied"
      [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
      <i class="fa-solid fa-reply-all text-white" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
        [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
      {{ getTranslatedText('REPLY') }}
    </a>
  </div>

  <!-- View Unit Plan Modal -->
  <div class="modal fade" id="viewUnitPlanModal" tabindex="-1" aria-labelledby="unitPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header" [class.rtl-modal-header]="translationService.getCurrentLanguage() === 'ar'">
          <h5 class="modal-title" id="unitPlanModalLabel"
            [class.rtl-modal-title]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('VIEW') }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
            [class.rtl-close-btn]="translationService.getCurrentLanguage() === 'ar'"></button>
        </div>
        <div class="modal-body text-center" [class.rtl-modal-body]="translationService.getCurrentLanguage() === 'ar'">
          <div *ngIf="selectedUnitPlanImage; else noImage">
            <img [src]="selectedUnitPlanImage" [alt]="getTranslatedText('UNIT_PLAN')" class="img-fluid rounded"
              style="max-height: 500px" />
            <div class="mt-3">
              <p class="text-muted" [class.rtl-text]="translationService.getCurrentLanguage() === 'ar'">{{
                getTranslatedText('IMAGE') }}</p>
            </div>
          </div>
          <ng-template #noImage>
            <div class="alert alert-warning" [class.rtl-alert]="translationService.getCurrentLanguage() === 'ar'">{{
              getTranslatedText('NO_AVAILABLE') }}</div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="recommendedUnits?.length == 0">
  <div class="row mb-5">
    <div class="col-md-5">
      <div class="d-flex align-items-center bg-light-dark-blue rounded p-5" role="alert" aria-live="polite">
        <span class="svg-icon text-info me-5" aria-hidden="true">
          <i class="fas fa-exclamation-circle ms-1 fs-5 text-dark-blue"></i>
        </span>
        <div class="flex-grow-1 me-2">
          <span class="fw-bolder text-dark-blue fs-6"
            [class.rtl-alert-title]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('NO_MATCHING_UNITS') }}
          </span>
          <span class="text-muted fw-bold d-block"
            [class.rtl-alert-message]="translationService.getCurrentLanguage() === 'ar'">
            {{ getTranslatedText('NO_RECOMMENDATIONS_MESSAGE') }}
          </span>
        </div>
      </div>
    </div>
    <div class="col-md-7"></div>
  </div>
</div>
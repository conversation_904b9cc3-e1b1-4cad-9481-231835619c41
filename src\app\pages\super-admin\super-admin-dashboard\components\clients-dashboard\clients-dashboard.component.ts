import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UsersService } from '../../../services/users.service';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-clients-dashboard',
  templateUrl: './clients-dashboard.component.html',
  styleUrls: ['./clients-dashboard.component.scss']
})
export class ClientsDashboardComponent implements OnInit {

  // Client statistics
  loading = false;
  totalClients: number = 0;
  totalComplaints: number = 0;
  RegisteredMonthlyActivity: any[] = [];
  visitedMonthlyActivity: any[] = [];
  requestTypeStats: any[] = [];
  mostRequests: any[] = [];
  totalRequests: number = 0;
  totalVisited: number = 0;

  constructor(
    private cd: ChangeDetectorRef,
    private userService: UsersService,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadClientStatistics();
    this.loadNoOfClients();
    this.loadNoOfClientsComplaints();
    this.loadRequestTypeStats();
  }

  refresh() {
    try {
      this.loadClientStatistics();
      this.loadNoOfClients();
      this.loadNoOfClientsComplaints();
      this.loadRequestTypeStats();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  }

  loadClientStatistics() {
    this.loading = true;
    this.userService.loadRegisteredMonthlyActivity().subscribe({
      next: (response:any) => {
        console.log(response);
        this.RegisteredMonthlyActivity = response.data;
        this.cd.detectChanges();
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
      },
    });

    this.userService.loadVisitedMonthlyActivity().subscribe({
      next: (response:any) => {
        console.log(response);
        this.visitedMonthlyActivity = response.data.count.monthlyVisitors;
        this.totalVisited = response.data.count.totalCountOfVisitors;
        this.cd.detectChanges();
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
      },
    });
    this.loading = false;
  }

 loadNoOfClients() {
    this.loading = true;
    this.userService.loadNoOfClients().subscribe({
      next: (response:any) => {
        const roles = response.data;
        const clientData = roles.find((item: any) => item.role === 'client');
        this.totalClients = clientData ? clientData.count : 0;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfClientsComplaints() {
    this.loading = true;
    this.userService.loadNoOfClientsComplaints().subscribe({
      next: (response:any) => {
        console.log(response);
        this.totalComplaints = response.data.clientComplaints;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadRequestTypeStats() {
    this.loading = true;
    this.userService.loadRequestTypeStats().subscribe({
      next: (response:any) => {
        console.log(response);
        const stats = response.data.unitOperationCount;
        const total = stats.reduce((sum: number, item: any) => sum + item.count, 0);
        this.totalRequests = total;
        this.requestTypeStats = stats.map((item: any) => ({
          ...item,
          percentage: total > 0 ? ((item.count / total) * 100).toFixed(0) : '0'
        }));

        const stats2 = response.data.mostUnitTypeAreaCount;
        const total2 = stats2.reduce((sum: number, item: any) => sum + item.count, 0);
        this.mostRequests = stats2.map((item: any) => ({
          ...item,
          percentage: total2 > 0 ? ((item.count / total2) * 100).toFixed(0) : '0'
        }));

        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }


  // loadTopClients() {}
  // loadRequestStats() {}
  // loadClientBehavior() {}
  // loadSatisfactionStats() {}
  // loadBudgetRangeStats() {}
  // loadGeographicStats() {}

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'جديد': return 'primary';
      case 'قيد المراجعة': return 'warning';
      case 'مكتمل': return 'success';
      case 'ملغي': return 'danger';
      case 'نشط': return 'success';
      default: return 'secondary';
    }
  }

  getPerformanceColor(value: number): string {
    if (value >= 80) return 'success';
    if (value >= 60) return 'primary';
    if (value >= 40) return 'warning';
    return 'danger';
  }

  getSatisfactionColor(type: string): string {
    switch (type) {
      case 'veryHappy': return 'success';
      case 'happy': return 'primary';
      case 'neutral': return 'warning';
      case 'unhappy': return 'danger';
      case 'veryUnhappy': return 'dark';
      default: return 'secondary';
    }
  }

  getSatisfactionLabel(type: string): string {
    switch (type) {
      case 'veryHappy': return 'راضي جداً';
      case 'happy': return 'راضي';
      case 'neutral': return 'محايد';
      case 'unhappy': return 'غير راضي';
      case 'veryUnhappy': return 'غير راضي جداً';
      default: return '';
    }
  }

  getConversionColor(rate: number): string {
    if (rate >= 8) return 'success';
    if (rate >= 6) return 'primary';
    if (rate >= 4) return 'warning';
    return 'danger';
  }


    getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'APARTMENTS': 'شقة',
        'DUPLEXES': 'دوبلكس',
        'STUDIOS': 'استوديو',
        'PENTHOUSES': 'بنت هاوس',
        'ROOFS': 'روف',
        'VILLAS': 'فيلا',
        'I_VILLA': 'اى فيلا',
        'TWIN_HOUSES': 'توين هاوس',
        'TOWN_HOUSES': 'تاون هاوس',
        'ADMINISTRATIVE_UNITS': 'وحدة ادارية',
        'MEDICAL_CLINICS': 'عيادة طبية',
        'PHARMACIES': 'صيدلية',
        'COMMERCIAL_STORES': 'محل تجارى',
        'WAREHOUSES': 'مخزن',
        'FACTORY_LANDS': 'ارض مصنع',
        'WAREHOUSES_LAND': 'ارض مخزن',
        'STANDALONE_VILLAS': 'فيلا مستقلة',
        'COMMERCIAL_ADMINISTRATIVE_BUILDINGS': 'مبنى ادارى تجارى',
        'COMMERCIAL_ADMINISTRATIVE_LANDS': 'ارض ادارى تجارى',
        'RESIDENTIAL_BUILDINGS': 'مبنى سكنى',
        'RESIDENTIAL_LANDS': 'ارض سكنى',
        'CHALETS': 'شاليه',
        'HOTELS': 'فندق',
        'FACTORIES': 'مصنع',
        'BASEMENTS': 'بيزمنت',
        'FULL_BUILDINGS': 'عمارة كاملة',
        'COMMERCIAL_UNITS': 'وحدة تجارية',
        'SHOPS': 'محل',
        'MIXED_HOUSINGS': 'منزل مختلط',
        'COOPERATIVES': 'التعاونيات',
        'YOUTH_UNITS': 'وحدات الشباب',
        'GANAT_MISR': 'جنت مصر',
        'DAR_MISR': 'دار مصر',
        'SAKAN_MISR': 'سكن مصر',
        'INDUSTRIAL_LANDS': 'الأراضي الصناعية',
        'CABIN': 'كابين',
        'VACATION_VILLA': 'فيلا مصيفية',
        'RESIDENTIAL_VILLA_LANDS': 'ارض فيلا سكنية',
        'RESIDENTIAL_BUILDINGS_LANDS': 'ارض مبنى سكنية',
        'ADMINSTRATIVE_LANDS': 'ارض ادارى',
        'COMMERCIAL_LANDS': 'ارض تجارى',
        'MEDICAL_LANDS': 'ارض طبى',
        'MIXED_LANDS': 'ارض مختلطة',
        'PURCHASE_SELL_OUTSIDE_COMPOUND': 'شراء وبيع خارج الكمبوند',
        'PRIMARY_INSIDE_COMPOUND': 'برايمري داخل الكمبوند',
        'RESALE_INSIDE_COMPOUND': 'اعادة بيع داخل الكمبوند',
        'RENTALS_OUTSIDE_COMPOUND': 'إيجارات خارج الكمبوند',
        'RENTALS_INSIDE_COMPOUND': 'إيجارات داخل الكمبوند',
        'SELL': 'بيع',
        'PURCHASING': 'شراء',
        'RENT_OUT': 'مؤجر',
        'RENT_IN': 'مستأجر'
      },
      'en': {
        'APARTMENTS': 'Apartments',
        'DUPLEXES': 'Duplexes',
        'STUDIOS': 'Studios',
        'PENTHOUSES': 'Penthouses',
        'ROOFS': 'Roofs',
        'VILLAS': 'Villas',
        'I_VILLA': 'I Villa',
        'TWIN_HOUSES': 'Twin Houses',
        'TOWN_HOUSES': 'Town Houses',
        'ADMINISTRATIVE_UNITS': 'Administrative Units',
        'MEDICAL_CLINICS': 'Medical Clinics',
        'PHARMACIES': 'Pharmacies',
        'COMMERCIAL_STORES': 'Commercial Stores',
        'WAREHOUSES': 'Warehouses',
        'FACTORY_LANDS': 'Factory Lands',
        'WAREHOUSES_LAND': 'Warehouses Land',
        'STANDALONE_VILLAS': 'Standalone Villas',
        'COMMERCIAL_ADMINISTRATIVE_BUILDINGS': 'Commercial Administrative Buildings',
        'COMMERCIAL_ADMINISTRATIVE_LANDS': 'Commercial Administrative Lands',
        'RESIDENTIAL_BUILDINGS': 'Residential Buildings',
        'RESIDENTIAL_LANDS': 'Residential Lands',
        'CHALETS': 'Chalets',
        'HOTELS': 'Hotels',
        'FACTORIES': 'Factories',
        'BASEMENTS': 'Basements',
        'FULL_BUILDINGS': 'Full Buildings',
        'COMMERCIAL_UNITS': 'Commercial Units',
        'SHOPS': 'Shops',
        'MIXED_HOUSINGS': 'Mixed Housings',
        'COOPERATIVES': 'Cooperatives',
        'YOUTH_UNITS': 'Youth Units',
        'GANAT_MISR': 'Ganat Misr',
        'DAR_MISR': 'Dar Misr',
        'SAKAN_MISR': 'Sakan Misr',
        'INDUSTRIAL_LANDS': 'Industrial Lands',
        'CABIN': 'Cabin',
        'VACATION_VILLA': 'Vacation Villa',
        'RESIDENTIAL_VILLA_LANDS': 'Residential Villa Lands',
        'RESIDENTIAL_BUILDINGS_LANDS': 'Residential Buildings Lands',
        'ADMINSTRATIVE_LANDS': 'Administrative Lands',
        'COMMERCIAL_LANDS': 'Commercial Lands',
        'MEDICAL_LANDS': 'Medical Lands',
        'MIXED_LANDS': 'Mixed Lands',
        'PURCHASE_SELL_OUTSIDE_COMPOUND': 'Purchase Sell Outside Compound',
        'PRIMARY_INSIDE_COMPOUND': 'Primary Inside Compound',
        'RESALE_INSIDE_COMPOUND': 'Resale Inside Compound',
        'RENTALS_OUTSIDE_COMPOUND': 'Rentals Outside Compound',
        'RENTALS_INSIDE_COMPOUND': 'Rentals Inside Compound',
        'SELL': 'Sell',
        'PURCHASING': 'Purchasing',
        'RENT_OUT': 'Rent Out',
        'RENT_IN': 'Rent In'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
    getTranslatedUnitType(unitType: string): string {
    if (!unitType) return unitType;

    switch (unitType.toLowerCase()) {
      case 'apartments':
        return this.getTranslatedText('APARTMENTS');
      case 'duplexes':
        return this.getTranslatedText('DUPLEXES');
      case 'studios':
        return this.getTranslatedText('STUDIOS');
      case 'penthouses':
        return this.getTranslatedText('PENTHOUSES');
      case 'roofs':
        return this.getTranslatedText('ROOFS');
      case 'villas':
        return this.getTranslatedText('VILLAS');
      case 'i_villa':
        return this.getTranslatedText('I_VILLA');
      case 'twin_houses':
        return this.getTranslatedText('TWIN_HOUSES');
      case 'town_houses':
        return this.getTranslatedText('TOWN_HOUSES');
      case 'administrative_units':
        return this.getTranslatedText('ADMINISTRATIVE_UNITS');
      case 'medical_clinics':
        return this.getTranslatedText('MEDICAL_CLINICS');
      case 'pharmacies':
        return this.getTranslatedText('PHARMACIES');
      case 'commercial_stores':
        return this.getTranslatedText('COMMERCIAL_STORES');
      case 'warehouses':
        return this.getTranslatedText('WAREHOUSES');
      case 'factory_lands':
        return this.getTranslatedText('FACTORY_LANDS');
      case 'warehouses_land':
        return this.getTranslatedText('WAREHOUSES_LAND');
      case 'standalone_villas':
        return this.getTranslatedText('STANDALONE_VILLAS');
      case 'commercial_administrative_buildings':
        return this.getTranslatedText('COMMERCIAL_ADMINISTRATIVE_BUILDINGS');
      case 'commercial_administrative_lands':
        return this.getTranslatedText('COMMERCIAL_ADMINISTRATIVE_LANDS');
      case 'residential_buildings':
        return this.getTranslatedText('RESIDENTIAL_BUILDINGS');
      case 'residential_lands':
        return this.getTranslatedText('RESIDENTIAL_LANDS');
      case 'chalets':
        return this.getTranslatedText('CHALETS');
      case 'hotels':
        return this.getTranslatedText('HOTELS');
      case 'factories':
        return this.getTranslatedText('FACTORIES');
      case 'basements':
        return this.getTranslatedText('BASEMENTS');
      case 'full_buildings':
        return this.getTranslatedText('FULL_BUILDINGS');
      case 'commercial_units':
        return this.getTranslatedText('COMMERCIAL_UNITS');
      case 'shops':
        return this.getTranslatedText('SHOPS');
      case 'mixed_housings':
        return this.getTranslatedText('MIXED_HOUSINGS');
      case 'cooperatives':
        return this.getTranslatedText('COOPERATIVES');
      case 'youth_units':
        return this.getTranslatedText('YOUTH_UNITS');
      case 'ganat_misr':
        return this.getTranslatedText('GANAT_MISR');
      case 'dar_misr':
        return this.getTranslatedText('DAR_MISR');
      case 'sakan_misr':
        return this.getTranslatedText('SAKAN_MISR');
      case 'industrial_lands':
        return this.getTranslatedText('INDUSTRIAL_LANDS');
      case 'cabin':
        return this.getTranslatedText('CABIN');
      case 'vacation_villa':
        return this.getTranslatedText('VACATION_VILLA');
      case 'residential_villa_lands':
        return this.getTranslatedText('RESIDENTIAL_VILLA_LANDS');
      case 'residential_buildings_lands':
        return this.getTranslatedText('RESIDENTIAL_BUILDINGS_LANDS');
      case 'adminstrative_lands':
        return this.getTranslatedText('ADMINSTRATIVE_LANDS');
      case 'commercial_lands':
        return this.getTranslatedText('COMMERCIAL_LANDS');
      case 'medical_lands':
        return this.getTranslatedText('MEDICAL_LANDS');
      case 'mixed_lands':
        return this.getTranslatedText('MIXED_LANDS');
      default:
        return unitType;
    }
  }
    getTranslatedOperationType(operationType: string): string {
    if (!operationType) return operationType;

    switch (operationType.toLowerCase()) {
      case 'sell':
        return this.getTranslatedText('SELL');
      case 'purchasing':
        return this.getTranslatedText('PURCHASING');
      case 'rent_out':
        return this.getTranslatedText('RENT_OUT');
      case 'rent_in':
        return this.getTranslatedText('RENT_IN');
      default:
        return operationType;
    }
  }
}

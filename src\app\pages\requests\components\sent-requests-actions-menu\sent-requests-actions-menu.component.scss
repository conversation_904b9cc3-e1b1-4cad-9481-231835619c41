// ===== RESPONSIVE DROPDOWN MENU FIXES =====

// Container positioning
.position-relative {
  position: relative !important;
}

// Button styling
.btn.btn-sm.btn-icon {
  padding: 0.4rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }
}

// Main dropdown menu
.menu.menu-sub.menu-sub-dropdown {
  // Base positioning and size
  position: absolute !important;
  z-index: 1050 !important;
  min-width: 180px !important;
  max-width: 220px !important;
  width: auto !important;

  // Responsive positioning - close to button
  right: 0 !important;
  left: auto !important;
  top: 100% !important;
  transform: translateX(-5px) !important;

  // Background and styling
  background: #ffffff !important;
  border: 1px solid #e4e6ef !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1) !important;
  padding: 0.5rem 0 !important;
  margin-top: 0.25rem !important;

  // Ensure it doesn't overflow screen
  max-height: 400px !important;
  overflow-y: auto !important;
}

// Menu items
.menu-item {
  padding: 0 !important;

  .menu-content {
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #181c32 !important;
    border-bottom: 1px solid #f1f1f2 !important;
    margin-bottom: 0.5rem !important;
  }

  .menu-link {
    display: flex !important;
    align-items: center !important;
    padding: 0.6rem 1rem !important;
    color: #5e6278 !important;
    text-decoration: none !important;
    font-size: 0.875rem !important;
    transition: all 0.2s ease !important;
    border-radius: 0 !important;

    &:hover {
      background-color: #f5f8fa !important;
      color: #181c32 !important;
    }

    i {
      margin-right: 0.5rem !important;
      font-size: 0.875rem !important;
      width: 16px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
}

// Separator styling
.separator {
  height: 1px !important;
  background-color: #f1f1f2 !important;
  margin: 0.5rem 0 !important;
  border: none !important;
}

// Button inside menu
.menu-content .btn {
  padding: 0.5rem 1rem !important;
  font-size: 0.8rem !important;
  border-radius: 0.5rem !important;
  width: 100% !important;

  i {
    margin-right: 0.4rem !important;
    font-size: 0.8rem !important;
  }
}

// ===== RESPONSIVE BREAKPOINTS =====

// Large screens (1200px+) - Close to button
@media screen and (min-width: 1200px) {
  .menu.menu-sub.menu-sub-dropdown {
    transform: translateX(-2px) !important;
    min-width: 200px !important;
  }
}

// Medium screens (768px - 1199px) - Slightly left
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .menu.menu-sub.menu-sub-dropdown {
    transform: translateX(-10px) !important;
    min-width: 170px !important;
    max-width: 200px !important;
  }

  .menu-item {
    .menu-content {
      padding: 0.6rem 0.8rem !important;
      font-size: 0.8rem !important;
    }

    .menu-link {
      padding: 0.5rem 0.8rem !important;
      font-size: 0.8rem !important;

      i {
        font-size: 0.8rem !important;
      }
    }
  }

  .menu-content .btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.75rem !important;
  }
}

// Small screens (576px - 767px) - Moderate left movement
@media screen and (min-width: 576px) and (max-width: 767px) {
  .menu.menu-sub.menu-sub-dropdown {
    transform: translateX(-20px) !important;
    min-width: 160px !important;
    max-width: 180px !important;
  }

  .menu-item {
    .menu-content {
      padding: 0.5rem 0.7rem !important;
      font-size: 0.75rem !important;
    }

    .menu-link {
      padding: 0.45rem 0.7rem !important;
      font-size: 0.75rem !important;

      i {
        font-size: 0.75rem !important;
        margin-right: 0.4rem !important;
      }
    }
  }

  .menu-content .btn {
    padding: 0.35rem 0.7rem !important;
    font-size: 0.7rem !important;
  }
}

// Extra small screens (≤575px) - Moderate left movement
@media screen and (max-width: 575px) {
  .menu.menu-sub.menu-sub-dropdown {
    transform: translateX(-30px) !important;
    min-width: 140px !important;
    max-width: 160px !important;
    border-radius: 0.5rem !important;
  }

  .menu-item {
    .menu-content {
      padding: 0.4rem 0.6rem !important;
      font-size: 0.7rem !important;
    }

    .menu-link {
      padding: 0.4rem 0.6rem !important;
      font-size: 0.7rem !important;

      i {
        font-size: 0.7rem !important;
        margin-right: 0.3rem !important;
      }
    }
  }

  .menu-content .btn {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.65rem !important;

    i {
      margin-right: 0.3rem !important;
    }
  }
}

// ===== RTL SUPPORT =====

:host-context(html[lang="ar"]) {
  .menu.menu-sub.menu-sub-dropdown {
    right: auto !important;
    left: 0 !important;
    transform: translateX(5px) !important;
  }

  .menu-item {
    .menu-content {
      text-align: right !important;
      font-family: 'Noto Kufi Arabic', sans-serif !important;
    }

    .menu-link {
      text-align: right !important;
      font-family: 'Markazi Text', sans-serif !important;

      i {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }
    }
  }

  .menu-content .btn {
    font-family: 'Markazi Text', sans-serif !important;

    i {
      margin-right: 0 !important;
      margin-left: 0.4rem !important;
    }
  }

  // RTL responsive adjustments
  @media screen and (min-width: 768px) and (max-width: 1199px) {
    .menu.menu-sub.menu-sub-dropdown {
      transform: translateX(10px) !important;
    }

    .menu-item .menu-link i {
      margin-left: 0.4rem !important;
    }
  }

  @media screen and (min-width: 576px) and (max-width: 767px) {
    .menu.menu-sub.menu-sub-dropdown {
      transform: translateX(20px) !important;
    }

    .menu-item .menu-link i {
      margin-left: 0.4rem !important;
    }
  }

  @media screen and (max-width: 575px) {
    .menu.menu-sub.menu-sub-dropdown {
      transform: translateX(30px) !important;
    }

    .menu-item .menu-link i {
      margin-left: 0.3rem !important;
    }

    .menu-content .btn i {
      margin-left: 0.3rem !important;
    }
  }
}

// ===== PREVENT OVERFLOW =====

// Ensure dropdown doesn't go off-screen
.menu.menu-sub.menu-sub-dropdown {
  // Prevent horizontal overflow
  @media screen and (max-width: 480px) {
    transform: translateX(-40px) !important;
    min-width: 120px !important;
    max-width: 140px !important;
  }

  @media screen and (max-width: 360px) {
    transform: translateX(-50px) !important;
    min-width: 110px !important;
    max-width: 130px !important;
  }
}

// RTL overflow prevention
:host-context(html[lang="ar"]) {
  .menu.menu-sub.menu-sub-dropdown {
    @media screen and (max-width: 480px) {
      transform: translateX(40px) !important;
    }

    @media screen and (max-width: 360px) {
      transform: translateX(50px) !important;
    }
  }
}

<form (ngSubmit)="onFilter()" #filterForm="ngForm" class="px-7 py-5" [ngClass]="{'show': isDropdownOpen}">
  <div class="fs-5 text-gray-900 fw-bolder">{{ getTranslatedText('FILTER_OPTIONS') }}</div>
  <div class="separator border-gray-200 my-4"></div>

  <div class="mb-10">
    <label class="form-label fw-bold">{{ getTranslatedText('SPECIALIZATION_SCOPE') }}:</label>
    <div>
      <select class="form-select form-select-solid" name="specializationScope" [(ngModel)]="selectedSpecializationScope"
        data-kt-select2="true" [attr.data-placeholder]="getTranslatedText('SELECT_OPTION')" data-allow-clear="true">
        <option [ngValue]="null">{{ getTranslatedText('SELECT') }}</option>
        <option *ngFor="let option of specializationScope" [value]="option.value">
          {{ getTranslatedOptionText(option.key) }}
        </option>
      </select>
    </div>
  </div>

  <div class="d-flex justify-content-end">
    <button type="reset" class="btn btn-sm btn-light btn-active-light-dark-blue me-2" (click)="onReset()">
      {{ getTranslatedText('RESET') }}
    </button>
    <button type="submit" class="btn btn-sm btn-dark-blue">
      {{ getTranslatedText('APPLY') }}
    </button>
  </div>
</form>
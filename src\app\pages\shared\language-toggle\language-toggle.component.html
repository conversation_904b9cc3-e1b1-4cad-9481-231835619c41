<div class="language-toggle" (document:click)="onDocumentClick($event)">
  <button class="btn btn-outline-light language-btn" type="button" (click)="toggleDropdown()"
    [attr.aria-expanded]="showDropdown">
    <i class="fas fa-globe me-2"></i>
    <span class="language-text">{{ getCurrentLanguageLabel() }}</span>
    <i class="fas fa-chevron-down ms-2" [class.rotated]="showDropdown"></i>
  </button>

  <!-- Language Dropdown - نفس الديزاين الأصلي -->
  <div class="language-dropdown" [class.show]="showDropdown">
    <div class="dropdown-item language-option" [class.active]="currentLanguage === 'en'" (click)="selectLanguage('en')">
      <i class="fas fa-check me-2 text-success" *ngIf="currentLanguage === 'en'"></i>
      <i class="fas fa-language me-2 text-primary" *ngIf="currentLanguage !== 'en'"></i>
      <span>English</span>
    </div>
    <div class="dropdown-item language-option" [class.active]="currentLanguage === 'ar'" (click)="selectLanguage('ar')">
      <i class="fas fa-check me-2 text-success" *ngIf="currentLanguage === 'ar'"></i>
      <i class="fas fa-language me-2 text-primary" *ngIf="currentLanguage !== 'ar'"></i>
      <span>العربية</span>
    </div>
  </div>
</div>
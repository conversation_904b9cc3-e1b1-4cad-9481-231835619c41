// RTL Support for Request History
.rtl-history-item {
  direction: rtl;
  flex-direction: row-reverse;

  .rtl-history-content {
    text-align: right;

    .rtl-description {
      font-family: 'Markazi Text', sans-serif;
      text-align: right;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .rtl-date {
      font-family: 'Markazi Text', sans-serif;
      text-align: right;
      font-size: 0.9rem;
    }
  }

  .rtl-badge {
    font-family: 'Markazi Text', sans-serif;
  }
}

// Loading and Empty States
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.text-center.py-10 {
  padding: 3rem 1rem;

  i {
    color: #a1a5b7;
    margin-bottom: 1rem;
  }

  h5 {
    color: #7e8299;
    font-size: 1.1rem;
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  .d-flex {
    direction: rtl;

    span {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  // Adjust layout when icons are hidden in Arabic
  .d-flex.align-items-center.mb-10 {
    .flex-grow-1 {
      margin-right: 0 !important;
      margin-left: 0 !important;
      padding-right: 1rem !important;
    }

    .rtl-history-content {
      text-align: right !important;

      .rtl-description {
        font-family: 'Markazi Text', sans-serif !important;
        text-align: right !important;
        font-size: 1.1rem !important;
      }

      .rtl-date {
        font-family: 'Markazi Text', sans-serif !important;
        text-align: right !important;
        font-size: 0.9rem !important;
      }
    }

    .rtl-badge {
      font-family: 'Markazi Text', sans-serif !important;
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }
  }
}

// ===== RESPONSIVE DESIGN FOR HISTORY =====

// Medium screens (768px - 991px)
@media screen and (min-width: 768px) and (max-width: 991px) {
  .d-flex.align-items-center.mb-10 {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 1rem !important;
    border: 1px solid #e4e6ef !important;
    border-radius: 0.5rem !important;
    margin-bottom: 1rem !important;

    .bullet {
      display: none !important;
    }

    .flex-grow-1 {
      width: 100% !important;
      margin-bottom: 0.75rem !important;

      .text-gray-800 {
        font-size: 1rem !important;
        margin-bottom: 0.5rem !important;
      }

      .text-muted {
        font-size: 0.85rem !important;
      }
    }

    .badge {
      align-self: flex-end !important;
      font-size: 0.75rem !important;
      padding: 0.4rem 0.6rem !important;
    }
  }
}

// Small screens (576px - 767px)
@media screen and (min-width: 576px) and (max-width: 767px) {
  .d-flex.align-items-center.mb-10 {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 0.875rem !important;
    border: 1px solid #e4e6ef !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.875rem !important;

    .bullet {
      display: none !important;
    }

    .flex-grow-1 {
      width: 100% !important;
      margin-bottom: 0.6rem !important;

      .text-gray-800 {
        font-size: 0.95rem !important;
        margin-bottom: 0.4rem !important;
      }

      .text-muted {
        font-size: 0.8rem !important;
      }
    }

    .badge {
      align-self: flex-end !important;
      font-size: 0.7rem !important;
      padding: 0.35rem 0.5rem !important;
    }
  }
}

// Extra small screens (≤575px)
@media screen and (max-width: 575px) {
  .d-flex.align-items-center.mb-10 {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 0.75rem !important;
    border: 1px solid #e4e6ef !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.75rem !important;

    .bullet {
      display: none !important;
    }

    .flex-grow-1 {
      width: 100% !important;
      margin-bottom: 0.5rem !important;

      .text-gray-800 {
        font-size: 0.9rem !important;
        margin-bottom: 0.3rem !important;
      }

      .text-muted {
        font-size: 0.75rem !important;
      }
    }

    .badge {
      align-self: flex-end !important;
      font-size: 0.65rem !important;
      padding: 0.3rem 0.45rem !important;
    }
  }
}

// RTL responsive adjustments
:host-context(html[lang="ar"]) {
  @media screen and (max-width: 991px) {
    .d-flex.align-items-center.mb-10 {
      .flex-grow-1 {
        .text-gray-800 {
          text-align: right !important;
          font-family: 'Markazi Text', sans-serif !important;
        }

        .text-muted {
          text-align: right !important;
          font-family: 'Markazi Text', sans-serif !important;
        }
      }

      .badge {
        align-self: flex-start !important;
        font-family: 'Markazi Text', sans-serif !important;
      }
    }
  }
}
